{% extends 'base.html' %}

{% block title %}Host Dashboard - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">🏨 Welcome back, {{ user.get_full_name|default:user.username }}!</h2>
                            <p class="mb-0 opacity-75">Manage your properties and bookings from your host dashboard</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="h4 mb-0">{{ stats.total_properties|default:0 }} Properties</div>
                            <small class="opacity-75">{{ stats.active_properties|default:0 }} Active</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-primary mb-2">🏨</div>
                    <h3 class="mb-1">{{ stats.total_properties|default:0 }}</h3>
                    <p class="text-muted mb-0">Total Properties</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-success mb-2">📅</div>
                    <h3 class="mb-1">{{ stats.total_bookings|default:0 }}</h3>
                    <p class="text-muted mb-0">Total Bookings</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-warning mb-2">⏳</div>
                    <h3 class="mb-1">{{ stats.pending_bookings|default:0 }}</h3>
                    <p class="text-muted mb-0">Pending Requests</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center h-100">
                <div class="card-body">
                    <div class="display-6 text-info mb-2">💰</div>
                    <h3 class="mb-1">{{ stats.total_revenue|floatformat:0|default:0 }}</h3>
                    <p class="text-muted mb-0">Total Revenue (IRR)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">🚀 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'accommodations:create' %}" class="btn btn-primary">
                            ➕ Add New Property
                        </a>
                        <a href="{% url 'accommodations:host_accommodations' %}" class="btn btn-outline-primary">
                            🏨 Manage Properties
                        </a>
                        <a href="{% url 'bookings:host_bookings' %}" class="btn btn-outline-success">
                            📅 Manage Bookings
                        </a>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                            👤 Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Bookings -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">📋 Recent Bookings</h5>
                    <a href="{% url 'bookings:host_bookings' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Guest</th>
                                        <th>Property</th>
                                        <th>Check-in</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in recent_bookings %}
                                    <tr>
                                        <td>
                                            <strong>{{ booking.guest.get_full_name|default:booking.guest.username }}</strong><br>
                                            <small class="text-muted">{{ booking.number_of_guests }} guest{{ booking.number_of_guests|pluralize }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ booking.accommodation.name }}</strong><br>
                                            <small class="text-muted">{{ booking.accommodation.city|title }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ booking.check_in_date|date:"M d" }}</strong><br>
                                            <small class="text-muted">{{ booking.duration_nights }} night{{ booking.duration_nights|pluralize }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'completed' %}primary{% else %}danger{% endif %}">
                                                {{ booking.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'bookings:detail' booking.id %}" class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <div class="mb-3">📅</div>
                            <p class="text-muted">No recent bookings</p>
                            <a href="{% url 'accommodations:create' %}" class="btn btn-primary btn-sm">
                                Add Your First Property
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tips and Resources -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">💡 Host Tips</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">📸</div>
                            <h6>Add Quality Photos</h6>
                            <p class="small text-muted">High-quality photos increase booking rates</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">⚡</div>
                            <h6>Quick Response</h6>
                            <p class="small text-muted">Respond to requests within 24 hours</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">🌟</div>
                            <h6>Guest Experience</h6>
                            <p class="small text-muted">Provide local recommendations</p>
                        </div>
                        <div class="col-md-3 text-center mb-3">
                            <div class="mb-2">📝</div>
                            <h6>Detailed Descriptions</h6>
                            <p class="small text-muted">Clear descriptions reduce questions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.15s ease-in-out;
}

.display-6 {
    font-size: 2rem;
}
</style>
{% endblock %}
