{% extends 'base.html' %}
{% load static %}

{% block title %}Accommodation Moderation - NomadPersia Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">🛡️ Admin Panel</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'dashboard:admin' %}" class="list-group-item list-group-item-action">
                        📊 Dashboard
                    </a>
                    <a href="{% url 'dashboard:admin_accommodations' %}" class="list-group-item list-group-item-action active">
                        🏠 Accommodations
                    </a>
                    <a href="{% url 'dashboard:admin_bookings' %}" class="list-group-item list-group-item-action">
                        📅 Bookings
                    </a>
                    <a href="{% url 'dashboard:admin_users' %}" class="list-group-item list-group-item-action">
                        👥 Users
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>🏠 Accommodation Moderation</h2>
                    <p class="text-muted">Review and manage accommodation listings</p>
                </div>
            </div>

            <!-- Status Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <form method="get" class="d-flex gap-2">
                                <select name="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending Review</option>
                                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                                    <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>Rejected</option>
                                </select>
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                            </form>
                        </div>
                        <div class="col-md-6 text-end">
                            {% for status in status_counts %}
                                <span class="badge bg-{% if status.status == 'active' %}success{% elif status.status == 'pending' %}warning{% elif status.status == 'rejected' %}danger{% else %}secondary{% endif %}">
                                    {{ status.count }} {{ status.status|title }}
                                </span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accommodations List -->
            {% if accommodations %}
                <div class="row">
                    {% for accommodation in accommodations %}
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">{{ accommodation.name }}</h6>
                                <span class="badge bg-{% if accommodation.status == 'active' %}success{% elif accommodation.status == 'pending' %}warning{% elif accommodation.status == 'rejected' %}danger{% else %}secondary{% endif %}">
                                    {{ accommodation.get_status_display }}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Host</small>
                                        <div>{{ accommodation.host.get_full_name|default:accommodation.host.username }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Type</small>
                                        <div>{{ accommodation.get_accommodation_type_display }}</div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Location</small>
                                        <div>{{ accommodation.get_city_display }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Price</small>
                                        <div class="text-primary">{{ accommodation.price_per_night|floatformat:0 }} IRR</div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Capacity</small>
                                        <div>👥 {{ accommodation.standard_capacity }}{% if accommodation.max_capacity and accommodation.max_capacity != accommodation.standard_capacity %}-{{ accommodation.max_capacity }}{% endif %} guests</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Layout</small>
                                        <div>🛏️ {{ accommodation.bedrooms }} bed • 🚿 {{ accommodation.bathrooms }} bath</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Description</small>
                                    <div class="small">{{ accommodation.description|truncatewords:20 }}</div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Created</small>
                                    <div class="small">{{ accommodation.created_at|date:"M d, Y H:i" }}</div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="{% url 'accommodations:detail' accommodation.pk %}" class="btn btn-outline-primary btn-sm">
                                        👁️ View
                                    </a>
                                    {% if accommodation.status == 'pending' %}
                                        <form method="post" action="{% url 'dashboard:admin_accommodation_action' accommodation.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="approve">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                ✅ Approve
                                            </button>
                                        </form>
                                        <form method="post" action="{% url 'dashboard:admin_accommodation_action' accommodation.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="reject">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                ❌ Reject
                                            </button>
                                        </form>
                                    {% elif accommodation.status == 'active' %}
                                        <form method="post" action="{% url 'dashboard:admin_accommodation_action' accommodation.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="deactivate">
                                            <button type="submit" class="btn btn-warning btn-sm">
                                                ⏸️ Deactivate
                                            </button>
                                        </form>
                                    {% elif accommodation.status == 'inactive' %}
                                        <form method="post" action="{% url 'dashboard:admin_accommodation_action' accommodation.pk %}" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="activate">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                ▶️ Activate
                                            </button>
                                        </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Accommodations pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">First</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Next</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">Last</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="card">
                    <div class="card-body text-center py-5">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🏠</div>
                        <h4>No Accommodations Found</h4>
                        <p class="text-muted">
                            {% if request.GET.status %}
                                No accommodations with status "{{ request.GET.status }}" found.
                            {% else %}
                                No accommodations have been submitted yet.
                            {% endif %}
                        </p>
                        <a href="{% url 'dashboard:admin' %}" class="btn btn-primary">
                            ← Back to Dashboard
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
