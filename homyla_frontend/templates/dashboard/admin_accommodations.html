{% extends 'base.html' %}

{% block title %}Property Management - NomadPersia Admin{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>🏨 Property Management</h2>
                    <p class="text-muted">Moderate and manage platform accommodations</p>
                </div>
                <div>
                    <a href="{% url 'dashboard:admin' %}" class="btn btn-outline-secondary">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Accommodations List -->
            {% if accommodations %}
                <div class="row">
                    {% for accommodation in accommodations %}
                    <div class="col-lg-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ accommodation.name }}</h5>
                                <span class="badge bg-{% if accommodation.status == 'active' %}success{% elif accommodation.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                    {{ accommodation.get_status_display }}
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Host</small>
                                        <div class="fw-bold">{{ accommodation.host.get_full_name|default:accommodation.host.username }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Location</small>
                                        <div>{{ accommodation.get_city_display }}{% if accommodation.town %}, {{ accommodation.town }}{% endif %}</div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Type</small>
                                        <div>{{ accommodation.get_accommodation_type_display }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Price</small>
                                        <div class="text-primary">{{ accommodation.price_per_night|floatformat:0 }} IRR</div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted">Contact</small>
                                        <div class="small">{{ accommodation.reservation_phone }}</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Check-out</small>
                                        <div class="small">{{ accommodation.get_check_out_time_display }}</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Key Policies</small>
                                    <div class="small">
                                        {% if not accommodation.smoking_allowed %}🚭 No Smoking {% endif %}
                                        {% if accommodation.pets_not_allowed %}🐕 No Pets {% endif %}
                                        {% if accommodation.events_not_allowed %}🎉 No Events {% endif %}
                                        {% if accommodation.same_gender_groups_only %}👥 Same Gender Only {% endif %}
                                        {% if accommodation.national_id_required %}🆔 ID Required {% endif %}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">Created</small>
                                    <div class="small">{{ accommodation.created_at|date:"M d, Y g:i A" }}</div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex gap-2">
                                    <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-outline-primary btn-sm">
                                        👁️ View
                                    </a>
                                    {% if accommodation.status == 'pending' %}
                                    <button class="btn btn-outline-success btn-sm" onclick="approveProperty({{ accommodation.id }})">
                                        ✅ Approve
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="rejectProperty({{ accommodation.id }})">
                                        ❌ Reject
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="display-1">🏨</i>
                    </div>
                    <h3>No Properties Found</h3>
                    <p class="text-muted">No accommodations found in the system.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function approveProperty(propertyId) {
    if (confirm('Approve this property?')) {
        alert('Property approval functionality would be implemented here');
    }
}

function rejectProperty(propertyId) {
    if (confirm('Reject this property?')) {
        alert('Property rejection functionality would be implemented here');
    }
}
</script>

<style>
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.8em;
}
</style>
{% endblock %}
