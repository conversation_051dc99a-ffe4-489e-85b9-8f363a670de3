{% extends 'base.html' %}

{% block title %}Book {{ accommodation.name }} - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- Booking Form -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h3 class="mb-0">📅 Book Your Stay</h3>
                    <small class="text-muted">Complete your booking for {{ accommodation.name }}</small>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the errors below:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post" id="bookingForm">
                        {% csrf_token %}
                        
                        <!-- Stay Details -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📅 Stay Details</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.check_in_date.id_for_label }}" class="form-label">Check-in Date *</label>
                                    <input type="date" class="form-control" name="check_in_date" 
                                           id="{{ form.check_in_date.id_for_label }}" 
                                           value="{{ form.check_in_date.value|default:'' }}" required
                                           min="{{ today|date:'Y-m-d' }}"
                                           onchange="calculateTotal()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.check_out_date.id_for_label }}" class="form-label">Check-out Date *</label>
                                    <input type="date" class="form-control" name="check_out_date" 
                                           id="{{ form.check_out_date.id_for_label }}" 
                                           value="{{ form.check_out_date.value|default:'' }}" required
                                           min="{{ today|date:'Y-m-d' }}"
                                           onchange="calculateTotal()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="checkInTime" class="form-label">Check-in Time *</label>
                                    <select class="form-select" id="checkInTime" name="check_in_time" required>
                                        <option value="12:00">12:00 PM</option>
                                        <option value="14:00" selected>2:00 PM</option>
                                        <option value="16:00">4:00 PM</option>
                                    </select>
                                    <small class="text-muted">Available check-in times</small>
                                </div>
                            </div>
                        </div>

                        <!-- Guest Capacity Selection -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">👥 Guest Information</h5>

                            <!-- Guest Summary Display -->
                            <div class="alert alert-info mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Total Guests: <span id="totalGuestDisplay">1</span></strong>
                                        <div class="small text-muted" id="guestBreakdownDisplay">1 adult</div>
                                    </div>
                                    <div class="small">
                                        Max capacity: {{ accommodation.max_capacity }} guests
                                    </div>
                                </div>
                            </div>

                            <!-- Detailed Guest Selection -->
                            <div class="row">
                                <!-- Adults -->
                                <div class="col-md-6 mb-3">
                                    <div class="guest-category border rounded p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold">Adults</div>
                                                <small class="text-muted">18+ years</small>
                                            </div>
                                            <div class="guest-counter">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('adults', -1)">-</button>
                                                <span class="mx-2 fw-bold" id="adultsCount">1</span>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('adults', 1)">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Teens -->
                                <div class="col-md-6 mb-3">
                                    <div class="guest-category border rounded p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold">Teens</div>
                                                <small class="text-muted">13-17 years</small>
                                            </div>
                                            <div class="guest-counter">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('teens', -1)">-</button>
                                                <span class="mx-2 fw-bold" id="teensCount">0</span>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('teens', 1)">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Children -->
                                <div class="col-md-6 mb-3">
                                    <div class="guest-category border rounded p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold">Children</div>
                                                <small class="text-muted">3-12 years</small>
                                            </div>
                                            <div class="guest-counter">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('children', -1)">-</button>
                                                <span class="mx-2 fw-bold" id="childrenCount">0</span>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('children', 1)">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Infants -->
                                <div class="col-md-6 mb-3">
                                    <div class="guest-category border rounded p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-bold">Infants</div>
                                                <small class="text-muted">0-2 years</small>
                                            </div>
                                            <div class="guest-counter">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('infants', -1)">-</button>
                                                <span class="mx-2 fw-bold" id="infantsCount">0</span>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('infants', 1)">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Hidden inputs for guest counts -->
                            <input type="hidden" name="adults" id="adultsInput" value="1">
                            <input type="hidden" name="teens" id="teensInput" value="0">
                            <input type="hidden" name="children" id="childrenInput" value="0">
                            <input type="hidden" name="infants" id="infantsInput" value="0">
                            <input type="hidden" name="number_of_guests" id="totalGuestsInput" value="1">

                            <!-- Capacity Warning -->
                            <div class="alert alert-warning mt-3 d-none" id="capacityWarning">
                                <strong>⚠️ Capacity Exceeded!</strong>
                                <div>Maximum capacity is {{ accommodation.max_capacity }} guests. Please reduce the number of guests.</div>
                            </div>

                            <!-- Debug Test Button -->
                            <div class="mt-3">
                                <button type="button" class="btn btn-info btn-sm" onclick="testJavaScript()">
                                    🔧 Test JavaScript
                                </button>
                                <button type="button" class="btn btn-warning btn-sm ms-2" onclick="changeGuestCount('adults', 1)">
                                    🔧 Test +1 Adult
                                </button>
                                <small class="text-muted ms-2">Click to verify JavaScript is working</small>
                            </div>

                            <!-- Debug Information Display -->
                            <div class="mt-3 p-3 bg-light rounded">
                                <h6>🔍 Debug Information</h6>
                                <div id="debugInfo">
                                    <div>Base Price: {{ accommodation.price_per_night|floatformat:0 }} IRR</div>
                                    <div>Extra Guest Fee: {{ accommodation.extra_guest_fee|floatformat:0 }} IRR</div>
                                    <div>Standard Capacity: {{ accommodation.standard_capacity }}</div>
                                    <div>Max Capacity: {{ accommodation.max_capacity }}</div>
                                    <div>Pricing Rules: {{ pricing_rules.count }}</div>
                                </div>
                            </div>
                            </div>
                        </div>

                        <!-- Special Requests -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📝 Special Requests</h5>
                            <div class="mb-3">
                                <label for="{{ form.special_requests.id_for_label }}" class="form-label">Special Requests (Optional)</label>
                                <textarea class="form-control" name="special_requests" 
                                          id="{{ form.special_requests.id_for_label }}" 
                                          rows="4"
                                          placeholder="Any special requests or requirements for your stay...">{{ form.special_requests.value|default:'' }}</textarea>
                                <small class="form-text text-muted">Let the host know about any special needs or preferences</small>
                            </div>
                        </div>

                        <!-- Guest Information -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">👤 Guest Information</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" class="form-control" 
                                           value="{{ user.get_full_name|default:user.username }}" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" 
                                           value="{{ user.email }}" readonly>
                                </div>
                                {% if user.profile.phone_number %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Phone Number</label>
                                    <input type="text" class="form-control" 
                                           value="{{ user.profile.phone_number }}" readonly>
                                </div>
                                {% endif %}
                            </div>
                            <small class="text-muted">
                                Need to update your information? 
                                <a href="{% url 'accounts:edit_profile' %}" target="_blank">Edit your profile</a>
                            </small>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" target="_blank">Terms and Conditions</a> and 
                                    <a href="#" target="_blank">Cancellation Policy</a> *
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-secondary">
                                ← Back to Property
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBooking">
                                {% if accommodation.instant_booking %}
                                    ⚡ Book Instantly
                                {% else %}
                                    🎯 Request Booking
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Booking Summary -->
        <div class="col-lg-4">
            <div class="card shadow sticky-top" style="top: 20px;">
                <div class="card-header">
                    <h5 class="mb-0">📋 Booking Summary</h5>
                </div>
                <div class="card-body">
                    <!-- Property Info -->
                    <div class="mb-3">
                        <h6 class="fw-bold">{{ accommodation.name }}</h6>
                        <small class="text-muted">
                            📍 {{ accommodation.get_city_display }}{% if accommodation.town %}, {{ accommodation.town }}{% endif %}<br>
                            🏠 {{ accommodation.get_accommodation_type_display }}
                            {% if accommodation.star_rating %}
                            <br>⭐ {{ accommodation.star_rating }} Star{{ accommodation.star_rating|pluralize }}
                            {% endif %}
                        </small>
                    </div>

                    <hr>

                    <!-- Contact Info -->
                    <div class="mb-3">
                        <h6 class="fw-bold">📞 Contact</h6>
                        <small class="text-muted">
                            Phone: <a href="tel:{{ accommodation.reservation_phone }}" class="text-decoration-none">{{ accommodation.reservation_phone }}</a>
                        </small>
                    </div>

                    <hr>

                    <!-- Key Policies -->
                    <div class="mb-3">
                        <h6 class="fw-bold">🏠 Key Policies</h6>
                        <small class="text-muted">
                            Check-out: {{ accommodation.get_check_out_time_display }}<br>
                            {% if not accommodation.smoking_allowed %}🚭 No Smoking<br>{% endif %}
                            {% if accommodation.pets_not_allowed %}🐕 No Pets<br>{% endif %}
                            {% if accommodation.events_not_allowed %}🎉 No Events<br>{% endif %}
                            {% if accommodation.same_gender_groups_only %}👥 Same Gender Groups Only<br>{% endif %}
                            {% if accommodation.national_id_required %}🆔 National ID Required<br>{% endif %}
                        </small>
                    </div>

                    <hr>

                    <!-- Host Info -->
                    <div class="mb-3">
                        <h6 class="fw-bold">Host</h6>
                        <div class="d-flex align-items-center">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                <span class="small">{{ accommodation.host.first_name.0|default:accommodation.host.username.0|upper }}</span>
                            </div>
                            <div>
                                <small class="fw-bold">{{ accommodation.host.get_full_name|default:accommodation.host.username }}</small>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Pricing Breakdown -->
                    <div class="mb-3">
                        <h6 class="fw-bold">💰 Pricing Breakdown</h6>

                        <!-- Base Pricing -->
                        <div class="pricing-section">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Base price per night:</span>
                                <span>{{ accommodation.price_per_night|floatformat:0 }} IRR</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2" id="nightsRow" style="display: none;">
                                <span id="nightsText">× 0 nights:</span>
                                <span id="subtotalAmount">0 IRR</span>
                            </div>
                        </div>

                        <!-- Extra Fees Section -->
                        <div id="extraFeesSection" style="display: none;">
                            <div class="border-top pt-2 mt-2">
                                <small class="text-muted fw-bold">Additional Fees:</small>
                                <div id="extraGuestFeeRow" class="d-flex justify-content-between mb-1" style="display: none;">
                                    <small>Extra guest fee:</small>
                                    <small id="extraGuestFeeAmount">0 IRR</small>
                                </div>
                                <div id="ageSpecificFeesContainer">
                                    <!-- Age-specific fees will be added here -->
                                </div>
                            </div>
                        </div>

                        <!-- Total Section -->
                        <div class="border-top pt-2 mt-2">
                            <div class="d-flex justify-content-between">
                                <strong>Total Amount:</strong>
                                <strong class="text-primary h5" id="totalAmount">{{ accommodation.price_per_night|floatformat:0 }} IRR</strong>
                            </div>
                        </div>

                        <!-- Pricing Notes -->
                        <div class="mt-2">
                            <small class="text-muted">
                                <div class="d-flex align-items-center mb-1">
                                    <span class="me-2">💡</span>
                                    <span>Prices include accommodation for {{ accommodation.standard_capacity }} guests</span>
                                </div>
                                {% if accommodation.extra_guest_fee > 0 %}
                                <div class="d-flex align-items-center mb-1">
                                    <span class="me-2">👥</span>
                                    <span>Extra guest fee: {{ accommodation.extra_guest_fee|floatformat:0 }} IRR per person (one-time)</span>
                                </div>
                                {% endif %}
                                <div class="d-flex align-items-center">
                                    <span class="me-2">🏠</span>
                                    <span>Maximum capacity: {{ accommodation.max_capacity }} guests</span>
                                </div>
                            </small>
                        </div>
                    </div>

                    <!-- Booking Status -->
                    <div class="alert alert-info">
                        <small>
                            {% if accommodation.instant_booking %}
                                <strong>⚡ Instant Booking Process:</strong><br>
                                1. Complete your booking details<br>
                                2. Instant confirmation<br>
                                3. Payment processing<br>
                                4. Enjoy your stay!
                            {% else %}
                                <strong>📋 Booking Request Process:</strong><br>
                                1. Submit your booking request<br>
                                2. Host reviews and responds<br>
                                3. Confirmation and payment<br>
                                4. Enjoy your stay!
                            {% endif %}
                        </small>
                    </div>

                    <!-- Property Highlights -->
                    {% if accommodation.amenities %}
                    <div class="mb-3">
                        <h6 class="fw-bold">🌟 Amenities</h6>
                        <small class="text-muted">{{ accommodation.amenities|truncatewords:10 }}</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Accommodation data for pricing calculations
const accommodationData = {
    id: {{ accommodation.id }},
    basePrice: {{ accommodation.price_per_night }},
    extraGuestFee: {{ accommodation.extra_guest_fee|default:0 }},
    standardCapacity: {{ accommodation.standard_capacity }},
    maxCapacity: {{ accommodation.max_capacity }},
    pricingCalendar: {{ pricing_calendar_json|safe }},
    capacityRules: {{ capacity_rules_json|safe }}
};

console.log('🔍 DEBUG: Accommodation data loaded:', accommodationData);

// Test function to verify JavaScript is working
function testJavaScript() {
    console.log('🔍 DEBUG: JavaScript test function called');
    alert('JavaScript is working! Guest counts: ' + JSON.stringify(guestCounts));
}

// Make functions globally accessible
window.changeGuestCount = changeGuestCount;
window.testJavaScript = testJavaScript;

// Guest counts
let guestCounts = {
    adults: 1,
    teens: 0,
    children: 0,
    infants: 0
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 DEBUG: Page loaded, initializing...');
    console.log('🔍 DEBUG: accommodationData:', accommodationData);
    console.log('🔍 DEBUG: guestCounts:', guestCounts);

    // Test if elements exist
    console.log('🔍 DEBUG: adultsCount element:', document.getElementById('adultsCount'));
    console.log('🔍 DEBUG: teensCount element:', document.getElementById('teensCount'));

    // Initialize date pickers
    initializeDatePickers();

    // Initialize guest display
    updateGuestDisplay();

    // Calculate initial total
    calculateTotal();

    console.log('🔍 DEBUG: Initialization complete');

    // Add debug info to page
    const debugDiv = document.getElementById('debugInfo');
    if (debugDiv) {
        debugDiv.innerHTML += `
            <div>JavaScript Loaded: ✅</div>
            <div>Guest Counts: ${JSON.stringify(guestCounts)}</div>
            <div>Pricing Calendar Keys: ${Object.keys(accommodationData.pricingCalendar || {}).length}</div>
        `;
    }
});

// Initialize date pickers with event listeners
function initializeDatePickers() {
    const today = new Date().toISOString().split('T')[0];
    const checkInInput = document.getElementById('{{ form.check_in_date.id_for_label }}');
    const checkOutInput = document.getElementById('{{ form.check_out_date.id_for_label }}');

    if (checkInInput) {
        checkInInput.min = today;
        checkInInput.addEventListener('change', function() {
            console.log('🔍 DEBUG: Check-in date changed:', this.value);

            const checkInDate = new Date(this.value);
            const nextDay = new Date(checkInDate);
            nextDay.setDate(nextDay.getDate() + 1);

            if (checkOutInput) {
                checkOutInput.min = nextDay.toISOString().split('T')[0];
                if (checkOutInput.value && new Date(checkOutInput.value) <= checkInDate) {
                    checkOutInput.value = nextDay.toISOString().split('T')[0];
                }
            }

            calculateTotal();
        });
    }

    if (checkOutInput) {
        checkOutInput.min = today;
        checkOutInput.addEventListener('change', function() {
            console.log('🔍 DEBUG: Check-out date changed:', this.value);
            calculateTotal();
        });
    }
}

// Change guest count
function changeGuestCount(category, change) {
    try {
        console.log(`🔍 DEBUG: changeGuestCount called - category: ${category}, change: ${change}`);
        console.log(`🔍 DEBUG: Current guest counts:`, guestCounts);

        if (!guestCounts.hasOwnProperty(category)) {
            console.error(`❌ ERROR: Invalid category: ${category}`);
            return;
        }

        const currentCount = guestCounts[category] || 0;
        const newCount = Math.max(0, currentCount + change);

        console.log(`🔍 DEBUG: ${category} count changing from ${currentCount} to ${newCount}`);

        // Validate capacity
        if (category === 'adults' && newCount === 0) {
            console.log('🔍 DEBUG: Cannot reduce adults to 0');
            alert('At least 1 adult is required for booking');
            return; // At least 1 adult required
        }

        // Check total capacity before making change
        const currentTotal = getTotalGuests();
        const newTotal = currentTotal - currentCount + newCount;

        if (newTotal > accommodationData.maxCapacity) {
            console.log(`🔍 DEBUG: Capacity exceeded: ${newTotal} > ${accommodationData.maxCapacity}`);
            showCapacityWarning();
            alert(`Maximum capacity is ${accommodationData.maxCapacity} guests`);
            return;
        }

        // Update the count
        guestCounts[category] = newCount;

        // Update display elements
        const countElement = document.getElementById(category + 'Count');
        const inputElement = document.getElementById(category + 'Input');

        if (countElement) {
            countElement.textContent = newCount;
            console.log(`🔍 DEBUG: Updated ${category}Count display to ${newCount}`);
        } else {
            console.error(`❌ ERROR: Could not find element ${category}Count`);
        }

        if (inputElement) {
            inputElement.value = newCount;
            console.log(`🔍 DEBUG: Updated ${category}Input value to ${newCount}`);
        } else {
            console.error(`❌ ERROR: Could not find element ${category}Input`);
        }

        updateGuestDisplay();
        calculateTotal();
        hideCapacityWarning();

        console.log(`🔍 DEBUG: Guest counts after update:`, guestCounts);

        // Update debug display
        const debugDiv = document.getElementById('debugInfo');
        if (debugDiv) {
            const guestCountsDiv = debugDiv.querySelector('.guest-counts-debug');
            if (guestCountsDiv) {
                guestCountsDiv.textContent = `Guest Counts: ${JSON.stringify(guestCounts)}`;
            }
        }

    } catch (error) {
        console.error('❌ ERROR in changeGuestCount:', error);
        alert('Error updating guest count: ' + error.message);
    }
}

// Get total guest count
function getTotalGuests() {
    return guestCounts.adults + guestCounts.teens + guestCounts.children + guestCounts.infants;
}

// Update guest display
function updateGuestDisplay() {
    const total = getTotalGuests();
    document.getElementById('totalGuestDisplay').textContent = total;
    document.getElementById('totalGuestsInput').value = total;

    // Update breakdown display
    const breakdown = [];
    if (guestCounts.adults > 0) breakdown.push(`${guestCounts.adults} adult${guestCounts.adults > 1 ? 's' : ''}`);
    if (guestCounts.teens > 0) breakdown.push(`${guestCounts.teens} teen${guestCounts.teens > 1 ? 's' : ''}`);
    if (guestCounts.children > 0) breakdown.push(`${guestCounts.children} child${guestCounts.children > 1 ? 'ren' : ''}`);
    if (guestCounts.infants > 0) breakdown.push(`${guestCounts.infants} infant${guestCounts.infants > 1 ? 's' : ''}`);

    document.getElementById('guestBreakdownDisplay').textContent = breakdown.join(', ');
}

// Show/hide capacity warning
function showCapacityWarning() {
    document.getElementById('capacityWarning').classList.remove('d-none');
}

function hideCapacityWarning() {
    document.getElementById('capacityWarning').classList.add('d-none');
}

// Calculate total price with dynamic pricing
function calculateTotal() {
    try {
        console.log('🔍 DEBUG: calculateTotal() called');

        const checkInInput = document.getElementById('{{ form.check_in_date.id_for_label }}');
        const checkOutInput = document.getElementById('{{ form.check_out_date.id_for_label }}');

        if (!checkInInput || !checkOutInput) {
            console.error('❌ ERROR: Date input elements not found');
            console.error('❌ Looking for elements:', '{{ form.check_in_date.id_for_label }}', '{{ form.check_out_date.id_for_label }}');
            return;
        }

        const checkIn = checkInInput.value;
        const checkOut = checkOutInput.value;

        console.log(`🔍 DEBUG: Dates - Check-in: ${checkIn}, Check-out: ${checkOut}`);

        if (!checkIn || !checkOut) {
            console.log('🔍 DEBUG: Missing dates, skipping calculation');
            return;
        }

    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);

    if (checkOutDate <= checkInDate) {
        console.log('🔍 DEBUG: Invalid date range');
        const nightsRow = document.getElementById('nightsRow');
        const totalAmount = document.getElementById('totalAmount');

        if (nightsRow) nightsRow.style.display = 'none';
        if (totalAmount) totalAmount.textContent = `${accommodationData.basePrice.toLocaleString()} IRR`;
        return;
    }

    const nights = Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24));
    const totalGuests = getTotalGuests();

    console.log(`🔍 DEBUG: Calculation details - Nights: ${nights}, Total guests: ${totalGuests}`);
    console.log(`🔍 DEBUG: Standard capacity: ${accommodationData.standardCapacity}, Max capacity: ${accommodationData.maxCapacity}`);
    console.log(`🔍 DEBUG: Extra guest fee: ${accommodationData.extraGuestFee} IRR`);
    console.log(`🔍 DEBUG: Pricing calendar:`, accommodationData.pricingCalendar);

    // Calculate base accommodation cost with dynamic pricing
    let accommodationCost = 0;
    let priceBreakdown = [];

    for (let i = 0; i < nights; i++) {
        const currentDate = new Date(checkInDate);
        currentDate.setDate(currentDate.getDate() + i);
        const dateStr = currentDate.toISOString().split('T')[0];

        let dailyPrice = accommodationData.basePrice;
        let priceSource = 'base';

        // Check if we have pricing data for this date
        if (accommodationData.pricingCalendar && accommodationData.pricingCalendar[dateStr]) {
            dailyPrice = accommodationData.pricingCalendar[dateStr].price;
            priceSource = 'dynamic';
        }

        accommodationCost += dailyPrice;
        priceBreakdown.push({
            date: dateStr,
            price: dailyPrice,
            source: priceSource
        });

        console.log(`🔍 DEBUG: Date ${dateStr}: ${dailyPrice.toLocaleString()} IRR (${priceSource})`);
    }

    let totalPrice = accommodationCost;

    console.log(`🔍 DEBUG: Base accommodation cost: ${accommodationCost} IRR`);

    // Calculate extra guest fees
    let extraGuestCost = 0;
    if (totalGuests > accommodationData.standardCapacity && accommodationData.extraGuestFee > 0) {
        const extraGuests = totalGuests - accommodationData.standardCapacity;
        extraGuestCost = extraGuests * accommodationData.extraGuestFee;  // One-time fee, not per night
        totalPrice += extraGuestCost;

        console.log(`🔍 DEBUG: Extra guest calculation:`);
        console.log(`🔍 DEBUG:   Total guests: ${totalGuests}`);
        console.log(`🔍 DEBUG:   Standard capacity: ${accommodationData.standardCapacity}`);
        console.log(`🔍 DEBUG:   Extra guests: ${extraGuests}`);
        console.log(`🔍 DEBUG:   Extra guest fee per person: ${accommodationData.extraGuestFee.toLocaleString()} IRR`);
        console.log(`🔍 DEBUG:   Total extra guest fee: ${extraGuestCost.toLocaleString()} IRR (one-time fee)`);
    } else {
        console.log(`🔍 DEBUG: No extra guest fees (${totalGuests} guests <= ${accommodationData.standardCapacity} standard capacity)`);
    }

    console.log(`🔍 DEBUG: Final total price: ${totalPrice} IRR`);

    // Update display elements
    const nightsRow = document.getElementById('nightsRow');
    const nightsText = document.getElementById('nightsText');
    const subtotalAmount = document.getElementById('subtotalAmount');
    const totalAmount = document.getElementById('totalAmount');

    if (nightsRow) {
        nightsRow.style.display = 'flex';
    }

    if (nightsText) {
        nightsText.textContent = `× ${nights} night${nights > 1 ? 's' : ''}:`;
    }

    if (subtotalAmount) {
        subtotalAmount.textContent = `${accommodationCost.toLocaleString()} IRR`;
    }

    // Show/hide extra fees section
    const extraFeesSection = document.getElementById('extraFeesSection');
    const extraGuestFeeRow = document.getElementById('extraGuestFeeRow');
    const extraGuestFeeAmount = document.getElementById('extraGuestFeeAmount');

    if (extraGuestCost > 0) {
        if (extraFeesSection) extraFeesSection.style.display = 'block';
        if (extraGuestFeeRow) extraGuestFeeRow.style.display = 'flex';
        if (extraGuestFeeAmount) extraGuestFeeAmount.textContent = `${extraGuestCost.toLocaleString()} IRR`;
    } else {
        if (extraFeesSection) extraFeesSection.style.display = 'none';
        if (extraGuestFeeRow) extraGuestFeeRow.style.display = 'none';
    }

    if (totalAmount) {
        totalAmount.textContent = `${totalPrice.toLocaleString()} IRR`;
    }

    console.log('🔍 DEBUG: Display updated successfully');

    } catch (error) {
        console.error('❌ ERROR in calculateTotal:', error);
        alert('Error calculating price: ' + error.message);
    }
}

// Set minimum check-out date when check-in changes
document.getElementById('{{ form.check_in_date.id_for_label }}').addEventListener('change', function() {
    const checkInDate = this.value;
    const checkOutInput = document.getElementById('{{ form.check_out_date.id_for_label }}');
    
    if (checkInDate) {
        const minCheckOut = new Date(checkInDate);
        minCheckOut.setDate(minCheckOut.getDate() + 1);
        checkOutInput.min = minCheckOut.toISOString().split('T')[0];
        
        // Clear check-out if it's before the new minimum
        if (checkOutInput.value && checkOutInput.value <= checkInDate) {
            checkOutInput.value = '';
        }
    }
});
</script>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

.sticky-top {
    position: sticky;
}

/* Guest Counter Styles */
.guest-category {
    transition: all 0.2s ease;
}

.guest-category:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.guest-counter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.guest-counter .btn {
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.guest-counter .btn:hover {
    transform: scale(1.1);
}

.guest-counter .btn:active {
    transform: scale(0.95);
}

.guest-counter .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.guest-counter span {
    min-width: 30px;
    text-align: center;
    font-size: 1.1rem;
}

/* Alert styles */
.alert-info {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}

.alert-warning {
    background-color: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
}
</style>
{% endblock %}
