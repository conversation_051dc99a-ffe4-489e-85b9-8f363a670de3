{% extends 'base.html' %}

{% block title %}Host Bookings - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>📋 Manage Bookings</h2>
                    <p class="text-muted">Review and manage bookings for your properties</p>
                </div>
                <div>
                    <a href="{% url 'dashboard:host_dashboard' %}" class="btn btn-outline-primary">
                        📊 Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-light h-100">
                        <div class="card-body text-center">
                            <h3 class="mb-0">{{ total_count }}</h3>
                            <p class="text-muted mb-0">Total Bookings</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning bg-opacity-10 h-100">
                        <div class="card-body text-center">
                            <h3 class="mb-0">{{ pending_count }}</h3>
                            <p class="text-muted mb-0">Pending</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success bg-opacity-10 h-100">
                        <div class="card-body text-center">
                            <h3 class="mb-0">{{ confirmed_count }}</h3>
                            <p class="text-muted mb-0">Confirmed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary bg-opacity-10 h-100">
                        <div class="card-body text-center">
                            <h3 class="mb-0">{{ revenue|floatformat:0 }}</h3>
                            <p class="text-muted mb-0">Revenue (IRR)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Form -->
            {% if form %}
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            {{ form.status.label_tag }}
                            {{ form.status }}
                        </div>
                        <div class="col-md-4">
                            {{ form.date_range.label_tag }}
                            {{ form.date_range }}
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="{% url 'bookings:host_bookings' %}" class="btn btn-outline-secondary ms-2">Reset</a>
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}

            <!-- Bookings List -->
            {% if bookings %}
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Guest</th>
                                    <th>Property</th>
                                    <th>Dates</th>
                                    <th>Status</th>
                                    <th>Price</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in bookings %}
                                <tr>
                                    <td>{{ booking.id }}</td>
                                    <td>
                                        <strong>{{ booking.guest.get_full_name|default:booking.guest.username }}</strong>
                                    </td>
                                    <td>{{ booking.accommodation.name }}</td>
                                    <td>
                                        {{ booking.check_in_date|date:"M d" }} - {{ booking.check_out_date|date:"M d, Y" }}
                                        <div class="small text-muted">{{ booking.duration_nights }} night{{ booking.duration_nights|pluralize }}</div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'completed' %}primary{% else %}danger{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ booking.total_price|floatformat:0 }} IRR</td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="{% url 'bookings:detail' booking.id %}" class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">&laquo; First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <!-- No Bookings -->
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="display-1">📅</i>
                    </div>
                    <h3>No Bookings Found</h3>
                    <p class="text-muted mb-4">
                        You don't have any bookings for your properties yet.
                    </p>
                    <div class="d-flex gap-2 justify-content-center">
                        <a href="{% url 'accommodations:host_accommodations' %}" class="btn btn-primary">
                            Manage Your Properties
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.table th, .table td {
    vertical-align: middle;
}
</style>
{% endblock %}
