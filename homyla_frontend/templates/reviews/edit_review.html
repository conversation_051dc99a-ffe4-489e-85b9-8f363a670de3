{% extends 'base.html' %}
{% load static %}

{% block title %}ویرایش نظر - {{ accommodation.name }}{% endblock %}

{% block extra_css %}
<style>
.rating-input {
    display: flex;
    align-items: center;
    gap: 10px;
}
.star-rating-input {
    display: flex;
    gap: 5px;
}
.star-rating-input .star {
    font-size: 1.5em;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s ease;
}
.star-rating-input .star:hover,
.star-rating-input .star.active {
    color: #ffc107;
}
.review-form-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}
.rating-category {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accommodations:search' %}">Accommodations</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accommodations:detail' accommodation.id %}">{{ accommodation.name }}</a></li>
            <li class="breadcrumb-item active">Edit Review</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <div class="review-form-card">
                <h2 class="mb-4">ویرایش نظر شما</h2>
                
                <form method="post" id="reviewForm">
                    {% csrf_token %}
                    
                    <!-- Overall Rating -->
                    <div class="rating-category">
                        <h5>امتیاز کلی</h5>
                        <div class="rating-input">
                            <div class="star-rating-input" data-rating="overall_rating">
                                {% for i in "12345" %}
                                    <span class="star{% if i|add:0 <= review.overall_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                {% endfor %}
                            </div>
                            <span class="rating-text ms-3"></span>
                        </div>
                        {{ form.overall_rating.as_hidden }}
                    </div>

                    <!-- Category Ratings -->
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Cleanliness -->
                            <div class="rating-category">
                                <h6>نظافت</h6>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="cleanliness_rating">
                                        {% for i in "12345" %}
                                            <span class="star{% if i|add:0 <= review.cleanliness_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.cleanliness_rating.as_hidden }}
                            </div>

                            <!-- Location -->
                            <div class="rating-category">
                                <h6>موقعیت</h6>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="location_rating">
                                        {% for i in "12345" %}
                                            <span class="star{% if i|add:0 <= review.location_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.location_rating.as_hidden }}
                            </div>

                            <!-- Value -->
                            <div class="rating-category">
                                <h6>ارزش</h6>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="value_rating">
                                        {% for i in "12345" %}
                                            <span class="star{% if i|add:0 <= review.value_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.value_rating.as_hidden }}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Communication -->
                            <div class="rating-category">
                                <h6>ارتباط با میزبان</h6>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="communication_rating">
                                        {% for i in "12345" %}
                                            <span class="star{% if i|add:0 <= review.communication_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.communication_rating.as_hidden }}
                            </div>

                            <!-- Check-in -->
                            <div class="rating-category">
                                <h6>تجربه ورود</h6>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="checkin_rating">
                                        {% for i in "12345" %}
                                            <span class="star{% if i|add:0 <= review.checkin_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.checkin_rating.as_hidden }}
                            </div>

                            <!-- Accuracy -->
                            <div class="rating-category">
                                <h6>دقت توضیحات</h6>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="accuracy_rating">
                                        {% for i in "12345" %}
                                            <span class="star{% if i|add:0 <= review.accuracy_rating %} active{% endif %}" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.accuracy_rating.as_hidden }}
                            </div>
                        </div>
                    </div>

                    <!-- Written Review -->
                    <div class="mb-4">
                        <label for="{{ form.review_text.id_for_label }}" class="form-label">
                            <h5>نظر کتبی (اختیاری)</h5>
                        </label>
                        {{ form.review_text }}
                        {% if form.review_text.errors %}
                            <div class="text-danger">
                                {% for error in form.review_text.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-2"></i>انصراف
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>ذخیره تغییرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Accommodation Summary -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ accommodation.name }}</h5>
                    <p class="text-muted">{{ accommodation.get_accommodation_type_display }} در {{ accommodation.get_city_display }}</p>
                    
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-2"></i>
                            شما در حال ویرایش نظر خود هستید. تغییرات بلافاصله اعمال خواهد شد.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ratingInputs = document.querySelectorAll('.star-rating-input');
    let ratings = {
        'overall_rating': {{ review.overall_rating }},
        'cleanliness_rating': {{ review.cleanliness_rating }},
        'location_rating': {{ review.location_rating }},
        'value_rating': {{ review.value_rating }},
        'communication_rating': {{ review.communication_rating }},
        'checkin_rating': {{ review.checkin_rating }},
        'accuracy_rating': {{ review.accuracy_rating }}
    };

    // Rating texts
    const ratingTexts = {
        1: 'ضعیف',
        2: 'متوسط',
        3: 'خوب',
        4: 'عالی',
        5: 'فوق‌العاده'
    };

    ratingInputs.forEach(function(ratingInput) {
        const ratingName = ratingInput.getAttribute('data-rating');
        const stars = ratingInput.querySelectorAll('.star');
        const hiddenInput = document.getElementById(`id_${ratingName}`);
        const ratingText = ratingInput.parentElement.querySelector('.rating-text');

        // Set initial values
        hiddenInput.value = ratings[ratingName];
        if (ratingText) {
            ratingText.textContent = ratingTexts[ratings[ratingName]];
        }

        stars.forEach(function(star, index) {
            star.addEventListener('click', function() {
                const value = parseInt(star.getAttribute('data-value'));
                ratings[ratingName] = value;
                hiddenInput.value = value;

                // Update star display
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });

                // Update rating text
                if (ratingText) {
                    ratingText.textContent = ratingTexts[value];
                }
            });

            star.addEventListener('mouseenter', function() {
                const value = parseInt(star.getAttribute('data-value'));
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });

        ratingInput.addEventListener('mouseleave', function() {
            const currentValue = ratings[ratingName] || 0;
            stars.forEach(function(s, i) {
                if (i < currentValue) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });
});
</script>
{% endblock %}
