{% extends 'base.html' %}

{% block title %}Register - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">🌟 Join NomadPersia</h3>
                    <p class="mb-0 small">Discover Iran's Hidden Gems</p>
                </div>
                <div class="card-body p-4">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the errors below:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- Role Selection -->
                        <div class="mb-3">
                            <label class="form-label">I want to:</label>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="role" value="guest" id="role_guest" checked>
                                        <label class="form-check-label" for="role_guest">
                                            <strong>🧳 Travel</strong><br>
                                            <small class="text-muted">Book accommodations</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="role" value="host" id="role_host">
                                        <label class="form-check-label" for="role_host">
                                            <strong>🏨 Host</strong><br>
                                            <small class="text-muted">List my property</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Personal Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                <input type="text" class="form-control" name="first_name" id="{{ form.first_name.id_for_label }}" 
                                       value="{{ form.first_name.value|default:'' }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="last_name" id="{{ form.last_name.id_for_label }}" 
                                       value="{{ form.last_name.value|default:'' }}" required>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            <input type="text" class="form-control" name="username" id="{{ form.username.id_for_label }}" 
                                   value="{{ form.username.value|default:'' }}" required>
                            <small class="form-text text-muted">Choose a unique username</small>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                            <input type="email" class="form-control" name="email" id="{{ form.email.id_for_label }}" 
                                   value="{{ form.email.value|default:'' }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                            <input type="password" class="form-control" name="password1" id="{{ form.password1.id_for_label }}" required>
                            <small class="form-text text-muted">At least 8 characters with letters and numbers</small>
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" name="password2" id="{{ form.password2.id_for_label }}" required>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> 
                                    and <a href="#" class="text-decoration-none">Privacy Policy</a>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                🚀 Create Account
                            </button>
                        </div>
                    </form>

                    <!-- Login Link -->
                    <div class="text-center mt-4">
                        <p class="text-muted">
                            Already have an account? 
                            <a href="{% url 'accounts:login' %}" class="text-decoration-none">Sign In</a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Benefits Section -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6 class="text-center mb-3">Why Join NomadPersia?</h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="mb-2">🏛️</div>
                            <small>Authentic Properties</small>
                        </div>
                        <div class="col-4">
                            <div class="mb-2">🤝</div>
                            <small>Local Hosts</small>
                        </div>
                        <div class="col-4">
                            <div class="mb-2">🛡️</div>
                            <small>Secure Booking</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.form-check-label {
    cursor: pointer;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}
</style>
{% endblock %}
