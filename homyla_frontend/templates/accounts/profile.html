{% extends 'base.html' %}

{% block title %}User Account - NomadPersia{% endblock %}

{% block extra_css %}
<style>
.profile-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.profile-image-container {
    position: relative;
    display: inline-block;
}

.profile-image-placeholder {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    margin: 0 auto;
}

.profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.verification-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    border: 2px solid white;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-complete { background-color: #28a745; }
.status-pending { background-color: #ffc107; }
.status-incomplete { background-color: #dc3545; }

.form-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: white;
}

.form-section h5 {
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.file-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    margin-top: 10px;
}

.bank-notice {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 12px;
    margin-top: 10px;
    font-size: 0.9rem;
    color: #1976d2;
}

.date-inputs {
    display: flex;
    gap: 10px;
}

.date-inputs .form-select {
    flex: 1;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header -->
            <div class="profile-section text-center">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="profile-image-container">
                            {% if profile.profile_image %}
                                <img src="{{ profile.profile_image.url }}" alt="Profile Picture" class="profile-image">
                            {% else %}
                                <div class="profile-image-placeholder">
                                    👤
                                </div>
                            {% endif %}
                            <div class="verification-badge {% if verification_status == 'verified' %}bg-success{% elif verification_status == 'pending' %}bg-warning{% else %}bg-secondary{% endif %}">
                                {% if verification_status == 'verified' %}✓{% elif verification_status == 'pending' %}⏳{% else %}?{% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9 text-start">
                        <h2>👤 User Account</h2>
                        <h4>{{ user.get_full_name|default:user.username }}</h4>
                        <span class="badge bg-primary fs-6">{{ profile.get_role_display }}</span>
                        <span class="badge {% if verification_status == 'verified' %}bg-success{% elif verification_status == 'pending' %}bg-warning{% else %}bg-secondary{% endif %} fs-6 ms-2">
                            {% if verification_status == 'verified' %}
                                Verified Account
                            {% elif verification_status == 'pending' %}
                                Verification Pending
                            {% else %}
                                Verification Incomplete
                            {% endif %}
                        </span>
                        
                        <!-- Status Indicators -->
                        <div class="mt-3">
                            <div class="d-flex align-items-center mb-2">
                                <span class="status-indicator status-{{ profile_complete|yesno:'complete,incomplete' }}"></span>
                                <span>Profile Information {{ profile_complete|yesno:'Complete,Incomplete' }}</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="status-indicator status-{{ bank_info_complete|yesno:'complete,incomplete' }}"></span>
                                <span>Bank Information {{ bank_info_complete|yesno:'Complete,Incomplete' }}</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="status-indicator status-{{ identity_verified|yesno:'complete,incomplete' }}"></span>
                                <span>Identity Verification {{ identity_verified|yesno:'Complete,Incomplete' }}</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="status-indicator status-{{ ownership_complete|yesno:'complete,incomplete' }}"></span>
                                <span>Ownership Documentation {{ ownership_complete|yesno:'Complete,Incomplete' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Update Form -->
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- Profile Image Section -->
                <div class="form-section">
                    <h5>📸 Profile Image Update</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="{{ form.profile_image.id_for_label }}" class="form-label">Upload/Change Profile Picture</label>
                            {{ form.profile_image }}
                            {% if form.profile_image.errors %}
                                <div class="text-danger small">{{ form.profile_image.errors }}</div>
                            {% endif %}
                            {% if profile.profile_image %}
                                <div class="mt-2">
                                    <img src="{{ profile.profile_image.url }}" alt="Current Profile" class="file-preview">
                                    <div class="small text-muted">Current profile picture</div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- User Information Section -->
                <div class="form-section">
                    <h5>👤 User Information</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name *</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-danger small">{{ form.first_name.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name *</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-danger small">{{ form.last_name.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email Address *</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small">{{ form.email.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.national_id.id_for_label }}" class="form-label">National ID Code</label>
                            {{ form.national_id }}
                            {% if form.national_id.errors %}
                                <div class="text-danger small">{{ form.national_id.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">Mobile Number</label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger small">{{ form.phone_number.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">Emergency Contact Number</label>
                            {{ form.emergency_contact }}
                            {% if form.emergency_contact.errors %}
                                <div class="text-danger small">{{ form.emergency_contact.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-8 mb-3">
                            <label class="form-label">Date of Birth</label>
                            <div class="date-inputs">
                                <div>
                                    <label class="form-label small">Day</label>
                                    {{ form.birth_day }}
                                </div>
                                <div>
                                    <label class="form-label small">Month</label>
                                    {{ form.birth_month }}
                                </div>
                                <div>
                                    <label class="form-label small">Year</label>
                                    {{ form.birth_year }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.gender.id_for_label }}" class="form-label">Gender (Optional)</label>
                            {{ form.gender }}
                            {% if form.gender.errors %}
                                <div class="text-danger small">{{ form.gender.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Bank Account Info Section -->
                <div class="form-section">
                    <h5>🏦 Bank Account Info</h5>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="{{ form.iban_number.id_for_label }}" class="form-label">IBAN (Sheba) Number</label>
                            {{ form.iban_number }}
                            {% if form.iban_number.errors %}
                                <div class="text-danger small">{{ form.iban_number.errors }}</div>
                            {% endif %}
                            <div class="bank-notice">
                                <strong>📝 Note:</strong> Changes will take effect after 24 hours if submitted before 23:30.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Identity Verification Section -->
                <div class="form-section">
                    <h5>🆔 Identity Verification Section</h5>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="{{ form.identity_document.id_for_label }}" class="form-label">Upload National ID Card or Passport</label>
                            {{ form.identity_document }}
                            {% if form.identity_document.errors %}
                                <div class="text-danger small">{{ form.identity_document.errors }}</div>
                            {% endif %}
                            <div class="form-text">Accepted formats: JPG, PNG, PDF (max 5MB)</div>
                            {% if profile.identity_document %}
                                <div class="mt-2">
                                    {% if profile.identity_document.url|slice:"-4:" == ".pdf" %}
                                        <div class="alert alert-info">
                                            📄 PDF Document: <a href="{{ profile.identity_document.url }}" target="_blank">View Document</a>
                                        </div>
                                    {% else %}
                                        <img src="{{ profile.identity_document.url }}" alt="Identity Document" class="file-preview">
                                    {% endif %}
                                    <div class="small text-muted">Current identity document</div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Ownership Documentation Section -->
                <div class="form-section">
                    <h5>🏠 Ownership Documentation Flow</h5>

                    <!-- Ownership Type Selection -->
                    <div class="row">
                        <div class="col-md-8 mb-4">
                            <label for="{{ form.ownership_type.id_for_label }}" class="form-label">
                                <strong>Ownership Type Selection</strong>
                            </label>
                            {{ form.ownership_type }}
                            {% if form.ownership_type.errors %}
                                <div class="text-danger small">{{ form.ownership_type.errors }}</div>
                            {% endif %}
                            <div class="form-text">Select your relationship to the property</div>
                        </div>
                    </div>

                    <!-- Conditional Upload Fields -->
                    <div id="ownership-documents" style="display: none;">
                        <div class="alert alert-info">
                            <strong>📋 Required Documents:</strong>
                            <div id="required-docs-list"></div>
                        </div>

                        <!-- User's National ID (always required for hosts) -->
                        <div class="row mb-3" id="user-national-id-section">
                            <div class="col-md-8">
                                <label for="{{ form.user_national_id.id_for_label }}" class="form-label">
                                    Your National ID Card <span class="text-danger">*</span>
                                </label>
                                {{ form.user_national_id }}
                                {% if form.user_national_id.errors %}
                                    <div class="text-danger small">{{ form.user_national_id.errors }}</div>
                                {% endif %}
                                <div class="form-text">Upload image of your national ID card (JPG, PNG, PDF)</div>
                                {% if profile.user_national_id %}
                                    <div class="mt-2">
                                        {% if profile.user_national_id.url|slice:"-4:" == ".pdf" %}
                                            <div class="alert alert-success">
                                                📄 PDF Document: <a href="{{ profile.user_national_id.url }}" target="_blank">View Document</a>
                                            </div>
                                        {% else %}
                                            <img src="{{ profile.user_national_id.url }}" alt="Your National ID" class="file-preview">
                                        {% endif %}
                                        <div class="small text-muted">Current document</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Owner's National ID (for tenants) -->
                        <div class="row mb-3" id="owner-national-id-section" style="display: none;">
                            <div class="col-md-8">
                                <label for="{{ form.owner_national_id.id_for_label }}" class="form-label">
                                    Property Owner's National ID <span class="text-danger">*</span>
                                </label>
                                {{ form.owner_national_id }}
                                {% if form.owner_national_id.errors %}
                                    <div class="text-danger small">{{ form.owner_national_id.errors }}</div>
                                {% endif %}
                                <div class="form-text">Upload image of property owner's national ID card</div>
                                {% if profile.owner_national_id %}
                                    <div class="mt-2">
                                        {% if profile.owner_national_id.url|slice:"-4:" == ".pdf" %}
                                            <div class="alert alert-success">
                                                📄 PDF Document: <a href="{{ profile.owner_national_id.url }}" target="_blank">View Document</a>
                                            </div>
                                        {% else %}
                                            <img src="{{ profile.owner_national_id.url }}" alt="Owner's National ID" class="file-preview">
                                        {% endif %}
                                        <div class="small text-muted">Current document</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Property Deed -->
                        <div class="row mb-3" id="property-deed-section" style="display: none;">
                            <div class="col-md-8">
                                <label for="{{ form.property_deed.id_for_label }}" class="form-label">
                                    Property Deed <span class="text-danger">*</span>
                                </label>
                                {{ form.property_deed }}
                                {% if form.property_deed.errors %}
                                    <div class="text-danger small">{{ form.property_deed.errors }}</div>
                                {% endif %}
                                <div class="form-text">Upload property deed document (JPG, PNG, PDF)</div>
                                {% if profile.property_deed %}
                                    <div class="mt-2">
                                        {% if profile.property_deed.url|slice:"-4:" == ".pdf" %}
                                            <div class="alert alert-success">
                                                📄 PDF Document: <a href="{{ profile.property_deed.url }}" target="_blank">View Document</a>
                                            </div>
                                        {% else %}
                                            <img src="{{ profile.property_deed.url }}" alt="Property Deed" class="file-preview">
                                        {% endif %}
                                        <div class="small text-muted">Current document</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Lease Agreement -->
                        <div class="row mb-3" id="lease-agreement-section" style="display: none;">
                            <div class="col-md-8">
                                <label for="{{ form.lease_agreement.id_for_label }}" class="form-label">
                                    Lease Agreement <span class="text-danger">*</span>
                                </label>
                                {{ form.lease_agreement }}
                                {% if form.lease_agreement.errors %}
                                    <div class="text-danger small">{{ form.lease_agreement.errors }}</div>
                                {% endif %}
                                <div class="form-text">Upload rental/lease agreement document (JPG, PNG, PDF)</div>
                                {% if profile.lease_agreement %}
                                    <div class="mt-2">
                                        {% if profile.lease_agreement.url|slice:"-4:" == ".pdf" %}
                                            <div class="alert alert-success">
                                                📄 PDF Document: <a href="{{ profile.lease_agreement.url }}" target="_blank">View Document</a>
                                            </div>
                                        {% else %}
                                            <img src="{{ profile.lease_agreement.url }}" alt="Lease Agreement" class="file-preview">
                                        {% endif %}
                                        <div class="small text-muted">Current document</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Commitment Letter -->
                        <div class="row mb-3" id="commitment-letter-section" style="display: none;">
                            <div class="col-md-8">
                                <label for="{{ form.commitment_letter.id_for_label }}" class="form-label">
                                    Commitment Letter <span class="text-danger">*</span>
                                </label>
                                {{ form.commitment_letter }}
                                {% if form.commitment_letter.errors %}
                                    <div class="text-danger small">{{ form.commitment_letter.errors }}</div>
                                {% endif %}
                                <div class="form-text">
                                    Upload signed commitment letter (JPG, PNG, PDF)<br>
                                    <a href="/media/templates/commitment_letter_template.pdf" target="_blank" class="btn btn-sm btn-outline-info mt-2">
                                        📄 Download Template
                                    </a>
                                    <small class="d-block text-muted mt-1">Download the template, fill it out by hand, and upload the signed document</small>
                                </div>
                                {% if profile.commitment_letter %}
                                    <div class="mt-2">
                                        {% if profile.commitment_letter.url|slice:"-4:" == ".pdf" %}
                                            <div class="alert alert-success">
                                                📄 PDF Document: <a href="{{ profile.commitment_letter.url }}" target="_blank">View Document</a>
                                            </div>
                                        {% else %}
                                            <img src="{{ profile.commitment_letter.url }}" alt="Commitment Letter" class="file-preview">
                                        {% endif %}
                                        <div class="small text-muted">Current document</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        ✅ Apply Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ownershipSelect = document.getElementById('ownership-type-select');
    const documentsSection = document.getElementById('ownership-documents');
    const requiredDocsList = document.getElementById('required-docs-list');

    // Document sections
    const userIdSection = document.getElementById('user-national-id-section');
    const ownerIdSection = document.getElementById('owner-national-id-section');
    const deedSection = document.getElementById('property-deed-section');
    const leaseSection = document.getElementById('lease-agreement-section');
    const commitmentSection = document.getElementById('commitment-letter-section');

    function updateDocumentRequirements() {
        const ownershipType = ownershipSelect.value;

        // Hide all sections first
        userIdSection.style.display = 'none';
        ownerIdSection.style.display = 'none';
        deedSection.style.display = 'none';
        leaseSection.style.display = 'none';
        commitmentSection.style.display = 'none';

        if (!ownershipType) {
            documentsSection.style.display = 'none';
            return;
        }

        documentsSection.style.display = 'block';
        let requiredDocs = [];

        // Always show user's national ID
        userIdSection.style.display = 'block';
        requiredDocs.push('Your National ID Card');

        if (ownershipType === 'owner_with_deed') {
            // I am the owner and have the deed
            deedSection.style.display = 'block';
            requiredDocs.push('Property Deed');

        } else if (ownershipType === 'owner_without_deed') {
            // I am the owner and do not have the deed
            // Only user's national ID required (already shown)

        } else if (ownershipType === 'tenant_with_lease') {
            // The owner is someone else and I have a lease agreement
            ownerIdSection.style.display = 'block';
            leaseSection.style.display = 'block';
            commitmentSection.style.display = 'block';
            requiredDocs.push('Property Owner\'s National ID', 'Lease Agreement', 'Commitment Letter');

        } else if (ownershipType === 'tenant_with_deed') {
            // The owner is someone else and I have the deed
            ownerIdSection.style.display = 'block';
            deedSection.style.display = 'block';
            commitmentSection.style.display = 'block';
            requiredDocs.push('Property Owner\'s National ID', 'Property Deed', 'Commitment Letter');

        } else if (ownershipType === 'tenant_without_deed') {
            // The owner is someone else and I don't have the deed
            ownerIdSection.style.display = 'block';
            commitmentSection.style.display = 'block';
            requiredDocs.push('Property Owner\'s National ID', 'Commitment Letter');
        }

        // Update required documents list
        requiredDocsList.innerHTML = requiredDocs.map(doc => `<li>${doc}</li>`).join('');
    }

    // Initialize on page load
    updateDocumentRequirements();

    // Update when selection changes
    ownershipSelect.addEventListener('change', updateDocumentRequirements);
});
</script>

{% endblock %}
