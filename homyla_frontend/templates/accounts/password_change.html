{% extends 'base.html' %}

{% block title %}Change Password - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header">
                    <h3 class="mb-0">🔒 Change Password</h3>
                    <small class="text-muted">Update your account password</small>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the errors below:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.old_password.id_for_label }}" class="form-label">Current Password</label>
                            <input type="password" class="form-control" name="old_password" 
                                   id="{{ form.old_password.id_for_label }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.new_password1.id_for_label }}" class="form-label">New Password</label>
                            <input type="password" class="form-control" name="new_password1" 
                                   id="{{ form.new_password1.id_for_label }}" required>
                            <small class="form-text text-muted">At least 8 characters with letters and numbers</small>
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.new_password2.id_for_label }}" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" name="new_password2" 
                                   id="{{ form.new_password2.id_for_label }}" required>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                                ← Back to Profile
                            </a>
                            <button type="submit" class="btn btn-primary">
                                🔒 Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6>🛡️ Password Security Tips</h6>
                    <ul class="small mb-0">
                        <li>Use at least 8 characters</li>
                        <li>Include uppercase and lowercase letters</li>
                        <li>Add numbers and special characters</li>
                        <li>Avoid common words or personal information</li>
                        <li>Don't reuse passwords from other sites</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}
</style>
{% endblock %}
