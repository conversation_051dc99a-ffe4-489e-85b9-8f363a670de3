#!/usr/bin/env python3
"""
Simple path verification script to check if our Django settings
point to the correct frontend template location.
"""

from pathlib import Path

# Simulate the BASE_DIR calculation from settings.py
BASE_DIR = Path(__file__).resolve().parent

print("🔍 Path Verification:")
print(f"   BASE_DIR (homyla_backend): {BASE_DIR}")
print(f"   Parent directory: {BASE_DIR.parent}")

# Calculate the template path as configured in settings.py
template_path = Path(BASE_DIR).parent / "homyla_frontend" / "templates"
print(f"   Calculated template path: {template_path}")
print(f"   Template path exists: {template_path.exists()}")

if template_path.exists():
    print(f"   Template directory contents:")
    for item in sorted(template_path.iterdir()):
        if item.is_dir():
            print(f"     📁 {item.name}/")
        else:
            print(f"     📄 {item.name}")

# Check static files path
static_path = Path(BASE_DIR).parent / "homyla_frontend" / "static"
print(f"\n   Calculated static path: {static_path}")
print(f"   Static path exists: {static_path.exists()}")

# Check if old template directory still exists
old_template_path = BASE_DIR / "templates"
print(f"\n   Old template path: {old_template_path}")
print(f"   Old template path exists: {old_template_path.exists()}")

print("\n✅ Path verification completed!")
