"""
URL patterns for the bookings app.

This module defines URL patterns for booking creation, management,
and CRUD operations for different user roles.
"""

from django.urls import path
from . import views

app_name = 'bookings'

urlpatterns = [
    # Booking creation
    path('create/<int:accommodation_id>/', views.create_booking_view, name='create'),

    # Booking management
    path('<int:pk>/', views.booking_detail_view, name='detail'),
    path('<int:pk>/cancel/', views.cancel_booking_view, name='cancel'),

    # Booking lists
    path('my-bookings/', views.my_bookings_view, name='my_bookings'),
    path('host-bookings/', views.host_bookings_view, name='host_bookings'),
]
