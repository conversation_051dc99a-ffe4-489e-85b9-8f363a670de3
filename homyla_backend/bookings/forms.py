"""
Forms for booking management in NomadPersia platform.

This module contains forms for creating, managing, and filtering bookings
with proper validation and user-friendly interfaces.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import date, timedelta

from .models import Booking
from accommodations.models import Accommodation


class BookingForm(forms.ModelForm):
    """
    Form for creating new booking requests.
    """
    
    class Meta:
        model = Booking
        fields = [
            'check_in_date', 'check_out_date', 'number_of_guests', 'special_requests'
        ]
        widgets = {
            'check_in_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'min': date.today().isoformat()
            }),
            'check_out_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'min': (date.today() + timedelta(days=1)).isoformat()
            }),
            'number_of_guests': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '20',
                'value': '2'
            }),
            'special_requests': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Any special requests or requirements? (optional)'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.accommodation = kwargs.pop('accommodation', None)
        super().__init__(*args, **kwargs)
        
        # Add help text
        self.fields['check_in_date'].help_text = 'Select your arrival date'
        self.fields['check_out_date'].help_text = 'Select your departure date'
        self.fields['number_of_guests'].help_text = 'How many guests will be staying?'
        self.fields['special_requests'].help_text = 'Let the host know about any special needs'
    
    def clean(self):
        """Custom validation for booking dates."""
        cleaned_data = super().clean()
        check_in = cleaned_data.get('check_in_date')
        check_out = cleaned_data.get('check_out_date')
        
        if check_in and check_out:
            # Check if check-in is not in the past
            if check_in < date.today():
                raise ValidationError({
                    'check_in_date': _('Check-in date cannot be in the past.')
                })
            
            # Check if check-out is after check-in
            if check_out <= check_in:
                raise ValidationError({
                    'check_out_date': _('Check-out date must be after check-in date.')
                })
            
            # Check minimum stay (1 night)
            if (check_out - check_in).days < 1:
                raise ValidationError(_('Minimum stay is 1 night.'))
            
            # Check maximum stay (30 days for MVP)
            if (check_out - check_in).days > 30:
                raise ValidationError(_('Maximum stay is 30 days.'))
        
        return cleaned_data
    
    def save(self, commit=True):
        """Save booking with calculated pricing."""
        booking = super().save(commit=False)

        # Set accommodation if provided (view will handle setting it if not)
        if self.accommodation:
            booking.accommodation = self.accommodation
            booking.price_per_night = self.accommodation.price_per_night

            # Calculate total price only if we have accommodation
            nights = booking.duration_nights
            booking.total_price = booking.price_per_night * nights

        if commit:
            booking.save()

        return booking


class BookingSearchForm(forms.Form):
    """
    Form for searching and filtering bookings.
    """
    
    STATUS_CHOICES = [
        ('', 'All Statuses'),
    ] + Booking.STATUS_CHOICES
    
    SORT_CHOICES = [
        ('', 'Sort by...'),
        ('-created_at', 'Newest First'),
        ('created_at', 'Oldest First'),
        ('check_in_date', 'Check-in Date'),
        ('-check_in_date', 'Check-in Date (Desc)'),
        ('total_price', 'Price: Low to High'),
        ('-total_price', 'Price: High to Low'),
    ]
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    accommodation_name = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by accommodation name...'
        })
    )
    
    check_in_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    check_in_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )


class BookingStatusForm(forms.ModelForm):
    """
    Form for hosts and admins to update booking status.
    """
    
    class Meta:
        model = Booking
        fields = ['status', 'host_notes']
        widgets = {
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'host_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Add notes about this booking decision...'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Limit status choices based on current status and user role
        if self.instance and self.instance.pk:
            current_status = self.instance.status
            
            if current_status == 'pending':
                self.fields['status'].choices = [
                    ('pending', 'Pending'),
                    ('confirmed', 'Confirm'),
                    ('rejected', 'Reject'),
                ]
            elif current_status == 'confirmed':
                self.fields['status'].choices = [
                    ('confirmed', 'Confirmed'),
                    ('cancelled', 'Cancel'),
                    ('completed', 'Mark as Completed'),
                ]
            else:
                # For completed, cancelled, or rejected bookings
                self.fields['status'].choices = [
                    (current_status, self.instance.get_status_display())
                ]
                self.fields['status'].widget.attrs['disabled'] = True
