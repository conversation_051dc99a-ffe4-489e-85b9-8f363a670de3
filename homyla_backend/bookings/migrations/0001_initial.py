# Generated by Django 5.0.14 on 2025-07-02 09:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accommodations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Booking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("check_in_date", models.DateField(help_text="Check-in date")),
                ("check_out_date", models.DateField(help_text="Check-out date")),
                (
                    "number_of_guests",
                    models.PositiveSmallIntegerField(
                        default=1, help_text="Number of guests for this booking"
                    ),
                ),
                (
                    "total_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total price for the entire stay",
                        max_digits=12,
                    ),
                ),
                (
                    "price_per_night",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Price per night at time of booking",
                        max_digits=10,
                    ),
                ),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("pending", "Pending Approval"),
                            ("confirmed", "Confirmed"),
                            ("cancelled", "Cancelled"),
                            ("completed", "Completed"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        help_text="Current status of the booking",
                        max_length=10,
                    ),
                ),
                (
                    "special_requests",
                    models.TextField(
                        blank=True, help_text="Special requests from the guest"
                    ),
                ),
                (
                    "host_notes",
                    models.TextField(blank=True, help_text="Notes from the host"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "confirmed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the booking was confirmed",
                        null=True,
                    ),
                ),
                (
                    "cancelled_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the booking was cancelled",
                        null=True,
                    ),
                ),
                (
                    "accommodation",
                    models.ForeignKey(
                        help_text="Accommodation being booked",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookings",
                        to="accommodations.accommodation",
                    ),
                ),
                (
                    "guest",
                    models.ForeignKey(
                        help_text="Guest who made the booking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking",
                "verbose_name_plural": "Bookings",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["guest"], name="bookings_bo_guest_i_05b863_idx"
                    ),
                    models.Index(
                        fields=["accommodation"], name="bookings_bo_accommo_b469f3_idx"
                    ),
                    models.Index(
                        fields=["status"], name="bookings_bo_status_233e96_idx"
                    ),
                    models.Index(
                        fields=["check_in_date"], name="bookings_bo_check_i_5b803e_idx"
                    ),
                    models.Index(
                        fields=["check_out_date"], name="bookings_bo_check_o_61855e_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="bookings_bo_created_1720a2_idx"
                    ),
                ],
            },
        ),
    ]
