# Generated by Django 5.0.14 on 2025-07-19 01:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bookings", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="booking",
            name="adults",
            field=models.PositiveSmallIntegerField(
                default=1, help_text="Number of adult guests (18+ years)"
            ),
        ),
        migrations.AddField(
            model_name="booking",
            name="children",
            field=models.PositiveSmallIntegerField(
                default=0, help_text="Number of child guests (3-12 years)"
            ),
        ),
        migrations.AddField(
            model_name="booking",
            name="infants",
            field=models.PositiveSmallIntegerField(
                default=0, help_text="Number of infant guests (0-2 years)"
            ),
        ),
        migrations.AddField(
            model_name="booking",
            name="teens",
            field=models.PositiveSmallIntegerField(
                default=0, help_text="Number of teen guests (13-17 years)"
            ),
        ),
        migrations.AddField(
            model_name="booking",
            name="total_guests",
            field=models.PositiveSmallIntegerField(
                default=1, help_text="Total guests (calculated field)"
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="booking",
            name="number_of_guests",
            field=models.PositiveSmallIntegerField(
                default=1, help_text="Total number of guests for this booking"
            ),
        ),
    ]
