"""
Booking models for NomadPersia platform.

This module defines models for booking requests, confirmations, and history
with proper relationships between guests, hosts, and accommodations.
"""

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class Booking(models.Model):
    """
    Model representing a booking request/confirmation.

    Handles the relationship between guests and accommodations,
    including booking status, dates, and pricing.
    """

    STATUS_CHOICES = [
        ('pending', _('Pending Approval')),
        ('confirmed', _('Confirmed')),
        ('cancelled', _('Cancelled')),
        ('completed', _('Completed')),
        ('rejected', _('Rejected')),
    ]

    # Relationships
    guest = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        related_name='bookings',
        help_text=_('Guest who made the booking')
    )

    accommodation = models.ForeignKey(
        'accommodations.Accommodation',
        on_delete=models.CASCADE,
        related_name='bookings',
        help_text=_('Accommodation being booked')
    )

    # Booking Details
    check_in_date = models.DateField(
        help_text=_('Check-in date')
    )

    check_out_date = models.DateField(
        help_text=_('Check-out date')
    )

    number_of_guests = models.PositiveSmallIntegerField(
        default=1,
        help_text=_('Total number of guests for this booking')
    )

    # Guest breakdown by age category
    adults = models.PositiveSmallIntegerField(
        default=1,
        help_text=_('Number of adult guests (18+ years)')
    )

    teens = models.PositiveSmallIntegerField(
        default=0,
        help_text=_('Number of teen guests (13-17 years)')
    )

    children = models.PositiveSmallIntegerField(
        default=0,
        help_text=_('Number of child guests (3-12 years)')
    )

    infants = models.PositiveSmallIntegerField(
        default=0,
        help_text=_('Number of infant guests (0-2 years)')
    )

    total_guests = models.PositiveSmallIntegerField(
        default=1,
        help_text=_('Total guests (calculated field)')
    )

    # Pricing
    total_price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text=_('Total price for the entire stay')
    )

    price_per_night = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Price per night at time of booking')
    )

    # Status and Notes
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('Current status of the booking')
    )

    special_requests = models.TextField(
        blank=True,
        help_text=_('Special requests from the guest')
    )

    host_notes = models.TextField(
        blank=True,
        help_text=_('Notes from the host')
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('When the booking was confirmed')
    )
    cancelled_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('When the booking was cancelled')
    )

    class Meta:
        verbose_name = _('Booking')
        verbose_name_plural = _('Bookings')
        indexes = [
            models.Index(fields=['guest']),
            models.Index(fields=['accommodation']),
            models.Index(fields=['status']),
            models.Index(fields=['check_in_date']),
            models.Index(fields=['check_out_date']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        accommodation_name = self.accommodation.name if self.accommodation else "Unknown Accommodation"
        guest_name = self.guest.username if self.guest else "Unknown Guest"
        return f"Booking #{self.id} - {guest_name} at {accommodation_name}"

    @property
    def duration_nights(self):
        """Calculate the number of nights for this booking."""
        return (self.check_out_date - self.check_in_date).days

    @property
    def is_future_booking(self):
        """Check if this is a future booking."""
        return self.check_in_date > timezone.now().date()

    @property
    def is_current_booking(self):
        """Check if this booking is currently active."""
        today = timezone.now().date()
        return self.check_in_date <= today <= self.check_out_date

    @property
    def is_past_booking(self):
        """Check if this booking is in the past."""
        return self.check_out_date < timezone.now().date()

    def can_be_cancelled(self):
        """Check if this booking can be cancelled."""
        return (
            self.status in ['pending', 'confirmed'] and
            self.is_future_booking
        )

    def get_guest_breakdown(self):
        """Get a formatted string of guest breakdown."""
        breakdown = []
        if self.adults > 0:
            breakdown.append(f"{self.adults} adult{'s' if self.adults > 1 else ''}")
        if self.teens > 0:
            breakdown.append(f"{self.teens} teen{'s' if self.teens > 1 else ''}")
        if self.children > 0:
            breakdown.append(f"{self.children} child{'ren' if self.children > 1 else ''}")
        if self.infants > 0:
            breakdown.append(f"{self.infants} infant{'s' if self.infants > 1 else ''}")

        return ', '.join(breakdown) if breakdown else f"{self.total_guests} guest{'s' if self.total_guests > 1 else ''}"

    def save(self, *args, **kwargs):
        """Override save to calculate total guests and handle instant booking."""
        self.total_guests = self.adults + self.teens + self.children + self.infants
        self.number_of_guests = self.total_guests  # Keep legacy field in sync

        # Handle instant booking confirmation
        if self.accommodation and self.accommodation.instant_booking and self.status == 'pending':
            self.status = 'confirmed'
            print(f"🔍 DEBUG: Auto-confirming booking {self.pk} due to instant booking setting")

        super().save(*args, **kwargs)

    def confirm_instant_booking(self):
        """Confirm booking if instant booking is enabled."""
        if self.accommodation and self.accommodation.instant_booking and self.status == 'pending':
            self.status = 'confirmed'
            self.save()
            return True
        return False

    def confirm_booking(self):
        """Confirm the booking."""
        if self.status == 'pending':
            self.status = 'confirmed'
            self.confirmed_at = timezone.now()
            self.save()

    def cancel_booking(self, reason=''):
        """Cancel the booking."""
        if self.can_be_cancelled():
            self.status = 'cancelled'
            self.cancelled_at = timezone.now()
            if reason:
                self.host_notes = f"Cancellation reason: {reason}"
            self.save()

    def clean(self):
        """Custom validation."""
        # Skip accommodation validation during form processing (it will be set in the view)
        # Only validate accommodation if this is a complete booking (has an ID)
        if self.pk and not self.accommodation:
            raise ValidationError(_('Booking must have an accommodation.'))

        # Check-out date must be after check-in date
        if self.check_out_date and self.check_in_date and self.check_out_date <= self.check_in_date:
            raise ValidationError(_('Check-out date must be after check-in date.'))

        # Cannot book in the past
        if self.check_in_date and self.check_in_date < timezone.now().date():
            raise ValidationError(_('Cannot book dates in the past.'))

        # Only calculate total price if not already set and accommodation is available
        # Skip pricing calculation during form validation (accommodation not set yet)
        if (not self.total_price and
            hasattr(self, 'accommodation') and self.accommodation and
            self.check_in_date and self.check_out_date):
            try:
                # Use accommodation's dynamic pricing calculation
                self.total_price = self.accommodation.calculate_total_price(
                    self.check_in_date,
                    self.check_out_date,
                    self.total_guests,
                    adults=self.adults,
                    teens=self.teens,
                    children=self.children,
                    infants=self.infants
                )
                print(f"🔍 DEBUG: Calculated dynamic total price: {self.total_price} IRR")
            except Exception as e:
                print(f"❌ ERROR: Failed to calculate pricing: {e}")
                # Don't raise validation error during form processing
                if self.pk:  # Only raise error for existing bookings
                    raise ValidationError(_(f'Error calculating pricing: {str(e)}'))

    def save(self, *args, **kwargs):
        """Override save to run validation and set pricing."""
        # Set price per night from accommodation if not set (for display purposes)
        if not self.price_per_night and self.accommodation:
            self.price_per_night = self.accommodation.price_per_night

        self.full_clean()
        super().save(*args, **kwargs)
