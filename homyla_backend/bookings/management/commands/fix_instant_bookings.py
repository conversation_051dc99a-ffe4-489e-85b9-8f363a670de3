"""
Management command to fix existing bookings that should be confirmed due to instant booking settings.
"""

from django.core.management.base import BaseCommand
from bookings.models import Booking


class Command(BaseCommand):
    help = 'Fix existing bookings that should be confirmed due to instant booking settings'

    def handle(self, *args, **options):
        # Find bookings that are pending but should be confirmed due to instant booking
        pending_bookings = Booking.objects.filter(
            status='pending',
            accommodation__instant_booking=True
        )

        updated_count = 0
        for booking in pending_bookings:
            booking.status = 'confirmed'
            booking.save()
            updated_count += 1
            self.stdout.write(
                f"✅ Updated booking {booking.pk} for {booking.accommodation.name} to 'confirmed'"
            )

        if updated_count > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated {updated_count} booking(s) to confirmed status'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('No bookings needed updating')
            )
