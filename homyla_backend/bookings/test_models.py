"""
Test cases for bookings app models.

This module contains comprehensive tests for Booking model
and related functionality.
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta

from accounts.models import UserProfile
from accommodations.models import Accommodation
from .models import Booking


class BookingModelTest(TestCase):
    """Test cases for Booking model."""
    
    def setUp(self):
        """Set up test data."""
        # Create host user and profile
        self.host_user = User.objects.create_user(
            username='hostuser',
            email='<EMAIL>',
            password='hostpass123'
        )
        self.host_profile = UserProfile.objects.create(
            user=self.host_user,
            role='host'
        )
        
        # Create guest user and profile
        self.guest_user = User.objects.create_user(
            username='guestuser',
            email='<EMAIL>',
            password='guestpass123'
        )
        self.guest_profile = UserProfile.objects.create(
            user=self.guest_user,
            role='guest'
        )
        
        # Create accommodation
        self.accommodation = Accommodation.objects.create(
            name='Test Hotel Tehran',
            description='A beautiful hotel in Tehran',
            accommodation_type='hotel',
            city='tehran',
            price_per_night=Decimal('1500000'),
            star_rating=4,
            host=self.host_user,
            status='active'
        )
        
        # Booking data
        self.booking_data = {
            'guest': self.guest_user,
            'accommodation': self.accommodation,
            'check_in_date': date.today() + timedelta(days=7),
            'check_out_date': date.today() + timedelta(days=10),
            'number_of_guests': 2,
            'price_per_night': Decimal('1500000'),
            'total_price': Decimal('4500000'),  # 3 nights * 1500000
            'special_requests': 'Late check-in please',
            'status': 'pending'
        }
    
    def test_create_booking(self):
        """Test creating a booking."""
        booking = Booking.objects.create(**self.booking_data)
        
        self.assertEqual(booking.guest, self.guest_user)
        self.assertEqual(booking.accommodation, self.accommodation)
        self.assertEqual(booking.number_of_guests, 2)
        self.assertEqual(booking.status, 'pending')
        self.assertEqual(booking.total_price, Decimal('4500000'))
    
    def test_booking_str_method(self):
        """Test string representation of Booking."""
        booking = Booking.objects.create(**self.booking_data)
        expected_str = f"Booking #{booking.id} - Test Hotel Tehran"
        self.assertEqual(str(booking), expected_str)
    
    def test_duration_nights_property(self):
        """Test duration_nights property calculation."""
        booking = Booking.objects.create(**self.booking_data)
        expected_nights = 3  # 10 - 7 = 3 days
        self.assertEqual(booking.duration_nights, expected_nights)
    
    def test_booking_status_choices(self):
        """Test booking status validation."""
        valid_statuses = ['pending', 'confirmed', 'cancelled', 'completed', 'rejected']
        
        for status in valid_statuses:
            data = self.booking_data.copy()
            data['status'] = status
            booking = Booking(**data)
            booking.full_clean()  # Should not raise ValidationError
    
    def test_check_in_date_validation(self):
        """Test check-in date validation."""
        # Past date should be invalid
        data = self.booking_data.copy()
        data['check_in_date'] = date.today() - timedelta(days=1)
        
        booking = Booking(**data)
        with self.assertRaises(ValidationError):
            booking.full_clean()
    
    def test_check_out_after_check_in_validation(self):
        """Test that check-out date is after check-in date."""
        data = self.booking_data.copy()
        data['check_out_date'] = data['check_in_date']  # Same day
        
        booking = Booking(**data)
        with self.assertRaises(ValidationError):
            booking.full_clean()
        
        # Check-out before check-in
        data['check_out_date'] = data['check_in_date'] - timedelta(days=1)
        booking = Booking(**data)
        with self.assertRaises(ValidationError):
            booking.full_clean()
    
    def test_number_of_guests_validation(self):
        """Test number of guests validation."""
        # Valid guest numbers
        for guests in [1, 2, 5, 10]:
            data = self.booking_data.copy()
            data['number_of_guests'] = guests
            booking = Booking(**data)
            booking.full_clean()  # Should not raise ValidationError
        
        # Invalid guest numbers
        for guests in [0, -1]:
            data = self.booking_data.copy()
            data['number_of_guests'] = guests
            booking = Booking(**data)
            with self.assertRaises(ValidationError):
                booking.full_clean()
    
    def test_price_validation(self):
        """Test price validation."""
        # Valid prices
        valid_prices = [Decimal('100000'), Decimal('5000000')]
        
        for price in valid_prices:
            data = self.booking_data.copy()
            data['price_per_night'] = price
            data['total_price'] = price * 3  # 3 nights
            booking = Booking(**data)
            booking.full_clean()  # Should not raise ValidationError
        
        # Invalid prices (negative)
        data = self.booking_data.copy()
        data['price_per_night'] = Decimal('-100')
        booking = Booking(**data)
        with self.assertRaises(ValidationError):
            booking.full_clean()
    
    def test_booking_timestamps(self):
        """Test that timestamps are set correctly."""
        booking = Booking.objects.create(**self.booking_data)
        
        self.assertIsNotNone(booking.created_at)
        self.assertIsNotNone(booking.updated_at)
        
        # Update booking and check updated_at changes
        original_updated_at = booking.updated_at
        booking.status = 'confirmed'
        booking.confirmed_at = timezone.now()
        booking.save()
        
        self.assertGreater(booking.updated_at, original_updated_at)
        self.assertIsNotNone(booking.confirmed_at)
    
    def test_booking_default_status(self):
        """Test default status is pending."""
        data = self.booking_data.copy()
        del data['status']  # Remove status to test default
        
        booking = Booking.objects.create(**data)
        self.assertEqual(booking.status, 'pending')
    
    def test_can_be_cancelled_method(self):
        """Test can_be_cancelled method."""
        # Pending booking can be cancelled
        data = self.booking_data.copy()
        data['status'] = 'pending'
        booking = Booking.objects.create(**data)
        self.assertTrue(booking.can_be_cancelled())
        
        # Confirmed booking can be cancelled
        booking.status = 'confirmed'
        booking.save()
        self.assertTrue(booking.can_be_cancelled())
        
        # Completed booking cannot be cancelled
        booking.status = 'completed'
        booking.save()
        self.assertFalse(booking.can_be_cancelled())
        
        # Already cancelled booking cannot be cancelled
        booking.status = 'cancelled'
        booking.save()
        self.assertFalse(booking.can_be_cancelled())
    
    def test_cancel_booking_method(self):
        """Test cancel_booking method."""
        booking = Booking.objects.create(**self.booking_data)
        
        reason = "Change of plans"
        booking.cancel_booking(reason)
        
        self.assertEqual(booking.status, 'cancelled')
        self.assertIsNotNone(booking.cancelled_at)
        self.assertEqual(booking.host_notes, reason)
    
    def test_is_future_booking_property(self):
        """Test is_future_booking property."""
        # Future booking
        booking = Booking.objects.create(**self.booking_data)
        self.assertTrue(booking.is_future_booking)
        
        # Past booking
        data = self.booking_data.copy()
        data['check_in_date'] = date.today() - timedelta(days=10)
        data['check_out_date'] = date.today() - timedelta(days=7)
        past_booking = Booking.objects.create(**data)
        self.assertFalse(past_booking.is_future_booking)
    
    def test_booking_ordering(self):
        """Test default ordering by creation date."""
        booking1 = Booking.objects.create(**self.booking_data)
        
        data2 = self.booking_data.copy()
        data2['check_in_date'] = date.today() + timedelta(days=14)
        data2['check_out_date'] = date.today() + timedelta(days=17)
        booking2 = Booking.objects.create(**data2)
        
        bookings = list(Booking.objects.all())
        # Should be ordered by -created_at (newest first)
        self.assertEqual(bookings[0], booking2)
        self.assertEqual(bookings[1], booking1)
    
    def test_booking_update(self):
        """Test updating booking information."""
        booking = Booking.objects.create(**self.booking_data)
        
        # Update booking
        booking.number_of_guests = 4
        booking.special_requests = 'Updated request'
        booking.status = 'confirmed'
        booking.confirmed_at = timezone.now()
        booking.save()
        
        # Refresh from database
        booking.refresh_from_db()
        
        self.assertEqual(booking.number_of_guests, 4)
        self.assertEqual(booking.special_requests, 'Updated request')
        self.assertEqual(booking.status, 'confirmed')
        self.assertIsNotNone(booking.confirmed_at)
    
    def test_booking_deletion(self):
        """Test booking deletion."""
        booking = Booking.objects.create(**self.booking_data)
        booking_id = booking.id
        
        booking.delete()
        
        with self.assertRaises(Booking.DoesNotExist):
            Booking.objects.get(id=booking_id)
    
    def test_guest_deletion_cascades(self):
        """Test that deleting guest also deletes bookings."""
        booking = Booking.objects.create(**self.booking_data)
        booking_id = booking.id
        
        self.guest_user.delete()
        
        with self.assertRaises(Booking.DoesNotExist):
            Booking.objects.get(id=booking_id)
    
    def test_accommodation_deletion_cascades(self):
        """Test that deleting accommodation also deletes bookings."""
        booking = Booking.objects.create(**self.booking_data)
        booking_id = booking.id
        
        self.accommodation.delete()
        
        with self.assertRaises(Booking.DoesNotExist):
            Booking.objects.get(id=booking_id)
    
    def test_booking_display_methods(self):
        """Test display methods for choices."""
        booking = Booking.objects.create(**self.booking_data)
        self.assertEqual(booking.get_status_display(), 'Pending')
    
    def test_booking_filtering(self):
        """Test booking filtering functionality."""
        # Create bookings with different statuses
        booking1 = Booking.objects.create(**self.booking_data)
        
        data2 = self.booking_data.copy()
        data2['status'] = 'confirmed'
        data2['check_in_date'] = date.today() + timedelta(days=14)
        data2['check_out_date'] = date.today() + timedelta(days=17)
        booking2 = Booking.objects.create(**data2)
        
        # Test status filtering
        pending_bookings = Booking.objects.filter(status='pending')
        confirmed_bookings = Booking.objects.filter(status='confirmed')
        
        self.assertIn(booking1, pending_bookings)
        self.assertNotIn(booking2, pending_bookings)
        self.assertIn(booking2, confirmed_bookings)
        self.assertNotIn(booking1, confirmed_bookings)
        
        # Test guest filtering
        guest_bookings = Booking.objects.filter(guest=self.guest_user)
        self.assertIn(booking1, guest_bookings)
        self.assertIn(booking2, guest_bookings)
        
        # Test accommodation filtering
        accommodation_bookings = Booking.objects.filter(accommodation=self.accommodation)
        self.assertIn(booking1, accommodation_bookings)
        self.assertIn(booking2, accommodation_bookings)
