"""
Views for booking management in NomadPersia platform.

This module contains views for creating, managing, and displaying bookings
for guests, hosts, and admins.
"""

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.utils import timezone

from accounts.views import role_required
from accommodations.models import Accommodation
from .forms import BookingForm, BookingSearchForm, BookingStatusForm
from .models import Booking


@login_required
@role_required(['guest'])
def create_booking_view(request, accommodation_id):
    """
    View for guests to create new booking requests with dynamic pricing and capacity.
    """

    accommodation = get_object_or_404(Accommodation, pk=accommodation_id, status='active')

    # Prevent hosts from booking their own properties
    if accommodation.host == request.user:
        messages.error(request, 'You cannot book your own property.')
        return redirect('accommodations:detail', pk=accommodation_id)

    if request.method == 'POST':
        # Extract guest counts from form data
        adults = int(request.POST.get('adults', 1))
        teens = int(request.POST.get('teens', 0))
        children = int(request.POST.get('children', 0))
        infants = int(request.POST.get('infants', 0))

        total_guests = adults + teens + children + infants

        # Validate capacity
        if total_guests > accommodation.max_capacity:
            messages.error(request, f'Total guests ({total_guests}) exceeds maximum capacity ({accommodation.max_capacity}).')
            return redirect('accommodations:detail', pk=accommodation_id)

        if adults == 0:
            messages.error(request, 'At least one adult is required for booking.')
            return redirect('accommodations:detail', pk=accommodation_id)

        # Create booking form with accommodation context
        form = BookingForm(request.POST, accommodation=accommodation)
        if form.is_valid():
            # Create booking object manually to ensure accommodation is set from the start
            booking = Booking(
                accommodation=accommodation,
                guest=request.user,
                check_in_date=form.cleaned_data['check_in_date'],
                check_out_date=form.cleaned_data['check_out_date'],
                number_of_guests=form.cleaned_data['number_of_guests'],
                special_requests=form.cleaned_data.get('special_requests', ''),
            )

            # Set guest breakdown
            booking.adults = adults
            booking.teens = teens
            booking.children = children
            booking.infants = infants
            booking.total_guests = total_guests

            print(f"🔍 DEBUG: Booking accommodation set to: {booking.accommodation} (ID: {booking.accommodation.id})")
            print(f"🔍 DEBUG: Booking dates: {booking.check_in_date} to {booking.check_out_date}")
            print(f"🔍 DEBUG: Guest count: {total_guests}")

            # Calculate total price using accommodation's pricing logic with guest breakdown
            booking.total_price = accommodation.calculate_total_price(
                booking.check_in_date,
                booking.check_out_date,
                total_guests,
                adults=adults,
                teens=teens,
                children=children,
                infants=infants
            )

            # Set booking status based on instant booking setting
            if accommodation.instant_booking:
                booking.status = 'confirmed'
                print(f"🔍 DEBUG: Instant booking enabled - setting status to 'confirmed'")
            else:
                booking.status = 'pending'
                print(f"🔍 DEBUG: Host approval required - setting status to 'pending'")

            # Save booking with error handling
            try:
                booking.save()
                print(f"🔍 DEBUG: Booking saved successfully with ID: {booking.id}")
            except Exception as e:
                print(f"❌ ERROR: Failed to save booking: {e}")
                messages.error(request, f'Error creating booking: {str(e)}')
                return redirect('accommodations:detail', pk=accommodation_id)

            # Success message based on booking type
            if accommodation.instant_booking:
                messages.success(
                    request,
                    f'Booking confirmed for {accommodation.name}! '
                    f'Total cost: {booking.total_price:,} IRR'
                )
            else:
                messages.success(
                    request,
                    f'Booking request submitted for {accommodation.name}! '
                    f'The host will review your request. Total cost: {booking.total_price:,} IRR'
                )
            return redirect('bookings:detail', pk=booking.pk)
        else:
            # Form validation failed
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = BookingForm(accommodation=accommodation)

    # Get pricing and availability data for booking system
    from datetime import date, timedelta
    from accommodations.models import AccommodationPricing, AccommodationAvailability, GuestCapacityRule
    import json

    # Get pricing rules for the next 90 days
    today = date.today()
    end_date = today + timedelta(days=90)

    pricing_rules = AccommodationPricing.objects.filter(
        accommodation=accommodation,
        is_active=True,
        end_date__gte=today
    ).order_by('start_date')

    # Get availability rules
    availability_rules = AccommodationAvailability.objects.filter(
        accommodation=accommodation,
        end_date__gte=today
    ).order_by('start_date')

    # Get guest capacity rules
    capacity_rules = GuestCapacityRule.objects.filter(
        accommodation=accommodation
    ).order_by('age_category')

    # Serialize capacity rules for JavaScript
    capacity_rules_data = []
    for rule in capacity_rules:
        capacity_rules_data.append({
            'age_category': rule.age_category,
            'age_category_display': rule.get_age_category_display(),
            'price_per_night': int(rule.price_per_night),
            'counts_in_capacity': rule.counts_in_capacity
        })

    capacity_rules_json = json.dumps(capacity_rules_data)

    # Generate pricing calendar for next 90 days
    pricing_calendar = {}
    current_date = today
    while current_date <= end_date:
        pricing_calendar[current_date.isoformat()] = {
            'date': current_date.isoformat(),
            'price': int(accommodation.get_price_for_date(current_date)),
            'available': True,
            'minimum_nights': 1
        }
        current_date += timedelta(days=1)

    # Convert to JSON string for safe template rendering
    pricing_calendar_json = json.dumps(pricing_calendar)

    # Apply availability rules to calendar
    for rule in availability_rules:
        rule_start = max(rule.start_date, today)
        rule_end = min(rule.end_date, end_date)
        current_date = rule_start

        while current_date <= rule_end:
            if current_date.isoformat() in pricing_calendar:
                pricing_calendar[current_date.isoformat()]['available'] = not rule.is_blocked
                pricing_calendar[current_date.isoformat()]['minimum_nights'] = rule.minimum_nights
            current_date += timedelta(days=1)

    context = {
        'form': form,
        'accommodation': accommodation,
        'price_per_night': accommodation.price_per_night,
        'pricing_rules': pricing_rules,
        'availability_rules': availability_rules,
        'capacity_rules': capacity_rules,
        'capacity_rules_json': capacity_rules_json,
        'pricing_calendar': pricing_calendar,
        'pricing_calendar_json': pricing_calendar_json,
        'base_price': accommodation.price_per_night,
        'extra_guest_fee': accommodation.extra_guest_fee,
        'standard_capacity': accommodation.standard_capacity,
        'max_capacity': accommodation.max_capacity,
    }

    return render(request, 'bookings/create.html', context)


@login_required
def booking_detail_view(request, pk):
    """
    Detail view for a specific booking.
    """
    booking = get_object_or_404(Booking, pk=pk)

    # Check permissions
    is_guest = booking.guest == request.user
    is_host = booking.accommodation.host == request.user
    is_admin = hasattr(request.user, 'profile') and request.user.profile.is_admin_user()

    if not (is_guest or is_host or is_admin):
        messages.error(request, 'You do not have permission to view this booking.')
        return redirect('accommodations:search')

    # Handle status updates
    if request.method == 'POST' and (is_host or is_admin):
        form = BookingStatusForm(request.POST, instance=booking, user=request.user)
        if form.is_valid():
            old_status = booking.status
            booking = form.save(commit=False)

            # Set timestamps based on status change
            if old_status != booking.status:
                if booking.status == 'confirmed':
                    booking.confirmed_at = timezone.now()
                elif booking.status == 'cancelled':
                    booking.cancelled_at = timezone.now()

            booking.save()

            # Send appropriate message
            status_messages = {
                'confirmed': 'Booking has been confirmed!',
                'rejected': 'Booking has been rejected.',
                'cancelled': 'Booking has been cancelled.',
                'completed': 'Booking has been marked as completed.',
            }

            if booking.status in status_messages:
                messages.success(request, status_messages[booking.status])

            return redirect('bookings:detail', pk=booking.pk)
    else:
        form = BookingStatusForm(instance=booking, user=request.user) if (is_host or is_admin) else None

    context = {
        'booking': booking,
        'is_guest': is_guest,
        'is_host': is_host,
        'is_admin': is_admin,
        'form': form,
        'can_cancel': booking.can_be_cancelled() and is_guest,
    }

    return render(request, 'bookings/detail.html', context)


@login_required
def my_bookings_view(request):
    """
    View for users to see their bookings (guests see their requests, hosts see property bookings).
    """
    user_profile = getattr(request.user, 'profile', None)

    if not user_profile:
        messages.error(request, 'Profile not found.')
        return redirect('accommodations:search')

    form = BookingSearchForm(request.GET or None)

    if user_profile.is_guest():
        # Guests see their own booking requests
        bookings = Booking.objects.filter(guest=request.user)
        page_title = 'My Booking Requests'
    elif user_profile.is_host():
        # Hosts see bookings for their properties
        bookings = Booking.objects.filter(accommodation__host=request.user)
        page_title = 'Bookings for My Properties'
    else:
        # Admins see all bookings
        bookings = Booking.objects.all()
        page_title = 'All Bookings'

    # Apply filters
    if form.is_valid():
        if form.cleaned_data.get('status'):
            bookings = bookings.filter(status=form.cleaned_data['status'])

        if form.cleaned_data.get('accommodation_name'):
            bookings = bookings.filter(
                accommodation__name__icontains=form.cleaned_data['accommodation_name']
            )

        if form.cleaned_data.get('check_in_from'):
            bookings = bookings.filter(
                check_in_date__gte=form.cleaned_data['check_in_from']
            )

        if form.cleaned_data.get('check_in_to'):
            bookings = bookings.filter(
                check_in_date__lte=form.cleaned_data['check_in_to']
            )

        # Apply sorting
        sort_by = form.cleaned_data.get('sort_by')
        if sort_by:
            bookings = bookings.order_by(sort_by)
        else:
            bookings = bookings.order_by('-created_at')
    else:
        bookings = bookings.order_by('-created_at')

    # Pagination
    paginator = Paginator(bookings, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_count = bookings.count()
    pending_count = bookings.filter(status='pending').count()
    confirmed_count = bookings.filter(status='confirmed').count()

    context = {
        'form': form,
        'page_obj': page_obj,
        'bookings': page_obj,
        'page_title': page_title,
        'total_count': total_count,
        'pending_count': pending_count,
        'confirmed_count': confirmed_count,
        'user_role': user_profile.role,
    }

    return render(request, 'bookings/list.html', context)


@login_required
def cancel_booking_view(request, pk):
    """
    View for guests to cancel their bookings.
    """
    booking = get_object_or_404(Booking, pk=pk, guest=request.user)

    if not booking.can_be_cancelled():
        messages.error(request, 'This booking cannot be cancelled.')
        return redirect('bookings:detail', pk=pk)

    if request.method == 'POST':
        reason = request.POST.get('reason', '')
        booking.cancel_booking(reason)
        messages.success(request, 'Your booking has been cancelled.')
        return redirect('bookings:detail', pk=pk)

    context = {
        'booking': booking,
    }

    return render(request, 'bookings/cancel.html', context)


@login_required
@role_required(['host', 'admin'])
def host_bookings_view(request):
    """
    View for hosts to manage bookings for their properties.
    """
    if hasattr(request.user, 'profile') and request.user.profile.is_host():
        bookings = Booking.objects.filter(accommodation__host=request.user)
    else:
        # Admin sees all bookings
        bookings = Booking.objects.all()

    form = BookingSearchForm(request.GET or None)

    # Apply filters (same logic as my_bookings_view)
    if form.is_valid():
        if form.cleaned_data.get('status'):
            bookings = bookings.filter(status=form.cleaned_data['status'])

        if form.cleaned_data.get('accommodation_name'):
            bookings = bookings.filter(
                accommodation__name__icontains=form.cleaned_data['accommodation_name']
            )

        if form.cleaned_data.get('check_in_from'):
            bookings = bookings.filter(
                check_in_date__gte=form.cleaned_data['check_in_from']
            )

        if form.cleaned_data.get('check_in_to'):
            bookings = bookings.filter(
                check_in_date__lte=form.cleaned_data['check_in_to']
            )

        sort_by = form.cleaned_data.get('sort_by')
        if sort_by:
            bookings = bookings.order_by(sort_by)
        else:
            bookings = bookings.order_by('-created_at')
    else:
        bookings = bookings.order_by('-created_at')

    # Pagination
    paginator = Paginator(bookings, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_count = bookings.count()
    pending_count = bookings.filter(status='pending').count()
    confirmed_count = bookings.filter(status='confirmed').count()
    revenue = sum(b.total_price for b in bookings.filter(status__in=['confirmed', 'completed']))

    context = {
        'form': form,
        'page_obj': page_obj,
        'bookings': page_obj,
        'total_count': total_count,
        'pending_count': pending_count,
        'confirmed_count': confirmed_count,
        'revenue': revenue,
        'is_admin': hasattr(request.user, 'profile') and request.user.profile.is_admin_user(),
    }

    return render(request, 'bookings/host_list.html', context)
