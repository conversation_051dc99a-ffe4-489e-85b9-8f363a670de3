"""
API Views for NomadPersia Platform

This module contains API views for handling REST API requests
for accommodations, bookings, and user authentication.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action, api_view
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.contrib.auth import get_user_model
from django.db.models import Q

from accommodations.models import Accommodation
from bookings.models import Booking
from accounts.models import UserProfile
from reviews.models import Review
from .serializers import (
    AccommodationListSerializer, AccommodationDetailSerializer,
    AccommodationCreateUpdateSerializer, BookingSerializer,
    BookingCreateSerializer, BookingUpdateSerializer,
    UserSerializer, ProfileSerializer,
    ReviewSerializer, ReviewCreateSerializer, HostResponseSerializer
)
from .permissions import IsHostOrAdmin, IsOwnerOrAdmin, IsGuestOrAdmin
from .filters import AccommodationFilter

User = get_user_model()


class AccommodationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing accommodations.

    Provides CRUD operations for accommodations with proper permissions:
    - List/Retrieve: Public access for active accommodations
    - Create: Host users only
    - Update/Delete: Host owner or admin only
    """

    queryset = Accommodation.objects.select_related('host')
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'description', 'city', 'town', 'address']
    ordering_fields = ['created_at', 'updated_at', 'price_per_night', 'star_rating']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return AccommodationListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return AccommodationCreateUpdateSerializer
        return AccommodationDetailSerializer

    def get_permissions(self):
        """Return appropriate permissions based on action."""
        if self.action in ['list', 'retrieve', 'search']:
            permission_classes = [permissions.AllowAny]
        elif self.action == 'create':
            permission_classes = [permissions.IsAuthenticated, IsHostOrAdmin]
        else:  # update, partial_update, destroy
            permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        queryset = self.queryset

        if self.action in ['list', 'search']:
            # Public views only show active accommodations
            queryset = queryset.filter(status='active')
        elif self.action == 'retrieve':
            # Detail view shows active accommodations or user's own accommodations
            if self.request.user.is_authenticated:
                queryset = queryset.filter(
                    Q(status='active') | Q(host=self.request.user)
                )
            else:
                queryset = queryset.filter(status='active')

        return queryset

    def perform_create(self, serializer):
        """Set the host to the current user when creating."""
        serializer.save(host=self.request.user)

    @action(detail=False, methods=['get'], permission_classes=[permissions.AllowAny])
    def search(self, request):
        """
        Advanced search endpoint for accommodations.

        Query parameters:
        - q: Search query
        - city: Filter by city
        - accommodation_type: Filter by type
        - min_price: Minimum price per night
        - max_price: Maximum price per night
        - min_capacity: Minimum guest capacity
        - instant_booking: Filter by instant booking availability
        """
        queryset = self.get_queryset()

        # Apply filters
        q = request.query_params.get('q')
        if q:
            queryset = queryset.filter(
                Q(name__icontains=q) |
                Q(description__icontains=q) |
                Q(city__icontains=q) |
                Q(town__icontains=q)
            )

        city = request.query_params.get('city')
        if city:
            queryset = queryset.filter(city=city)

        accommodation_type = request.query_params.get('accommodation_type')
        if accommodation_type:
            queryset = queryset.filter(accommodation_type=accommodation_type)

        min_price = request.query_params.get('min_price')
        if min_price:
            try:
                queryset = queryset.filter(price_per_night__gte=int(min_price))
            except ValueError:
                pass

        max_price = request.query_params.get('max_price')
        if max_price:
            try:
                queryset = queryset.filter(price_per_night__lte=int(max_price))
            except ValueError:
                pass

        min_capacity = request.query_params.get('min_capacity')
        if min_capacity:
            try:
                queryset = queryset.filter(max_capacity__gte=int(min_capacity))
            except ValueError:
                pass

        instant_booking = request.query_params.get('instant_booking')
        if instant_booking and instant_booking.lower() == 'true':
            queryset = queryset.filter(instant_booking=True)

        # Apply ordering
        ordering = request.query_params.get('ordering', '-created_at')
        if ordering in self.ordering_fields or ordering.lstrip('-') in self.ordering_fields:
            queryset = queryset.order_by(ordering)

        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = AccommodationListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = AccommodationListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'], permission_classes=[permissions.AllowAny])
    def availability(self, request, pk=None):
        """Check accommodation availability for given dates."""
        accommodation = self.get_object()
        check_in = request.query_params.get('check_in')
        check_out = request.query_params.get('check_out')

        if not check_in or not check_out:
            return Response(
                {'error': 'check_in and check_out dates are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from datetime import datetime
            check_in_date = datetime.strptime(check_in, '%Y-%m-%d').date()
            check_out_date = datetime.strptime(check_out, '%Y-%m-%d').date()

            if check_out_date <= check_in_date:
                return Response(
                    {'error': 'Check-out date must be after check-in date'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check for existing bookings
            conflicting_bookings = Booking.objects.filter(
                accommodation=accommodation,
                status__in=['confirmed', 'pending'],
                check_in_date__lt=check_out_date,
                check_out_date__gt=check_in_date
            ).exists()

            # Check availability rules
            blocked_periods = accommodation.availability_rules.filter(
                is_blocked=True,
                start_date__lt=check_out_date,
                end_date__gt=check_in_date
            ).exists()

            is_available = not (conflicting_bookings or blocked_periods)

            return Response({
                'available': is_available,
                'check_in': check_in,
                'check_out': check_out,
                'nights': (check_out_date - check_in_date).days
            })

        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )


class BookingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing bookings.

    Provides CRUD operations for bookings with proper permissions:
    - List: User's own bookings or host's property bookings
    - Create: Authenticated users (guests)
    - Update: Host or admin for status changes
    - Delete: Booking owner (cancel booking)
    """

    queryset = Booking.objects.select_related('accommodation', 'guest').all()
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'accommodation__city']
    ordering_fields = ['created_at', 'check_in_date', 'check_out_date']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return BookingCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return BookingUpdateSerializer
        return BookingSerializer

    def get_permissions(self):
        """Return appropriate permissions based on action."""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        elif self.action == 'create':
            permission_classes = [permissions.IsAuthenticated, IsGuestOrAdmin]
        elif self.action in ['update', 'partial_update']:
            permission_classes = [permissions.IsAuthenticated, IsHostOrAdmin]
        else:  # destroy (cancel)
            permission_classes = [permissions.IsAuthenticated, IsOwnerOrAdmin]

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """Filter queryset based on user permissions."""
        # Handle schema generation for Swagger
        if getattr(self, 'swagger_fake_view', False):
            return self.queryset.none()

        user = self.request.user

        # Handle anonymous users or users without profiles
        if not user.is_authenticated or not hasattr(user, 'profile'):
            return self.queryset.none()

        if user.profile.is_admin_user():
            # Admin can see all bookings
            return self.queryset
        elif user.profile.is_host():
            # Host can see bookings for their properties and their own bookings
            return self.queryset.filter(
                Q(accommodation__host=user) | Q(guest=user)
            )
        else:
            # Guests can only see their own bookings
            return self.queryset.filter(guest=user)

    def perform_create(self, serializer):
        """Set the guest to the current user and calculate total price."""
        accommodation_id = serializer.validated_data['accommodation_id']
        accommodation = Accommodation.objects.get(id=accommodation_id)

        # Calculate total price
        check_in_date = serializer.validated_data['check_in_date']
        check_out_date = serializer.validated_data['check_out_date']
        adults = serializer.validated_data.get('adults', 1)
        teens = serializer.validated_data.get('teens', 0)
        children = serializer.validated_data.get('children', 0)
        infants = serializer.validated_data.get('infants', 0)

        total_price = accommodation.calculate_total_price(
            check_in_date, check_out_date,
            adults + teens + children + infants,
            adults=adults, teens=teens, children=children, infants=infants
        )

        # Set booking status based on instant booking
        booking_status = 'confirmed' if accommodation.instant_booking else 'pending'

        serializer.save(
            guest=self.request.user,
            accommodation=accommodation,
            total_price=total_price,
            status=booking_status
        )

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated, IsOwnerOrAdmin])
    def cancel(self, request, pk=None):
        """Cancel a booking."""
        booking = self.get_object()

        if booking.status in ['cancelled', 'completed']:
            return Response(
                {'error': f'Cannot cancel a {booking.status} booking'},
                status=status.HTTP_400_BAD_REQUEST
            )

        booking.status = 'cancelled'
        booking.save()

        serializer = self.get_serializer(booking)
        return Response(serializer.data)


class UserProfileView(APIView):
    """
    View for retrieving and updating user profile information.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Get current user's profile."""
        # Create profile if it doesn't exist
        profile, created = UserProfile.objects.get_or_create(user=request.user)
        serializer = ProfileSerializer(profile, context={'request': request})
        return Response(serializer.data)

    def put(self, request):
        """Update current user's profile."""
        # Create profile if it doesn't exist
        profile, created = UserProfile.objects.get_or_create(user=request.user)
        serializer = ProfileSerializer(
            profile,
            data=request.data,
            partial=True,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserRegistrationView(APIView):
    """
    View for user registration.
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        """Register a new user."""
        user_data = {
            'username': request.data.get('username'),
            'email': request.data.get('email'),
            'password': request.data.get('password'),
            'first_name': request.data.get('first_name', ''),
            'last_name': request.data.get('last_name', ''),
        }

        # Validate required fields
        if not all([user_data['username'], user_data['email'], user_data['password']]):
            return Response(
                {'error': 'Username, email, and password are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user already exists
        if User.objects.filter(username=user_data['username']).exists():
            return Response(
                {'error': 'Username already exists'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if User.objects.filter(email=user_data['email']).exists():
            return Response(
                {'error': 'Email already exists'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Create user
            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            )

            # Create or get profile
            profile, created = UserProfile.objects.get_or_create(user=user)

            # Update profile with additional data
            profile_data = {
                'phone_number': request.data.get('phone_number', ''),
                'date_of_birth': request.data.get('date_of_birth'),
                'role': 'host' if request.data.get('is_host', False) else 'guest',
            }

            for key, value in profile_data.items():
                if value is not None:
                    setattr(profile, key, value)
            profile.save()

            # Return user data
            serializer = ProfileSerializer(profile, context={'request': request})
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {'error': f'Registration failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )


class ReviewViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing reviews.

    Provides CRUD operations for reviews with proper permissions.
    """

    queryset = Review.objects.filter(is_published=True).select_related(
        'guest', 'accommodation', 'booking'
    ).order_by('-created_at')

    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['review_text', 'accommodation__name', 'guest__username']
    ordering_fields = ['created_at', 'overall_rating']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return ReviewCreateSerializer
        elif self.action == 'respond':
            return HostResponseSerializer
        return ReviewSerializer

    def get_queryset(self):
        """Filter queryset based on user permissions and URL parameters."""
        # Handle schema generation for Swagger
        if getattr(self, 'swagger_fake_view', False):
            return self.queryset.none()

        user = self.request.user
        queryset = self.queryset

        # Filter by accommodation if specified in URL
        accommodation_id = self.request.query_params.get('accommodation_id')
        if accommodation_id:
            queryset = queryset.filter(accommodation_id=accommodation_id)

        # Handle permissions
        if not user.is_authenticated:
            return queryset.none()

        # Admin can see all reviews
        if hasattr(user, 'profile') and user.profile.is_admin_user():
            return queryset

        # Hosts can see reviews for their accommodations
        if hasattr(user, 'profile') and user.profile.is_host():
            host_reviews = queryset.filter(accommodation__host=user)
            own_reviews = queryset.filter(guest=user)
            return (host_reviews | own_reviews).distinct()

        # Guests can see all published reviews and their own
        return queryset

    def perform_create(self, serializer):
        """Create review with current user as guest."""
        serializer.save()

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def respond(self, request, pk=None):
        """
        Allow hosts to respond to reviews.

        POST /api/v1/reviews/{id}/respond/
        """
        review = self.get_object()

        # Check if user is the host
        if review.accommodation.host != request.user:
            return Response(
                {'error': 'Only the accommodation host can respond to reviews.'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = HostResponseSerializer(
            review,
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            serializer.save()
            return Response(ReviewSerializer(review, context={'request': request}).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AccommodationReviewsViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for listing reviews for a specific accommodation.

    GET /api/v1/accommodations/{id}/reviews/
    """

    serializer_class = ReviewSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [OrderingFilter]
    ordering_fields = ['created_at', 'overall_rating']
    ordering = ['-created_at']

    def get_queryset(self):
        """Get reviews for the specified accommodation."""
        accommodation_id = self.kwargs.get('accommodation_pk')
        return Review.objects.filter(
            accommodation_id=accommodation_id,
            is_published=True
        ).select_related('guest', 'accommodation', 'booking')


@api_view(['GET'])
def api_root(request):
    """
    API Root endpoint
    """
    return Response({
        'message': 'Welcome to NomadPersia API',
        'version': 'v1'
    })
