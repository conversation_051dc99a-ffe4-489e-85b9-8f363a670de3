"""
API Serializers for NomadPersia Platform

This module contains serializers for converting Django model instances
to JSON representations and handling API request/response data.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from accommodations.models import Accommodation, AccommodationPhoto, AccommodationPricing, AccommodationAvailability, GuestCapacityRule
from bookings.models import Booking
from accounts.models import UserProfile
from reviews.models import Review, ReviewHelpfulness

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model."""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'date_joined']
        read_only_fields = ['id', 'date_joined']


class ProfileSerializer(serializers.ModelSerializer):
    """Serializer for User Profile."""
    user = UserSerializer(read_only=True)

    class Meta:
        model = UserProfile
        fields = ['user', 'role', 'phone_number', 'date_of_birth', 'profile_image',
                 'national_id', 'emergency_contact', 'gender', 'created_at']
        read_only_fields = ['created_at']


class AccommodationPhotoSerializer(serializers.ModelSerializer):
    """Serializer for Accommodation Photos."""

    class Meta:
        model = AccommodationPhoto
        fields = ['id', 'image', 'photo_type', 'is_main', 'caption', 'uploaded_at']
        read_only_fields = ['id', 'uploaded_at']


class AccommodationPricingSerializer(serializers.ModelSerializer):
    """Serializer for Accommodation Pricing Rules."""

    class Meta:
        model = AccommodationPricing
        fields = ['id', 'pricing_type', 'start_date', 'end_date', 'price_per_night',
                 'description', 'is_active']
        read_only_fields = ['id']


class AccommodationAvailabilitySerializer(serializers.ModelSerializer):
    """Serializer for Accommodation Availability Rules."""

    class Meta:
        model = AccommodationAvailability
        fields = ['id', 'start_date', 'end_date', 'is_blocked', 'minimum_nights', 'reason']
        read_only_fields = ['id']


class GuestCapacityRuleSerializer(serializers.ModelSerializer):
    """Serializer for Guest Capacity Rules."""

    class Meta:
        model = GuestCapacityRule
        fields = ['id', 'age_category', 'price_per_night', 'counts_in_capacity']
        read_only_fields = ['id']


class AccommodationListSerializer(serializers.ModelSerializer):
    """Serializer for Accommodation list view (minimal data)."""
    host = UserSerializer(read_only=True)
    city_display = serializers.CharField(source='get_city_display', read_only=True)
    accommodation_type_display = serializers.CharField(source='get_accommodation_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    class Meta:
        model = Accommodation
        fields = [
            'id', 'name', 'accommodation_type', 'accommodation_type_display',
            'city', 'city_display', 'town', 'price_per_night', 'star_rating',
            'standard_capacity', 'max_capacity', 'bedrooms', 'bathrooms',
            'status', 'status_display', 'instant_booking', 'host',
            'average_rating', 'total_reviews', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AccommodationDetailSerializer(serializers.ModelSerializer):
    """Serializer for Accommodation detail view (complete data)."""
    host = UserSerializer(read_only=True)
    
    # Display fields
    city_display = serializers.CharField(source='get_city_display', read_only=True)
    accommodation_type_display = serializers.CharField(source='get_accommodation_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    cancellation_policy_display = serializers.CharField(source='get_cancellation_policy_display', read_only=True)
    check_out_time_display = serializers.CharField(source='get_check_out_time_display', read_only=True)
    
    class Meta:
        model = Accommodation
        fields = [
            'id', 'name', 'description', 'accommodation_type', 'accommodation_type_display',
            'city', 'city_display', 'town', 'address', 'reservation_phone', 'geolocation',
            'price_per_night', 'star_rating', 'amenities', 'image_placeholder',
            'standard_capacity', 'max_capacity', 'bedrooms', 'bathrooms', 'property_size',
            'extra_guest_fee', 'instant_booking', 'cancellation_policy', 'cancellation_policy_display',
            'check_in_time', 'check_out_time', 'check_out_time_display', 'additional_policies',
            'smoking_allowed', 'same_gender_groups_only', 'national_id_required',
            'pets_not_allowed', 'events_not_allowed', 'no_24hour_checkin',
            'status', 'status_display', 'host', 'average_rating', 'total_reviews',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'host', 'status', 'created_at', 'updated_at']


class AccommodationCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating accommodations."""
    
    class Meta:
        model = Accommodation
        fields = [
            'name', 'description', 'accommodation_type', 'city', 'town', 'address',
            'reservation_phone', 'geolocation', 'price_per_night', 'star_rating',
            'amenities', 'image_placeholder', 'standard_capacity', 'max_capacity',
            'bedrooms', 'bathrooms', 'property_size', 'extra_guest_fee',
            'instant_booking', 'cancellation_policy', 'check_in_time', 'check_out_time',
            'additional_policies', 'smoking_allowed', 'same_gender_groups_only',
            'national_id_required', 'pets_not_allowed', 'events_not_allowed',
            'no_24hour_checkin'
        ]
    
    def validate_reservation_phone(self, value):
        """Validate international phone number format."""
        import re
        if value:
            clean_phone = re.sub(r'[\s\-\(\)]', '', value)

            # Support international phone number formats
            # Must start with + followed by country code (1-4 digits) and phone number (4-15 digits)
            # Or local format starting with 0 (for backward compatibility)
            international_format = re.match(r'^\+[1-9]\d{1,3}\d{4,15}$', clean_phone)
            local_format = re.match(r'^0\d{7,15}$', clean_phone)

            if not (international_format or local_format):
                raise serializers.ValidationError(
                    'Please enter a valid phone number (international format: +[country code][number] or local format starting with 0)'
                )
        return value
    
    def validate_geolocation(self, value):
        """Validate geolocation format."""
        import re
        if value:
            geo_pattern = re.match(r'^-?[0-9]+\.?[0-9]*,-?[0-9]+\.?[0-9]*$', value.replace(' ', ''))
            if not geo_pattern:
                raise serializers.ValidationError(
                    'Please enter coordinates in format: latitude,longitude (e.g., 35.6892,51.3890)'
                )
        return value
    
    def validate(self, data):
        """Custom validation for accommodation data."""
        max_capacity = data.get('max_capacity')
        standard_capacity = data.get('standard_capacity')
        extra_guest_fee = data.get('extra_guest_fee', 0)
        
        # Validate capacity logic
        if max_capacity and standard_capacity and max_capacity < standard_capacity:
            raise serializers.ValidationError({
                'max_capacity': 'Maximum capacity cannot be less than standard capacity.'
            })
        
        # Validate extra guest fee
        if (max_capacity and standard_capacity and max_capacity > standard_capacity and
            extra_guest_fee == 0):
            raise serializers.ValidationError({
                'extra_guest_fee': 'Extra guest fee should be set when maximum capacity exceeds standard capacity.'
            })
        
        return data


class BookingSerializer(serializers.ModelSerializer):
    """Serializer for Booking model."""
    accommodation = AccommodationListSerializer(read_only=True)
    accommodation_id = serializers.IntegerField(write_only=True)
    guest = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    guest_breakdown = serializers.CharField(source='get_guest_breakdown', read_only=True)
    duration_nights = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = Booking
        fields = [
            'id', 'accommodation', 'accommodation_id', 'guest', 'check_in_date',
            'check_out_date', 'adults', 'teens', 'children', 'infants',
            'total_guests', 'number_of_guests', 'guest_breakdown', 'duration_nights',
            'total_price', 'special_requests', 'status', 'status_display',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'guest', 'total_guests', 'number_of_guests', 'created_at', 'updated_at']
    
    def validate_accommodation_id(self, value):
        """Validate that accommodation exists and is active."""
        try:
            accommodation = Accommodation.objects.get(id=value, status='active')
        except Accommodation.DoesNotExist:
            raise serializers.ValidationError('Accommodation not found or not available for booking.')
        return value
    
    def validate(self, data):
        """Custom validation for booking data."""
        check_in_date = data.get('check_in_date')
        check_out_date = data.get('check_out_date')
        
        if check_in_date and check_out_date:
            if check_out_date <= check_in_date:
                raise serializers.ValidationError({
                    'check_out_date': 'Check-out date must be after check-in date.'
                })
        
        # Validate guest counts
        adults = data.get('adults', 0)
        teens = data.get('teens', 0)
        children = data.get('children', 0)
        infants = data.get('infants', 0)
        
        if adults < 1:
            raise serializers.ValidationError({
                'adults': 'At least one adult is required for booking.'
            })
        
        total_guests = adults + teens + children + infants
        if total_guests < 1:
            raise serializers.ValidationError('At least one guest is required.')
        
        return data


class BookingCreateSerializer(BookingSerializer):
    """Serializer for creating bookings."""
    
    class Meta(BookingSerializer.Meta):
        fields = [
            'accommodation_id', 'check_in_date', 'check_out_date',
            'adults', 'teens', 'children', 'infants', 'special_requests'
        ]


class BookingUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating booking status (host/admin only)."""
    
    class Meta:
        model = Booking
        fields = ['status']
    
    def validate_status(self, value):
        """Validate status transitions."""
        if self.instance:
            current_status = self.instance.status
            valid_transitions = {
                'pending': ['confirmed', 'cancelled'],
                'confirmed': ['completed', 'cancelled'],
                'cancelled': [],  # Cannot change from cancelled
                'completed': []   # Cannot change from completed
            }
            
            if value not in valid_transitions.get(current_status, []):
                raise serializers.ValidationError(
                    f'Cannot change status from {current_status} to {value}.'
                )
        
        return value


class ReviewSerializer(serializers.ModelSerializer):
    """Serializer for Review model."""
    guest = UserSerializer(read_only=True)
    accommodation = AccommodationListSerializer(read_only=True)
    star_display = serializers.CharField(read_only=True)
    average_category_rating = serializers.FloatField(read_only=True)

    class Meta:
        model = Review
        fields = [
            'id', 'guest', 'accommodation', 'overall_rating', 'cleanliness_rating',
            'location_rating', 'value_rating', 'communication_rating',
            'checkin_rating', 'accuracy_rating', 'review_text', 'host_response',
            'host_response_date', 'is_verified', 'is_published', 'created_at',
            'updated_at', 'star_display', 'average_category_rating'
        ]
        read_only_fields = [
            'id', 'guest', 'accommodation', 'is_verified', 'created_at',
            'updated_at', 'star_display', 'average_category_rating'
        ]


class ReviewCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating reviews."""
    accommodation_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Review
        fields = [
            'accommodation_id', 'overall_rating', 'cleanliness_rating',
            'location_rating', 'value_rating', 'communication_rating',
            'checkin_rating', 'accuracy_rating', 'review_text'
        ]

    def validate(self, data):
        """Custom validation for review creation."""
        user = self.context['request'].user
        accommodation_id = data['accommodation_id']

        try:
            accommodation = Accommodation.objects.get(id=accommodation_id)
        except Accommodation.DoesNotExist:
            raise serializers.ValidationError("Accommodation not found.")

        # Check if user has completed booking
        completed_booking = Booking.objects.filter(
            guest=user,
            accommodation=accommodation,
            status='completed'
        ).first()

        if not completed_booking:
            raise serializers.ValidationError(
                "You can only review accommodations where you have completed a booking."
            )

        # Check if user has already reviewed
        existing_review = Review.objects.filter(
            guest=user,
            accommodation=accommodation
        ).exists()

        if existing_review:
            raise serializers.ValidationError(
                "You have already reviewed this accommodation."
            )

        data['accommodation'] = accommodation
        data['booking'] = completed_booking
        return data

    def create(self, validated_data):
        """Create review with user and accommodation."""
        accommodation_id = validated_data.pop('accommodation_id')
        accommodation = validated_data.pop('accommodation')
        booking = validated_data.pop('booking')

        review = Review.objects.create(
            guest=self.context['request'].user,
            accommodation=accommodation,
            booking=booking,
            **validated_data
        )
        return review


class HostResponseSerializer(serializers.ModelSerializer):
    """Serializer for host responses to reviews."""

    class Meta:
        model = Review
        fields = ['host_response']

    def validate(self, data):
        """Validate host response."""
        user = self.context['request'].user
        review = self.instance

        if review.accommodation.host != user:
            raise serializers.ValidationError(
                "Only the accommodation host can respond to reviews."
            )

        return data

    def update(self, instance, validated_data):
        """Update review with host response and timestamp."""
        instance.host_response = validated_data.get('host_response', instance.host_response)

        if instance.host_response and not instance.host_response_date:
            from django.utils import timezone
            instance.host_response_date = timezone.now()

        instance.save()
        return instance
