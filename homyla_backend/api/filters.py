"""
Filters for NomadPersia API

This module contains filter classes for API endpoints to enable
advanced filtering and search capabilities.
"""

import django_filters
from accommodations.models import Accommodation
from bookings.models import Booking


class AccommodationFilter(django_filters.FilterSet):
    """
    Filter class for Accommodation model with advanced filtering options.
    """
    
    # Text search filters
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    town = django_filters.CharFilter(lookup_expr='icontains')
    address = django_filters.CharFilter(lookup_expr='icontains')
    
    # Choice filters
    city = django_filters.ChoiceFilter(choices=Accommodation.CITIES)
    accommodation_type = django_filters.ChoiceFilter(choices=Accommodation.ACCOMMODATION_TYPES)
    status = django_filters.ChoiceFilter(choices=Accommodation.STATUS_CHOICES)
    cancellation_policy = django_filters.ChoiceFilter(choices=Accommodation.CANCELLATION_POLICIES)
    check_out_time = django_filters.ChoiceFilter(choices=Accommodation.CHECK_OUT_TIME_CHOICES)
    
    # Numeric range filters
    price_per_night = django_filters.RangeFilter()
    price_min = django_filters.NumberFilter(field_name='price_per_night', lookup_expr='gte')
    price_max = django_filters.NumberFilter(field_name='price_per_night', lookup_expr='lte')
    
    star_rating = django_filters.RangeFilter()
    star_rating_min = django_filters.NumberFilter(field_name='star_rating', lookup_expr='gte')
    star_rating_max = django_filters.NumberFilter(field_name='star_rating', lookup_expr='lte')
    
    standard_capacity = django_filters.RangeFilter()
    standard_capacity_min = django_filters.NumberFilter(field_name='standard_capacity', lookup_expr='gte')
    standard_capacity_max = django_filters.NumberFilter(field_name='standard_capacity', lookup_expr='lte')
    
    max_capacity = django_filters.RangeFilter()
    max_capacity_min = django_filters.NumberFilter(field_name='max_capacity', lookup_expr='gte')
    max_capacity_max = django_filters.NumberFilter(field_name='max_capacity', lookup_expr='lte')
    
    bedrooms = django_filters.RangeFilter()
    bedrooms_min = django_filters.NumberFilter(field_name='bedrooms', lookup_expr='gte')
    bedrooms_max = django_filters.NumberFilter(field_name='bedrooms', lookup_expr='lte')
    
    bathrooms = django_filters.RangeFilter()
    bathrooms_min = django_filters.NumberFilter(field_name='bathrooms', lookup_expr='gte')
    bathrooms_max = django_filters.NumberFilter(field_name='bathrooms', lookup_expr='lte')
    
    property_size = django_filters.RangeFilter()
    property_size_min = django_filters.NumberFilter(field_name='property_size', lookup_expr='gte')
    property_size_max = django_filters.NumberFilter(field_name='property_size', lookup_expr='lte')
    
    extra_guest_fee = django_filters.RangeFilter()
    extra_guest_fee_min = django_filters.NumberFilter(field_name='extra_guest_fee', lookup_expr='gte')
    extra_guest_fee_max = django_filters.NumberFilter(field_name='extra_guest_fee', lookup_expr='lte')
    
    # Boolean filters
    instant_booking = django_filters.BooleanFilter()
    smoking_allowed = django_filters.BooleanFilter()
    same_gender_groups_only = django_filters.BooleanFilter()
    national_id_required = django_filters.BooleanFilter()
    pets_not_allowed = django_filters.BooleanFilter()
    events_not_allowed = django_filters.BooleanFilter()
    no_24hour_checkin = django_filters.BooleanFilter()
    
    # Date filters
    created_after = django_filters.DateFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateFilter(field_name='created_at', lookup_expr='lte')
    updated_after = django_filters.DateFilter(field_name='updated_at', lookup_expr='gte')
    updated_before = django_filters.DateFilter(field_name='updated_at', lookup_expr='lte')
    
    # Host filters
    host = django_filters.NumberFilter(field_name='host__id')
    host_username = django_filters.CharFilter(field_name='host__username', lookup_expr='icontains')
    
    # Amenities filter (contains any of the specified amenities)
    amenities = django_filters.CharFilter(method='filter_amenities')
    
    def filter_amenities(self, queryset, name, value):
        """
        Filter accommodations that contain any of the specified amenities.
        Expects comma-separated list of amenities.
        """
        if value:
            amenities_list = [amenity.strip() for amenity in value.split(',')]
            for amenity in amenities_list:
                queryset = queryset.filter(amenities__icontains=amenity)
        return queryset
    
    class Meta:
        model = Accommodation
        fields = {
            'name': ['exact', 'icontains'],
            'city': ['exact'],
            'accommodation_type': ['exact'],
            'price_per_night': ['exact', 'gte', 'lte', 'range'],
            'star_rating': ['exact', 'gte', 'lte', 'range'],
            'standard_capacity': ['exact', 'gte', 'lte'],
            'max_capacity': ['exact', 'gte', 'lte'],
            'bedrooms': ['exact', 'gte', 'lte'],
            'bathrooms': ['exact', 'gte', 'lte'],
            'instant_booking': ['exact'],
            'status': ['exact'],
            'host': ['exact'],
        }


class BookingFilter(django_filters.FilterSet):
    """
    Filter class for Booking model with advanced filtering options.
    """
    
    # Status filter
    status = django_filters.ChoiceFilter(choices=Booking.STATUS_CHOICES)
    
    # Date filters
    check_in_after = django_filters.DateFilter(field_name='check_in_date', lookup_expr='gte')
    check_in_before = django_filters.DateFilter(field_name='check_in_date', lookup_expr='lte')
    check_out_after = django_filters.DateFilter(field_name='check_out_date', lookup_expr='gte')
    check_out_before = django_filters.DateFilter(field_name='check_out_date', lookup_expr='lte')
    
    created_after = django_filters.DateFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateFilter(field_name='created_at', lookup_expr='lte')
    
    # Guest count filters
    adults = django_filters.RangeFilter()
    adults_min = django_filters.NumberFilter(field_name='adults', lookup_expr='gte')
    adults_max = django_filters.NumberFilter(field_name='adults', lookup_expr='lte')
    
    total_guests = django_filters.RangeFilter()
    total_guests_min = django_filters.NumberFilter(field_name='total_guests', lookup_expr='gte')
    total_guests_max = django_filters.NumberFilter(field_name='total_guests', lookup_expr='lte')
    
    # Price filters
    total_price = django_filters.RangeFilter()
    total_price_min = django_filters.NumberFilter(field_name='total_price', lookup_expr='gte')
    total_price_max = django_filters.NumberFilter(field_name='total_price', lookup_expr='lte')
    
    # Accommodation filters
    accommodation = django_filters.NumberFilter(field_name='accommodation__id')
    accommodation_name = django_filters.CharFilter(field_name='accommodation__name', lookup_expr='icontains')
    accommodation_city = django_filters.ChoiceFilter(
        field_name='accommodation__city',
        choices=Accommodation.CITIES
    )
    accommodation_type = django_filters.ChoiceFilter(
        field_name='accommodation__accommodation_type',
        choices=Accommodation.ACCOMMODATION_TYPES
    )
    
    # User filters
    guest = django_filters.NumberFilter(field_name='guest__id')
    guest_username = django_filters.CharFilter(field_name='guest__username', lookup_expr='icontains')
    host = django_filters.NumberFilter(field_name='accommodation__host__id')
    host_username = django_filters.CharFilter(field_name='accommodation__host__username', lookup_expr='icontains')
    
    # Duration filter
    duration_nights = django_filters.RangeFilter(method='filter_duration')
    duration_min = django_filters.NumberFilter(method='filter_duration_min')
    duration_max = django_filters.NumberFilter(method='filter_duration_max')
    
    def filter_duration(self, queryset, name, value):
        """Filter by booking duration range."""
        if value:
            if value.start:
                queryset = queryset.extra(
                    where=["check_out_date - check_in_date >= %s"],
                    params=[value.start]
                )
            if value.stop:
                queryset = queryset.extra(
                    where=["check_out_date - check_in_date <= %s"],
                    params=[value.stop]
                )
        return queryset
    
    def filter_duration_min(self, queryset, name, value):
        """Filter by minimum booking duration."""
        if value:
            queryset = queryset.extra(
                where=["check_out_date - check_in_date >= %s"],
                params=[value]
            )
        return queryset
    
    def filter_duration_max(self, queryset, name, value):
        """Filter by maximum booking duration."""
        if value:
            queryset = queryset.extra(
                where=["check_out_date - check_in_date <= %s"],
                params=[value]
            )
        return queryset
    
    class Meta:
        model = Booking
        fields = {
            'status': ['exact'],
            'check_in_date': ['exact', 'gte', 'lte'],
            'check_out_date': ['exact', 'gte', 'lte'],
            'adults': ['exact', 'gte', 'lte'],
            'total_guests': ['exact', 'gte', 'lte'],
            'total_price': ['exact', 'gte', 'lte'],
            'accommodation': ['exact'],
            'guest': ['exact'],
        }
