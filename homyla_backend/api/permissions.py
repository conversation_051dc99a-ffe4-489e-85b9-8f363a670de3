"""
Custom permissions for NomadPersia API

This module contains custom permission classes for role-based access control
in the API endpoints.
"""

from rest_framework import permissions


class IsHostOrAdmin(permissions.BasePermission):
    """
    Permission that allows access only to hosts or admin users.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        return (
            request.user.profile.is_host() or
            request.user.profile.is_admin_user() or
            request.user.is_superuser
        )


class IsGuestOrAdmin(permissions.BasePermission):
    """
    Permission that allows access only to guests or admin users.
    """
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        return (
            request.user.profile.is_guest() or
            request.user.profile.is_admin_user() or
            request.user.is_superuser
        )


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Permission that allows access only to the owner of the object or admin users.
    """
    
    def has_object_permission(self, request, view, obj):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Admin users have full access
        if request.user.profile.is_admin_user() or request.user.is_superuser:
            return True
        
        # Check ownership based on object type
        if hasattr(obj, 'host'):
            # For accommodations
            return obj.host == request.user
        elif hasattr(obj, 'guest'):
            # For bookings - guest can read/cancel, host can update status
            if request.method in permissions.SAFE_METHODS or view.action == 'cancel':
                return obj.guest == request.user
            else:
                return obj.accommodation.host == request.user
        elif hasattr(obj, 'user'):
            # For profiles
            return obj.user == request.user
        else:
            # For user objects
            return obj == request.user


class IsHostOfAccommodation(permissions.BasePermission):
    """
    Permission that allows access only to the host of the accommodation.
    """
    
    def has_object_permission(self, request, view, obj):
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Admin users have full access
        if request.user.profile.is_admin_user() or request.user.is_superuser:
            return True
        
        # For booking objects, check if user is the host of the accommodation
        if hasattr(obj, 'accommodation'):
            return obj.accommodation.host == request.user
        
        return False


class ReadOnlyOrOwner(permissions.BasePermission):
    """
    Permission that allows read-only access to everyone,
    but write access only to the owner or admin.
    """
    
    def has_permission(self, request, view):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for authenticated users
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Admin users have full access
        if request.user.profile.is_admin_user() or request.user.is_superuser:
            return True
        
        # Write permissions only to the owner
        if hasattr(obj, 'host'):
            return obj.host == request.user
        elif hasattr(obj, 'guest'):
            return obj.guest == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        else:
            return obj == request.user


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Permission that allows read-only access to everyone,
    but write access only to admin users.
    """
    
    def has_permission(self, request, view):
        # Read permissions for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for admin users
        return (
            request.user and 
            request.user.is_authenticated and
            (request.user.profile.is_admin_user() or request.user.is_superuser)
        )
