"""
Photo models for accommodations.
This module contains models for handling accommodation photos and galleries.
"""

import os
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator


def accommodation_photo_upload_path(instance, filename):
    """Generate upload path for accommodation photos."""
    # Create path: accommodations/accommodation_id/photos/filename
    return f'accommodations/{instance.accommodation.id}/photos/{filename}'


class AccommodationPhoto(models.Model):
    """
    Model for storing accommodation photos.
    """
    
    PHOTO_TYPES = [
        ('main', _('Main Photo')),
        ('interior', _('Interior View')),
        ('bedroom', _('Bedroom')),
        ('kitchen', _('Kitchen')),
        ('bathroom', _('Bathroom')),
        ('exterior', _('Exterior View')),
        ('amenity', _('Amenity')),
        ('other', _('Other')),
    ]
    
    accommodation = models.ForeignKey(
        'Accommodation',
        on_delete=models.CASCADE,
        related_name='photos',
        help_text=_('Accommodation this photo belongs to')
    )
    
    image = models.ImageField(
        upload_to=accommodation_photo_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])],
        help_text=_('Photo file (JPG, PNG, WebP)')
    )
    
    photo_type = models.CharField(
        max_length=20,
        choices=PHOTO_TYPES,
        default='other',
        help_text=_('Type/category of this photo')
    )
    
    caption = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Optional caption for this photo')
    )
    
    order = models.PositiveSmallIntegerField(
        default=0,
        help_text=_('Display order (0 = first)')
    )
    
    is_main = models.BooleanField(
        default=False,
        help_text=_('Is this the main/featured photo?')
    )
    
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['order', 'uploaded_at']
        verbose_name = _('Accommodation Photo')
        verbose_name_plural = _('Accommodation Photos')
    
    def __str__(self):
        return f"{self.accommodation.name} - {self.get_photo_type_display()}"
    
    def save(self, *args, **kwargs):
        # If this is set as main photo, unset other main photos for this accommodation
        if self.is_main:
            AccommodationPhoto.objects.filter(
                accommodation=self.accommodation,
                is_main=True
            ).exclude(pk=self.pk).update(is_main=False)
        
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        # Delete the actual file when the model instance is deleted
        if self.image:
            if os.path.isfile(self.image.path):
                os.remove(self.image.path)
        super().delete(*args, **kwargs)


class AccommodationGallery(models.Model):
    """
    Model for managing accommodation photo galleries.
    This is a helper model for organizing photos.
    """
    
    accommodation = models.OneToOneField(
        'Accommodation',
        on_delete=models.CASCADE,
        related_name='gallery',
        help_text=_('Accommodation this gallery belongs to')
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = _('Accommodation Gallery')
        verbose_name_plural = _('Accommodation Galleries')
    
    def __str__(self):
        return f"Gallery for {self.accommodation.name}"
    
    def get_main_photo(self):
        """Get the main photo for this accommodation."""
        return self.accommodation.photos.filter(is_main=True).first()
    
    def get_photos_by_type(self, photo_type):
        """Get photos of a specific type."""
        return self.accommodation.photos.filter(photo_type=photo_type)
    
    def get_all_photos(self):
        """Get all photos ordered by display order."""
        return self.accommodation.photos.all()
    
    def has_photos(self):
        """Check if this gallery has any photos."""
        return self.accommodation.photos.exists()
    
    def photo_count(self):
        """Get the total number of photos."""
        return self.accommodation.photos.count()
