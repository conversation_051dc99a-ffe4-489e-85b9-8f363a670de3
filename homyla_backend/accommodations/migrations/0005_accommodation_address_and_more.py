# Generated by Django 5.0.14 on 2025-07-21 11:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "accommodations",
            "0004_accommodationavailability_accommodationpricing_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="accommodation",
            name="address",
            field=models.TextField(
                default="Address to be updated",
                help_text="Complete property address including street, neighborhood, and postal code",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="accommodation",
            name="events_not_allowed",
            field=models.BooleanField(
                default=True, help_text="Events and parties are prohibited"
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="geolocation",
            field=models.CharField(
                blank=True,
                help_text="GPS coordinates in format: latitude,longitude",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="national_id_required",
            field=models.<PERSON><PERSON>anField(
                default=False, help_text="Guests must present national ID upon check-in"
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="no_24hour_checkin",
            field=models.BooleanField(
                default=True, help_text="24-hour check-in is not available"
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="pets_not_allowed",
            field=models.BooleanField(
                default=True, help_text="Pets are prohibited in the property"
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="reservation_phone",
            field=models.CharField(
                default="Phone to be updated",
                help_text="Phone number for reservations and guest contact",
                max_length=20,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="accommodation",
            name="same_gender_groups_only",
            field=models.BooleanField(
                default=False, help_text="Only accept all-male or all-female groups"
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="smoking_allowed",
            field=models.BooleanField(
                default=False, help_text="Smoking is permitted in the property"
            ),
        ),
    ]
