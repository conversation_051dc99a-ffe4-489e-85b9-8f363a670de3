# Generated manually for enhanced accommodation model

from django.db import migrations, models
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('accommodations', '0001_initial'),
    ]

    operations = [
        # Update accommodation type choices and max_length
        migrations.AlterField(
            model_name='accommodation',
            name='accommodation_type',
            field=models.CharField(
                choices=[
                    ('villa_suite', 'Villa & Suite'),
                    ('apartment', 'Apartment'),
                    ('cottage', 'Cottage')
                ],
                help_text='Type of accommodation',
                max_length=15
            ),
        ),
        
        # Add new capacity fields
        migrations.AddField(
            model_name='accommodation',
            name='standard_capacity',
            field=models.PositiveSmallIntegerField(
                default=2,
                help_text='Standard number of guests included in base price',
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(20)
                ]
            ),
        ),
        migrations.AddField(
            model_name='accommodation',
            name='max_capacity',
            field=models.PositiveSmallIntegerField(
                blank=True,
                help_text='Maximum number of guests allowed (optional)',
                null=True,
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(30)
                ]
            ),
        ),
        migrations.AddField(
            model_name='accommodation',
            name='bedrooms',
            field=models.PositiveSmallIntegerField(
                default=1,
                help_text='Number of bedrooms',
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(10)
                ]
            ),
        ),
        migrations.AddField(
            model_name='accommodation',
            name='bathrooms',
            field=models.PositiveSmallIntegerField(
                default=1,
                help_text='Number of bathrooms',
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(10)
                ]
            ),
        ),
        migrations.AddField(
            model_name='accommodation',
            name='property_size',
            field=models.PositiveIntegerField(
                blank=True,
                help_text='Property size in square meters',
                null=True,
                validators=[django.core.validators.MinValueValidator(20)]
            ),
        ),
        
        # Add pricing fields
        migrations.AddField(
            model_name='accommodation',
            name='extra_guest_fee',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text='Additional fee per guest above standard capacity',
                max_digits=10,
                validators=[django.core.validators.MinValueValidator(0)]
            ),
        ),
        
        # Add booking policy fields
        migrations.AddField(
            model_name='accommodation',
            name='instant_booking',
            field=models.BooleanField(
                default=False,
                help_text='Allow instant booking without host approval'
            ),
        ),
        migrations.AddField(
            model_name='accommodation',
            name='cancellation_policy',
            field=models.CharField(
                choices=[
                    ('flexible', 'Flexible - 24 hours before'),
                    ('moderate', 'Moderate - 5 days before'),
                    ('strict', 'Strict - 10 days before')
                ],
                default='moderate',
                help_text='Cancellation policy for guests',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='accommodation',
            name='check_in_time',
            field=models.CharField(
                blank=True,
                help_text='Latest check-in time (e.g., 22:00)',
                max_length=5
            ),
        ),
    ]
