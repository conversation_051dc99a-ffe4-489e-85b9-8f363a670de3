# Generated manually for photo models

from django.db import migrations, models
import django.core.validators
import accommodations.models


class Migration(migrations.Migration):

    dependencies = [
        ('accommodations', '0002_enhance_accommodation_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccommodationPhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Photo file (JPG, PNG, WebP)', upload_to=accommodations.models.accommodation_photo_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])])),
                ('photo_type', models.CharField(choices=[('main', 'Main Photo'), ('interior', 'Interior View'), ('bedroom', 'Bedroom'), ('kitchen', 'Kitchen'), ('bathroom', 'Bathroom'), ('exterior', 'Exterior View'), ('amenity', 'Amenity'), ('other', 'Other')], default='other', help_text='Type/category of this photo', max_length=20)),
                ('caption', models.CharField(blank=True, help_text='Optional caption for this photo', max_length=200)),
                ('order', models.PositiveSmallIntegerField(default=0, help_text='Display order (0 = first)')),
                ('is_main', models.BooleanField(default=False, help_text='Is this the main/featured photo?')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('accommodation', models.ForeignKey(help_text='Accommodation this photo belongs to', on_delete=models.deletion.CASCADE, related_name='photos', to='accommodations.accommodation')),
            ],
            options={
                'verbose_name': 'Accommodation Photo',
                'verbose_name_plural': 'Accommodation Photos',
                'ordering': ['order', 'uploaded_at'],
            },
        ),
    ]
