# Generated by Django 5.0.14 on 2025-07-21 13:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accommodations", "0007_accommodation_additional_policies_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="accommodation",
            name="average_rating",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Average rating from all reviews (calculated automatically)",
                max_digits=3,
                null=True,
                verbose_name="Average Rating",
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="total_reviews",
            field=models.PositiveIntegerField(
                default=0,
                help_text="Total number of published reviews (calculated automatically)",
                verbose_name="Total Reviews",
            ),
        ),
    ]
