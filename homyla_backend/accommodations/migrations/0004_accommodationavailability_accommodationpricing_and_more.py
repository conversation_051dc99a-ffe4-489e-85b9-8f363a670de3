# Generated by Django 5.0.14 on 2025-07-19 01:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accommodations", "0003_add_photo_models"),
    ]

    operations = [
        migrations.CreateModel(
            name="AccommodationAvailability",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "start_date",
                    models.DateField(help_text="Start date for blocked period"),
                ),
                ("end_date", models.DateField(help_text="End date for blocked period")),
                (
                    "is_blocked",
                    models.BooleanField(
                        default=True,
                        help_text="Whether these dates are blocked for booking",
                    ),
                ),
                (
                    "minimum_nights",
                    models.PositiveSmallIntegerField(
                        default=1,
                        help_text="Minimum number of nights required for this period",
                    ),
                ),
                (
                    "reason",
                    models.CharField(
                        blank=True,
                        help_text="Reason for blocking these dates",
                        max_length=200,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "accommodation",
                    models.ForeignKey(
                        help_text="Accommodation this availability rule applies to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="availability_rules",
                        to="accommodations.accommodation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Accommodation Availability",
                "verbose_name_plural": "Accommodation Availability Rules",
                "ordering": ["start_date"],
            },
        ),
        migrations.CreateModel(
            name="AccommodationPricing",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "pricing_type",
                    models.CharField(
                        choices=[
                            ("weekday", "Weekday Pricing"),
                            ("weekend", "Weekend Pricing"),
                            ("holiday", "Public Holiday"),
                            ("peak_season", "Peak Season"),
                            ("custom", "Custom Date Range"),
                        ],
                        default="custom",
                        help_text="Type of pricing rule",
                        max_length=20,
                    ),
                ),
                (
                    "start_date",
                    models.DateField(help_text="Start date for this pricing rule"),
                ),
                (
                    "end_date",
                    models.DateField(help_text="End date for this pricing rule"),
                ),
                (
                    "price_per_night",
                    models.PositiveIntegerField(
                        help_text="Price per night in Toman for this date range"
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        help_text="Optional description for this pricing rule",
                        max_length=200,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this pricing rule is active"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "accommodation",
                    models.ForeignKey(
                        help_text="Accommodation this pricing applies to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="date_pricing",
                        to="accommodations.accommodation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Accommodation Pricing",
                "verbose_name_plural": "Accommodation Pricing Rules",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="GuestCapacityRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "age_category",
                    models.CharField(
                        choices=[
                            ("infant", "Infant (0-2 years)"),
                            ("child", "Child (3-12 years)"),
                            ("teen", "Teen (13-17 years)"),
                            ("adult", "Adult (18+ years)"),
                        ],
                        help_text="Age category for this rule",
                        max_length=20,
                    ),
                ),
                (
                    "price_per_night",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Additional price per night for this age category (0 = free)",
                    ),
                ),
                (
                    "is_counted_in_capacity",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this age category counts towards total capacity",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "accommodation",
                    models.ForeignKey(
                        help_text="Accommodation this capacity rule applies to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="capacity_rules",
                        to="accommodations.accommodation",
                    ),
                ),
            ],
            options={
                "verbose_name": "Guest Capacity Rule",
                "verbose_name_plural": "Guest Capacity Rules",
                "unique_together": {("accommodation", "age_category")},
            },
        ),
    ]
