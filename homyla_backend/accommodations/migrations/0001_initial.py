# Generated by Django 5.0.14 on 2025-07-02 09:07

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Accommodation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the accommodation", max_length=200
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed description of the accommodation"
                    ),
                ),
                (
                    "accommodation_type",
                    models.CharField(
                        choices=[
                            ("hotel", "Hotel"),
                            ("villa", "Villa"),
                            ("suite", "Suite"),
                        ],
                        help_text="Type of accommodation",
                        max_length=10,
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        choices=[
                            ("tehran", "Tehran"),
                            ("shiraz", "Shiraz"),
                            ("mashhad", "Mashhad"),
                        ],
                        help_text="City where accommodation is located",
                        max_length=10,
                    ),
                ),
                (
                    "price_per_night",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Price per night in Iranian Rial",
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "star_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "1 Star"),
                            (2, "2 Stars"),
                            (3, "3 Stars"),
                            (4, "4 Stars"),
                            (5, "5 Stars"),
                        ],
                        help_text="Star rating (only applicable for hotels)",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "amenities",
                    models.TextField(
                        blank=True,
                        help_text="List of amenities (comma-separated for MVP)",
                    ),
                ),
                (
                    "image_placeholder",
                    models.CharField(
                        default="[Accommodation Image Placeholder]",
                        help_text="Text placeholder for accommodation images (MVP only)",
                        max_length=500,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Approval"),
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        help_text="Current status of the accommodation listing",
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "host",
                    models.ForeignKey(
                        help_text="Host who owns this accommodation",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="accommodations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Accommodation",
                "verbose_name_plural": "Accommodations",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["city", "accommodation_type"],
                        name="accommodati_city_cdb54c_idx",
                    ),
                    models.Index(
                        fields=["price_per_night"],
                        name="accommodati_price_p_f92f02_idx",
                    ),
                    models.Index(
                        fields=["star_rating"], name="accommodati_star_ra_94504f_idx"
                    ),
                    models.Index(
                        fields=["status"], name="accommodati_status_de8589_idx"
                    ),
                    models.Index(
                        fields=["host"], name="accommodati_host_id_8c3a55_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="accommodati_created_2cd983_idx"
                    ),
                ],
            },
        ),
    ]
