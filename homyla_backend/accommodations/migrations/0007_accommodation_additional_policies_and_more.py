# Generated by Django 5.0.14 on 2025-07-21 12:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accommodations", "0006_accommodation_town"),
    ]

    operations = [
        migrations.AddField(
            model_name="accommodation",
            name="additional_policies",
            field=models.TextField(
                blank=True, help_text="Additional house rules and policies for guests"
            ),
        ),
        migrations.AddField(
            model_name="accommodation",
            name="check_out_time",
            field=models.CharField(
                choices=[
                    ("12:00", "12:00 PM"),
                    ("14:00", "2:00 PM"),
                    ("16:00", "4:00 PM"),
                ],
                default="12:00",
                help_text="Required check-out time",
                max_length=5,
            ),
        ),
    ]
