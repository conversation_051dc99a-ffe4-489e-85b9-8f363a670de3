# Generated manually for price field increase

from django.db import migrations, models
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('accommodations', '0008_accommodation_average_rating_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='accommodation',
            name='price_per_night',
            field=models.DecimalField(decimal_places=2, help_text='Price per night (supports international currencies)', max_digits=14, validators=[django.core.validators.MinValueValidator(0)]),
        ),
    ]
