"""
URL patterns for the accommodations app.

This module defines URL patterns for accommodation search, management,
and CRUD operations for different user roles.
"""

from django.urls import path
from . import views

app_name = 'accommodations'

urlpatterns = [
    # Public accommodation views
    path('', views.search_view, name='search'),
    path('<int:pk>/', views.accommodation_detail_view, name='detail'),

    # Host accommodation management
    path('host/', views.host_accommodations_view, name='host_accommodations'),
    path('host/create/', views.create_accommodation_view, name='create'),
    path('host/<int:pk>/edit/', views.edit_accommodation_view, name='edit'),
    path('host/<int:pk>/toggle-status/', views.toggle_accommodation_status_view, name='toggle_status'),
]
