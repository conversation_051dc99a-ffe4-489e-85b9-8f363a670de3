"""
Management command to set up complex pricing test scenario
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from accommodations.models import Accommodation, AccommodationPricing
from datetime import date, timedelta
from decimal import Decimal


class Command(BaseCommand):
    help = 'Set up complex pricing test scenario with seasonal pricing and extra guest fees'

    def add_arguments(self, parser):
        parser.add_argument(
            '--accommodation-id',
            type=int,
            help='Specific accommodation ID to set up pricing for',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up complex pricing test scenario...'))

        # Get accommodation to set up
        if options['accommodation_id']:
            try:
                accommodation = Accommodation.objects.get(id=options['accommodation_id'])
            except Accommodation.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Accommodation with ID {options["accommodation_id"]} not found')
                )
                return
        else:
            accommodation = Accommodation.objects.first()
            if not accommodation:
                self.stdout.write(self.style.ERROR('No accommodations found'))
                return

        self.stdout.write(f'Setting up pricing for: {accommodation.name}')

        # Set up the exact scenario from the issue description
        # Base rental price: $1,000 per night (convert to IRR)
        base_price_usd = 1000
        usd_to_irr = 50000  # Approximate conversion rate
        base_price = base_price_usd * usd_to_irr

        # Update accommodation settings
        accommodation.price_per_night = base_price
        accommodation.standard_capacity = 6  # Standard capacity: 6 guests
        accommodation.max_capacity = 8       # Maximum capacity: 8 guests
        accommodation.extra_guest_fee = 500 * usd_to_irr  # Extra guest fee: $500 per guest
        accommodation.save()

        self.stdout.write(f'✅ Updated accommodation settings:')
        self.stdout.write(f'   Base price: {accommodation.price_per_night:,} IRR (${base_price_usd})')
        self.stdout.write(f'   Standard capacity: {accommodation.standard_capacity} guests')
        self.stdout.write(f'   Maximum capacity: {accommodation.max_capacity} guests')
        self.stdout.write(f'   Extra guest fee: {accommodation.extra_guest_fee:,} IRR ($500) per guest')

        today = date.today()

        # Create holiday pricing rule (50% increase)
        holiday_start = today + timedelta(days=10)
        holiday_end = today + timedelta(days=15)
        holiday_price = int(accommodation.price_per_night * Decimal('1.5'))  # 50% increase

        holiday_pricing, created = AccommodationPricing.objects.update_or_create(
            accommodation=accommodation,
            pricing_type='holiday',
            start_date=holiday_start,
            end_date=holiday_end,
            defaults={
                'price_per_night': holiday_price,
                'description': 'Holiday Premium (50% increase)',
                'is_active': True
            }
        )

        self.stdout.write(f'✅ Holiday pricing: {holiday_start} to {holiday_end}')
        self.stdout.write(f'   Holiday price: {holiday_price:,} IRR (${holiday_price // usd_to_irr}) per night')

        # Create weekend pricing rule (20% increase)
        weekend_price = int(accommodation.price_per_night * Decimal('1.2'))

        # Create weekend pricing for the next 8 weeks
        current_date = today
        end_date = today + timedelta(days=56)  # 8 weeks
        weekend_count = 0

        while current_date <= end_date:
            # Check if it's Friday or Saturday (weekend in Iran)
            if current_date.weekday() in [4, 5]:  # Friday=4, Saturday=5
                weekend_start = current_date
                weekend_end = current_date + timedelta(days=1)

                weekend_pricing, created = AccommodationPricing.objects.update_or_create(
                    accommodation=accommodation,
                    pricing_type='weekend',
                    start_date=weekend_start,
                    end_date=weekend_end,
                    defaults={
                        'price_per_night': weekend_price,
                        'description': f'Weekend Premium - {weekend_start.strftime("%B %d")}',
                        'is_active': True
                    }
                )

                if created:
                    weekend_count += 1

            current_date += timedelta(days=1)

        self.stdout.write(f'✅ Weekend pricing: {weekend_count} weekend periods')
        self.stdout.write(f'   Weekend price: {weekend_price:,} IRR (${weekend_price // usd_to_irr}) per night')

        # Create peak season pricing (30% increase)
        peak_start = today + timedelta(days=20)
        peak_end = today + timedelta(days=35)
        peak_price = int(accommodation.price_per_night * Decimal('1.3'))

        peak_pricing, created = AccommodationPricing.objects.update_or_create(
            accommodation=accommodation,
            pricing_type='peak_season',
            start_date=peak_start,
            end_date=peak_end,
            defaults={
                'price_per_night': peak_price,
                'description': 'Peak Season (30% increase)',
                'is_active': True
            }
        )

        self.stdout.write(f'✅ Peak season pricing: {peak_start} to {peak_end}')
        self.stdout.write(f'   Peak price: {peak_price:,} IRR (${peak_price // usd_to_irr}) per night')

        # Test the pricing calculation
        self.stdout.write('\n=== TESTING COMPLEX PRICING SCENARIO ===')

        # Test scenario: 8 guests, 3 nights during holiday period
        test_start = holiday_start
        test_end = holiday_start + timedelta(days=3)
        guest_count = 8

        self.stdout.write(f'Test booking: {test_start} to {test_end} ({guest_count} guests)')

        # Calculate expected price
        nights = 3
        accommodation_cost = holiday_price * nights
        extra_guests = guest_count - accommodation.standard_capacity
        extra_guest_cost = extra_guests * accommodation.extra_guest_fee
        expected_total = accommodation_cost + extra_guest_cost

        self.stdout.write(f'Expected calculation:')
        self.stdout.write(f'  Holiday price: {holiday_price:,} IRR × {nights} nights = {accommodation_cost:,} IRR')
        self.stdout.write(f'  Extra guests: {extra_guests} × {accommodation.extra_guest_fee:,} IRR = {extra_guest_cost:,} IRR')
        self.stdout.write(f'  Total expected: {expected_total:,} IRR (${expected_total // usd_to_irr})')

        # Test actual calculation
        calculated_total = accommodation.calculate_total_price(
            test_start, test_end, guest_count,
            adults=guest_count, teens=0, children=0, infants=0
        )

        self.stdout.write(f'Actual calculation: {calculated_total:,} IRR')

        if calculated_total == expected_total:
            self.stdout.write(self.style.SUCCESS('✅ Pricing calculation is CORRECT!'))
        else:
            self.stdout.write(self.style.ERROR(f'❌ Pricing calculation is INCORRECT!'))
            self.stdout.write(f'   Difference: {abs(calculated_total - expected_total):,} IRR')

        self.stdout.write('\n=== SETUP COMPLETE ===')
        self.stdout.write(f'Accommodation ID: {accommodation.id}')
        self.stdout.write(f'You can now test the booking system at:')
        self.stdout.write(f'http://localhost:8082/accommodations/{accommodation.id}/book/')
        self.stdout.write(f'')
        self.stdout.write(f'Test dates: {test_start} to {test_end}')
        self.stdout.write(f'Test guests: {guest_count} guests')
        self.stdout.write(f'Expected total: {expected_total:,} IRR (${expected_total // usd_to_irr})')
