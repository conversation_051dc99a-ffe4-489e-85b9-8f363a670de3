"""
Management command to load comprehensive sample data for NomadPersia platform.

This command creates sample users, accommodations, bookings, and other data
for testing and demonstration purposes.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timed<PERSON>ta
from decimal import Decimal

from accounts.models import UserProfile
from accommodations.models import Accommodation
from bookings.models import Booking


class Command(BaseCommand):
    help = 'Load comprehensive sample data for NomadPersia platform'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing sample data before creating new data',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing sample data...')
            # Clear in correct order due to foreign key constraints
            Booking.objects.filter(guest__username__startswith='guest').delete()
            Accommodation.objects.filter(host__username__startswith='host').delete()
            User.objects.filter(username__startswith='host').delete()
            User.objects.filter(username__startswith='guest').delete()
            self.stdout.write(self.style.SUCCESS('Sample data cleared.'))

        self.stdout.write('Creating comprehensive sample data...')

        # Create sample hosts
        hosts_data = [
            {
                'username': 'host_tehran',
                'email': '<EMAIL>',
                'first_name': 'Ahmad',
                'last_name': 'Hosseini',
                'password': 'host123',
                'phone': '+989121234567'
            },
            {
                'username': 'host_shiraz',
                'email': '<EMAIL>',
                'first_name': 'Maryam',
                'last_name': 'Karimi',
                'password': 'host123',
                'phone': '+989131234567'
            },
            {
                'username': 'host_mashhad',
                'email': '<EMAIL>',
                'first_name': 'Ali',
                'last_name': 'Rezaei',
                'password': 'host123',
                'phone': '+989151234567'
            }
        ]

        hosts = []
        for host_data in hosts_data:
            user, created = User.objects.get_or_create(
                username=host_data['username'],
                defaults={
                    'email': host_data['email'],
                    'first_name': host_data['first_name'],
                    'last_name': host_data['last_name'],
                }
            )
            if created:
                user.set_password(host_data['password'])
                user.save()
                
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'role': 'host',
                    'phone_number': host_data['phone']
                }
            )
            hosts.append(user)
            
            if created:
                self.stdout.write(f'Created host: {user.username}')

        # Create sample guests
        guests_data = [
            {
                'username': 'guest_sara',
                'email': '<EMAIL>',
                'first_name': 'Sara',
                'last_name': 'Ahmadi',
                'password': 'guest123',
                'phone': '+989123456789'
            },
            {
                'username': 'guest_reza',
                'email': '<EMAIL>',
                'first_name': 'Reza',
                'last_name': 'Mohammadi',
                'password': 'guest123',
                'phone': '+989987654321'
            },
            {
                'username': 'guest_elena',
                'email': '<EMAIL>',
                'first_name': 'Elena',
                'last_name': 'Rossi',
                'password': 'guest123',
                'phone': '+393123456789'
            }
        ]

        guests = []
        for guest_data in guests_data:
            user, created = User.objects.get_or_create(
                username=guest_data['username'],
                defaults={
                    'email': guest_data['email'],
                    'first_name': guest_data['first_name'],
                    'last_name': guest_data['last_name'],
                }
            )
            if created:
                user.set_password(guest_data['password'])
                user.save()
                
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'role': 'guest',
                    'phone_number': guest_data['phone']
                }
            )
            guests.append(user)
            
            if created:
                self.stdout.write(f'Created guest: {user.username}')

        # Create comprehensive accommodations
        accommodations_data = [
            # Tehran accommodations
            {
                'name': 'Grand Azadi Hotel Tehran',
                'description': 'Luxury 5-star hotel in the heart of Tehran with panoramic city views. Located near major business districts and cultural attractions including the National Museum and Golestan Palace.',
                'accommodation_type': 'hotel',
                'city': 'tehran',
                'price_per_night': Decimal('3500000'),
                'star_rating': 5,
                'amenities': 'WiFi, Pool, Spa, Gym, Restaurant, Room Service, Parking, Airport Shuttle, Business Center, Conference Rooms',
                'image_placeholder': '[Luxury hotel exterior with modern architecture, elegant lobby with Persian carpets, spacious rooms with city views, rooftop pool]',
                'host': hosts[0],
                'status': 'active'
            },
            {
                'name': 'Tehran Heritage Villa',
                'description': 'Beautiful traditional Persian villa in upscale Niavaran district. Perfect for families seeking authentic Iranian hospitality with modern amenities.',
                'accommodation_type': 'villa',
                'city': 'tehran',
                'price_per_night': Decimal('2200000'),
                'star_rating': None,
                'amenities': 'WiFi, Kitchen, Garden, Parking, Traditional Decor, Family Friendly, BBQ Area, Courtyard',
                'image_placeholder': '[Traditional Persian villa with beautiful garden, authentic interior design, family dining area, peaceful courtyard]',
                'host': hosts[0],
                'status': 'active'
            },
            {
                'name': 'Modern Tehran Suite',
                'description': 'Contemporary suite in trendy Elahieh neighborhood. Ideal for business travelers and couples seeking luxury and convenience.',
                'accommodation_type': 'suite',
                'city': 'tehran',
                'price_per_night': Decimal('1800000'),
                'star_rating': None,
                'amenities': 'WiFi, Kitchen, Gym Access, Parking, Modern Design, City View, Balcony',
                'image_placeholder': '[Modern suite with contemporary design, city skyline views, fully equipped kitchen, stylish living area]',
                'host': hosts[0],
                'status': 'active'
            },
            # Shiraz accommodations
            {
                'name': 'Hafez Garden Hotel',
                'description': 'Boutique hotel inspired by Persian poetry, located near Hafez Tomb and Eram Garden. Experience the cultural heart of Shiraz with traditional architecture and modern comfort.',
                'accommodation_type': 'hotel',
                'city': 'shiraz',
                'price_per_night': Decimal('1800000'),
                'star_rating': 4,
                'amenities': 'WiFi, Restaurant, Traditional Architecture, Cultural Tours, Garden View, Poetry Library, Persian Breakfast',
                'image_placeholder': '[Historic hotel with Persian architecture, traditional courtyard with fountain, rooms with cultural motifs, poetry corner]',
                'host': hosts[1],
                'status': 'active'
            },
            {
                'name': 'Persepolis Villa Shiraz',
                'description': 'Elegant villa with views of Shiraz mountains. Perfect base for exploring Persepolis and other historical sites. Features traditional Persian garden design.',
                'accommodation_type': 'villa',
                'city': 'shiraz',
                'price_per_night': Decimal('2500000'),
                'star_rating': None,
                'amenities': 'WiFi, Garden, Mountain View, Cultural Proximity, Traditional Design, Tour Arrangements, Persian Garden',
                'image_placeholder': '[Elegant villa with mountain backdrop, Persian garden with roses, traditional architecture, panoramic views]',
                'host': hosts[1],
                'status': 'active'
            },
            # Mashhad accommodations
            {
                'name': 'Imam Reza Pilgrimage Hotel',
                'description': 'Comfortable hotel near the Holy Shrine of Imam Reza. Designed for pilgrims and spiritual travelers with peaceful atmosphere and excellent service.',
                'accommodation_type': 'hotel',
                'city': 'mashhad',
                'price_per_night': Decimal('1200000'),
                'star_rating': 3,
                'amenities': 'WiFi, Prayer Room, Halal Restaurant, Shuttle to Shrine, Quiet Environment, Spiritual Guidance, 24/7 Reception',
                'image_placeholder': '[Modern hotel near shrine, comfortable rooms with prayer direction, peaceful atmosphere, spiritual ambiance]',
                'host': hosts[2],
                'status': 'active'
            },
            {
                'name': 'Mashhad Family Residence',
                'description': 'Spacious family villa perfect for large groups visiting Mashhad. Traditional Iranian hospitality with modern comforts and easy access to holy sites.',
                'accommodation_type': 'villa',
                'city': 'mashhad',
                'price_per_night': Decimal('2800000'),
                'star_rating': None,
                'amenities': 'WiFi, Large Kitchen, Multiple Bedrooms, Family Friendly, Traditional Hospitality, Prayer Space, Group Dining',
                'image_placeholder': '[Large family villa with multiple rooms, traditional Iranian family dining area, comfortable living spaces, group areas]',
                'host': hosts[2],
                'status': 'active'
            }
        ]

        accommodations = []
        for acc_data in accommodations_data:
            accommodation, created = Accommodation.objects.get_or_create(
                name=acc_data['name'],
                host=acc_data['host'],
                defaults=acc_data
            )
            accommodations.append(accommodation)
            
            if created:
                self.stdout.write(f'Created accommodation: {accommodation.name}')

        # Create sample bookings with realistic scenarios
        bookings_data = [
            {
                'guest': guests[0],  # Sara
                'accommodation': accommodations[0],  # Grand Azadi Hotel
                'check_in_date': timezone.now().date() + timedelta(days=7),
                'check_out_date': timezone.now().date() + timedelta(days=10),
                'number_of_guests': 2,
                'special_requests': 'Late check-in please, arriving around 10 PM. Honeymoon trip, would appreciate room upgrade if available.',
                'status': 'pending'
            },
            {
                'guest': guests[1],  # Reza
                'accommodation': accommodations[3],  # Hafez Garden Hotel
                'check_in_date': timezone.now().date() + timedelta(days=14),
                'check_out_date': timezone.now().date() + timedelta(days=18),
                'number_of_guests': 1,
                'special_requests': 'Quiet room preferred for remote work. Need reliable WiFi for video conferences.',
                'status': 'confirmed'
            },
            {
                'guest': guests[2],  # Elena
                'accommodation': accommodations[4],  # Persepolis Villa
                'check_in_date': timezone.now().date() + timedelta(days=21),
                'check_out_date': timezone.now().date() + timedelta(days=28),
                'number_of_guests': 1,
                'special_requests': 'Photography equipment storage needed. Early morning access to garden for sunrise photos.',
                'status': 'confirmed'
            },
            {
                'guest': guests[0],  # Sara
                'accommodation': accommodations[5],  # Imam Reza Hotel
                'check_in_date': timezone.now().date() + timedelta(days=35),
                'check_out_date': timezone.now().date() + timedelta(days=39),
                'number_of_guests': 3,
                'special_requests': 'Family pilgrimage trip with elderly parents. Need ground floor room and wheelchair accessibility.',
                'status': 'pending'
            }
        ]

        for booking_data in bookings_data:
            accommodation = booking_data['accommodation']
            nights = (booking_data['check_out_date'] - booking_data['check_in_date']).days
            
            booking, created = Booking.objects.get_or_create(
                guest=booking_data['guest'],
                accommodation=accommodation,
                check_in_date=booking_data['check_in_date'],
                defaults={
                    'check_out_date': booking_data['check_out_date'],
                    'number_of_guests': booking_data['number_of_guests'],
                    'price_per_night': accommodation.price_per_night,
                    'total_price': accommodation.price_per_night * nights,
                    'special_requests': booking_data['special_requests'],
                    'status': booking_data['status']
                }
            )
            
            if created:
                if booking.status == 'confirmed':
                    booking.confirmed_at = timezone.now()
                    booking.save()
                self.stdout.write(f'Created booking: {booking.guest.username} -> {booking.accommodation.name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 COMPREHENSIVE SAMPLE DATA CREATED SUCCESSFULLY!\n\n'
                f'👥 USERS CREATED:\n'
                f'   Hosts: host_tehran, host_shiraz, host_mashhad (password: host123)\n'
                f'   Guests: guest_sara, guest_reza, guest_elena (password: guest123)\n'
                f'   Admin: admin (password: admin123)\n\n'
                f'🏨 ACCOMMODATIONS: {len(accommodations)} properties\n'
                f'   - Tehran: 3 properties (hotel, villa, suite)\n'
                f'   - Shiraz: 2 properties (hotel, villa)\n'
                f'   - Mashhad: 2 properties (hotel, villa)\n\n'
                f'📅 BOOKINGS: {len(bookings_data)} sample bookings\n'
                f'   - Different statuses (pending, confirmed)\n'
                f'   - Realistic scenarios and requests\n'
                f'   - Various guest types and needs\n\n'
                f'🚀 PLATFORM READY FOR TESTING AND DEMONSTRATION!\n'
                f'   Visit http://localhost:8000 to explore the platform'
            )
        )
