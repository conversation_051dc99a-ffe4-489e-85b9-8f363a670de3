"""
Management command to create test pricing data for holiday/seasonal pricing testing
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from accommodations.models import Accommodation, AccommodationPricing
from datetime import date, timedelta
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create test pricing data for holiday/seasonal pricing testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--accommodation-id',
            type=int,
            help='Specific accommodation ID to create pricing for',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating test pricing data...'))

        # Get accommodations to create pricing for
        if options['accommodation_id']:
            accommodations = Accommodation.objects.filter(id=options['accommodation_id'])
            if not accommodations.exists():
                self.stdout.write(
                    self.style.ERROR(f'Accommodation with ID {options["accommodation_id"]} not found')
                )
                return
        else:
            accommodations = Accommodation.objects.all()[:3]  # First 3 accommodations

        if not accommodations.exists():
            self.stdout.write(self.style.ERROR('No accommodations found'))
            return

        today = date.today()
        created_count = 0

        for accommodation in accommodations:
            self.stdout.write(f'\nCreating pricing for: {accommodation.name}')
            self.stdout.write(f'Base price: {accommodation.price_per_night} IRR')

            # 1. Holiday pricing (New Year) - 50% increase
            holiday_start = today + timedelta(days=10)
            holiday_end = today + timedelta(days=15)
            holiday_price = int(accommodation.price_per_night * Decimal('1.5'))

            holiday_pricing, created = AccommodationPricing.objects.get_or_create(
                accommodation=accommodation,
                pricing_type='holiday',
                start_date=holiday_start,
                end_date=holiday_end,
                defaults={
                    'price_per_night': holiday_price,
                    'description': 'New Year Holiday Premium',
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  ✅ Holiday pricing: {holiday_start} to {holiday_end} = {holiday_price} IRR'
                    )
                )
                created_count += 1
            else:
                self.stdout.write(f'  📝 Holiday pricing already exists')

            # 2. Peak season pricing - 30% increase
            peak_start = today + timedelta(days=20)
            peak_end = today + timedelta(days=35)
            peak_price = int(accommodation.price_per_night * Decimal('1.3'))

            peak_pricing, created = AccommodationPricing.objects.get_or_create(
                accommodation=accommodation,
                pricing_type='peak_season',
                start_date=peak_start,
                end_date=peak_end,
                defaults={
                    'price_per_night': peak_price,
                    'description': 'Summer Peak Season',
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  ✅ Peak season: {peak_start} to {peak_end} = {peak_price} IRR'
                    )
                )
                created_count += 1
            else:
                self.stdout.write(f'  📝 Peak season pricing already exists')

            # 3. Weekend premium - 20% increase (every weekend for next 2 months)
            weekend_price = int(accommodation.price_per_night * Decimal('1.2'))
            
            current_date = today
            end_date = today + timedelta(days=60)
            
            while current_date <= end_date:
                # Check if it's Friday or Saturday (weekend in Iran)
                if current_date.weekday() in [4, 5]:  # Friday=4, Saturday=5
                    weekend_start = current_date
                    weekend_end = current_date + timedelta(days=1)
                    
                    weekend_pricing, created = AccommodationPricing.objects.get_or_create(
                        accommodation=accommodation,
                        pricing_type='weekend',
                        start_date=weekend_start,
                        end_date=weekend_end,
                        defaults={
                            'price_per_night': weekend_price,
                            'description': f'Weekend Premium - {weekend_start.strftime("%B %d")}',
                            'is_active': True
                        }
                    )
                    
                    if created:
                        created_count += 1
                
                current_date += timedelta(days=1)

            self.stdout.write(f'  ✅ Weekend pricing created for Fridays and Saturdays')

            # 4. Low season discount - 15% decrease
            low_start = today + timedelta(days=40)
            low_end = today + timedelta(days=55)
            low_price = int(accommodation.price_per_night * Decimal('0.85'))

            low_pricing, created = AccommodationPricing.objects.get_or_create(
                accommodation=accommodation,
                pricing_type='low_season',
                start_date=low_start,
                end_date=low_end,
                defaults={
                    'price_per_night': low_price,
                    'description': 'Low Season Discount',
                    'is_active': True
                }
            )

            if created:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'  ✅ Low season: {low_start} to {low_end} = {low_price} IRR'
                    )
                )
                created_count += 1
            else:
                self.stdout.write(f'  📝 Low season pricing already exists')

        self.stdout.write(
            self.style.SUCCESS(f'\n🎉 Created {created_count} new pricing rules')
        )

        # Test the pricing system
        self.stdout.write('\n=== TESTING PRICING SYSTEM ===')
        
        test_accommodation = accommodations.first()
        
        # Test regular date
        regular_date = today + timedelta(days=5)
        regular_price = test_accommodation.get_price_for_date(regular_date)
        self.stdout.write(f'Regular date ({regular_date}): {regular_price} IRR')
        
        # Test holiday date
        holiday_date = today + timedelta(days=12)
        holiday_price_actual = test_accommodation.get_price_for_date(holiday_date)
        self.stdout.write(f'Holiday date ({holiday_date}): {holiday_price_actual} IRR')
        
        # Test total calculation
        test_start = today + timedelta(days=10)
        test_end = today + timedelta(days=15)
        total_price = test_accommodation.calculate_total_price(test_start, test_end, 2)
        self.stdout.write(f'Total for holiday period ({test_start} to {test_end}): {total_price} IRR')
        
        self.stdout.write(self.style.SUCCESS('\n✅ Test pricing data created successfully!'))
        self.stdout.write('You can now test the booking system with dynamic pricing.')
