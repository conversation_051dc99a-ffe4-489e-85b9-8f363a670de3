"""
Forms for accommodation management in NomadPersia platform.

This module contains forms for creating, editing, and searching accommodations
with proper validation and user-friendly interfaces.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import re

from .models import Accommodation, AccommodationPricing, AccommodationAvailability, GuestCapacityRule


class AccommodationForm(forms.ModelForm):
    """
    Form for creating and editing accommodations.
    """
    
    class Meta:
        model = Accommodation
        fields = [
            'name', 'description', 'accommodation_type', 'city', 'town', 'address',
            'reservation_phone', 'geolocation',
            'price_per_night', 'star_rating', 'amenities', 'image_placeholder',
            'standard_capacity', 'max_capacity', 'bedrooms', 'bathrooms', 'property_size',
            'extra_guest_fee', 'instant_booking', 'cancellation_policy', 'check_in_time', 'check_out_time',
            'smoking_allowed', 'same_gender_groups_only', 'national_id_required',
            'pets_not_allowed', 'events_not_allowed', 'no_24hour_checkin', 'additional_policies'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter accommodation name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Describe your accommodation in detail'
            }),
            'accommodation_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'city': forms.Select(attrs={
                'class': 'form-select'
            }),
            'town': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Zafaraniyeh, Elahiyeh, Niavaran'
            }),
            'price_per_night': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Price per night (IRR)',
                'min': '0',
                'step': '1000'
            }),
            'star_rating': forms.Select(attrs={
                'class': 'form-select'
            }),
            'amenities': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'List amenities separated by commas (e.g., WiFi, Pool, Parking)'
            }),
            'image_placeholder': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Describe the accommodation images (MVP feature)'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter complete address including street, neighborhood, postal code'
            }),
            'reservation_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., +98 21 1234 5678 or 09123456789',
                'pattern': r'^(\+98|0)?[0-9\s\-\(\)]{10,15}$'
            }),
            'geolocation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter coordinates (lat,lng) or select on map - e.g., 35.6892, 51.3890'
            }),
            'smoking_allowed': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'same_gender_groups_only': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'national_id_required': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'pets_not_allowed': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'events_not_allowed': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'no_24hour_checkin': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'check_out_time': forms.Select(attrs={
                'class': 'form-select'
            }),
            'additional_policies': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Enter any additional house rules, policies, or special requirements for guests...'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make star_rating not required initially
        self.fields['star_rating'].required = False
        
        # Add help text
        self.fields['price_per_night'].help_text = 'Enter price in Iranian Rial'
        self.fields['star_rating'].help_text = 'Required only for hotels'
        self.fields['amenities'].help_text = 'Separate multiple amenities with commas'
    
    def clean(self):
        """Custom validation for accommodation form."""
        cleaned_data = super().clean()
        accommodation_type = cleaned_data.get('accommodation_type')
        star_rating = cleaned_data.get('star_rating')
        standard_capacity = cleaned_data.get('standard_capacity')
        max_capacity = cleaned_data.get('max_capacity')
        extra_guest_fee = cleaned_data.get('extra_guest_fee')
        reservation_phone = cleaned_data.get('reservation_phone')
        geolocation = cleaned_data.get('geolocation')

        # Clear star rating for all new accommodation types (no hotels)
        if star_rating:
            cleaned_data['star_rating'] = None

        # Validate capacity logic
        if max_capacity and standard_capacity and max_capacity < standard_capacity:
            raise ValidationError({
                'max_capacity': _('Maximum capacity cannot be less than standard capacity.')
            })

        # Validate extra guest fee
        if (max_capacity and standard_capacity and max_capacity > standard_capacity and
            extra_guest_fee == 0):
            raise ValidationError({
                'extra_guest_fee': _('Extra guest fee should be set when maximum capacity exceeds standard capacity.')
            })

        # Validate phone number format
        if reservation_phone:
            # Remove spaces, dashes, and parentheses for validation
            clean_phone = re.sub(r'[\s\-\(\)]', '', reservation_phone)

            # Check international phone number patterns
            # Support international format (+country code + number) or local format (starting with 0)
            international_format = re.match(r'^\+[1-9]\d{1,3}\d{4,15}$', clean_phone)
            local_format = re.match(r'^0\d{7,15}$', clean_phone)

            if not (international_format or local_format):
                raise ValidationError({
                    'reservation_phone': _('Please enter a valid phone number (international format: +[country code][number] or local format starting with 0)')
                })

        # Validate geolocation format
        if geolocation:
            # Check if it matches latitude,longitude format
            geo_pattern = re.match(r'^-?[0-9]+\.?[0-9]*,-?[0-9]+\.?[0-9]*$', geolocation.replace(' ', ''))
            if not geo_pattern:
                raise ValidationError({
                    'geolocation': _('Please enter coordinates in format: latitude,longitude (e.g., 35.6892,51.3890)')
                })

        return cleaned_data


class AccommodationPricingForm(forms.ModelForm):
    """
    Form for creating dynamic pricing rules.
    """

    class Meta:
        model = AccommodationPricing
        fields = ['pricing_type', 'start_date', 'end_date', 'price_per_night', 'description']
        widgets = {
            'pricing_type': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'price_per_night': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Price per night (Toman)',
                'min': '0',
                'step': '1000'
            }),
            'description': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Optional description (e.g., Nowruz holidays)'
            })
        }

    def clean(self):
        """Validate pricing form."""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise ValidationError('Start date cannot be after end date.')

        return cleaned_data


class AccommodationAvailabilityForm(forms.ModelForm):
    """
    Form for managing accommodation availability.
    """

    class Meta:
        model = AccommodationAvailability
        fields = ['start_date', 'end_date', 'is_blocked', 'minimum_nights', 'reason']
        widgets = {
            'start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'end_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'is_blocked': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'minimum_nights': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '30'
            }),
            'reason': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Reason for blocking (optional)'
            })
        }


class GuestCapacityForm(forms.Form):
    """
    Form for configuring guest capacity and pricing rules.
    """

    # Base capacity settings
    standard_capacity = forms.IntegerField(
        min_value=1,
        max_value=20,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Base guest capacity'
        }),
        help_text='Number of guests included in base price'
    )

    max_capacity = forms.IntegerField(
        min_value=1,
        max_value=30,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Maximum total guests'
        }),
        help_text='Maximum number of guests allowed'
    )

    extra_guest_fee = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Extra guest fee per night',
            'step': '1000'
        }),
        help_text='Additional fee per extra guest per night (Toman)'
    )

    # Age-specific pricing
    infant_fee = forms.IntegerField(
        min_value=0,
        initial=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '1000'
        }),
        help_text='Fee per infant (0-2 years) per night'
    )

    child_fee = forms.IntegerField(
        min_value=0,
        initial=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '1000'
        }),
        help_text='Fee per child (3-12 years) per night'
    )

    teen_fee = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '1000'
        }),
        help_text='Fee per teen (13-17 years) per night'
    )

    # Capacity counting options
    infants_count_in_capacity = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text='Count infants towards total capacity'
    )

    children_count_in_capacity = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text='Count children towards total capacity'
    )

    def clean(self):
        """Validate capacity settings."""
        cleaned_data = super().clean()
        standard_capacity = cleaned_data.get('standard_capacity')
        max_capacity = cleaned_data.get('max_capacity')

        if standard_capacity and max_capacity and max_capacity < standard_capacity:
            raise ValidationError('Maximum capacity cannot be less than standard capacity.')

        return cleaned_data


class AccommodationSearchForm(forms.Form):
    """
    Form for searching and filtering accommodations.
    """
    
    SORT_CHOICES = [
        ('', 'Sort by...'),
        ('price_asc', 'Price: Low to High'),
        ('price_desc', 'Price: High to Low'),
        ('name', 'Name A-Z'),
        ('newest', 'Newest First'),
        ('rating', 'Highest Rated'),
    ]
    
    city = forms.ChoiceField(
        choices=[('', 'All Cities')] + Accommodation.CITIES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    accommodation_type = forms.ChoiceField(
        choices=[('', 'All Types')] + Accommodation.ACCOMMODATION_TYPES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    min_price = forms.DecimalField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min price',
            'step': '1000'
        })
    )
    
    max_price = forms.DecimalField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max price',
            'step': '1000'
        })
    )
    
    star_rating = forms.ChoiceField(
        choices=[('', 'Any Rating')] + Accommodation.STAR_RATINGS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    search_query = forms.CharField(
        required=False,
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, description, or amenities...'
        })
    )
    
    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    
    def clean(self):
        """Validate price range."""
        cleaned_data = super().clean()
        min_price = cleaned_data.get('min_price')
        max_price = cleaned_data.get('max_price')
        
        if min_price and max_price and min_price > max_price:
            raise ValidationError(_('Minimum price cannot be greater than maximum price.'))
        
        return cleaned_data


class AccommodationStatusForm(forms.ModelForm):
    """
    Form for admin to change accommodation status.
    """
    
    class Meta:
        model = Accommodation
        fields = ['status']
        widgets = {
            'status': forms.Select(attrs={
                'class': 'form-select'
            })
        }
