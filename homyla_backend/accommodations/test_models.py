"""
Test cases for accommodations app models.

This module contains comprehensive tests for Accommodation model
and related functionality.
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal

from accounts.models import UserProfile
from .models import Accommodation


class AccommodationModelTest(TestCase):
    """Test cases for Accommodation model."""
    
    def setUp(self):
        """Set up test data."""
        self.host_user = User.objects.create_user(
            username='hostuser',
            email='<EMAIL>',
            password='hostpass123'
        )
        self.host_profile = UserProfile.objects.create(
            user=self.host_user,
            role='host'
        )
        
        self.accommodation_data = {
            'name': 'Test Hotel Tehran',
            'description': 'A beautiful hotel in the heart of Tehran',
            'accommodation_type': 'hotel',
            'city': 'tehran',
            'price_per_night': Decimal('1500000'),
            'star_rating': 4,
            'amenities': 'WiFi, Pool, Spa, Restaurant',
            'image_placeholder': 'Beautiful hotel exterior and interior photos',
            'host': self.host_user,
            'status': 'active'
        }
    
    def test_create_accommodation(self):
        """Test creating an accommodation."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        
        self.assertEqual(accommodation.name, 'Test Hotel Tehran')
        self.assertEqual(accommodation.host, self.host_user)
        self.assertEqual(accommodation.accommodation_type, 'hotel')
        self.assertEqual(accommodation.city, 'tehran')
        self.assertEqual(accommodation.price_per_night, Decimal('1500000'))
        self.assertEqual(accommodation.star_rating, 4)
        self.assertEqual(accommodation.status, 'active')
    
    def test_accommodation_str_method(self):
        """Test string representation of Accommodation."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        expected_str = "Test Hotel Tehran - Tehran"
        self.assertEqual(str(accommodation), expected_str)
    
    def test_accommodation_type_choices(self):
        """Test accommodation type validation."""
        valid_types = ['hotel', 'villa', 'suite']
        
        for acc_type in valid_types:
            data = self.accommodation_data.copy()
            data['accommodation_type'] = acc_type
            accommodation = Accommodation(**data)
            accommodation.full_clean()  # Should not raise ValidationError
    
    def test_city_choices(self):
        """Test city validation."""
        valid_cities = ['tehran', 'shiraz', 'mashhad']
        
        for city in valid_cities:
            data = self.accommodation_data.copy()
            data['city'] = city
            accommodation = Accommodation(**data)
            accommodation.full_clean()  # Should not raise ValidationError
    
    def test_star_rating_validation(self):
        """Test star rating validation."""
        # Valid star ratings
        for rating in [1, 2, 3, 4, 5]:
            data = self.accommodation_data.copy()
            data['star_rating'] = rating
            accommodation = Accommodation(**data)
            accommodation.full_clean()  # Should not raise ValidationError
        
        # Invalid star ratings
        for rating in [0, 6, -1]:
            data = self.accommodation_data.copy()
            data['star_rating'] = rating
            accommodation = Accommodation(**data)
            with self.assertRaises(ValidationError):
                accommodation.full_clean()
    
    def test_price_validation(self):
        """Test price validation."""
        # Valid prices
        valid_prices = [Decimal('100000'), Decimal('5000000'), Decimal('999999.99')]
        
        for price in valid_prices:
            data = self.accommodation_data.copy()
            data['price_per_night'] = price
            accommodation = Accommodation(**data)
            accommodation.full_clean()  # Should not raise ValidationError
        
        # Invalid prices (negative)
        data = self.accommodation_data.copy()
        data['price_per_night'] = Decimal('-100')
        accommodation = Accommodation(**data)
        with self.assertRaises(ValidationError):
            accommodation.full_clean()
    
    def test_status_choices(self):
        """Test status validation."""
        valid_statuses = ['pending', 'active', 'inactive', 'rejected']
        
        for status in valid_statuses:
            data = self.accommodation_data.copy()
            data['status'] = status
            accommodation = Accommodation(**data)
            accommodation.full_clean()  # Should not raise ValidationError
    
    def test_get_amenities_list_method(self):
        """Test get_amenities_list method."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        amenities_list = accommodation.get_amenities_list()
        
        expected_list = ['WiFi', 'Pool', 'Spa', 'Restaurant']
        self.assertEqual(amenities_list, expected_list)
        
        # Test with empty amenities
        accommodation.amenities = ''
        accommodation.save()
        self.assertEqual(accommodation.get_amenities_list(), [])
        
        # Test with None amenities
        accommodation.amenities = None
        accommodation.save()
        self.assertEqual(accommodation.get_amenities_list(), [])
    
    def test_accommodation_timestamps(self):
        """Test that timestamps are set correctly."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        
        self.assertIsNotNone(accommodation.created_at)
        self.assertIsNotNone(accommodation.updated_at)
        
        # Update accommodation and check updated_at changes
        original_updated_at = accommodation.updated_at
        accommodation.name = 'Updated Hotel Name'
        accommodation.save()
        
        self.assertGreater(accommodation.updated_at, original_updated_at)
    
    def test_accommodation_default_status(self):
        """Test default status is pending."""
        data = self.accommodation_data.copy()
        del data['status']  # Remove status to test default
        
        accommodation = Accommodation.objects.create(**data)
        self.assertEqual(accommodation.status, 'pending')
    
    def test_star_rating_optional_for_non_hotels(self):
        """Test that star rating is optional for non-hotel accommodations."""
        data = self.accommodation_data.copy()
        data['accommodation_type'] = 'villa'
        data['star_rating'] = None
        
        accommodation = Accommodation.objects.create(**data)
        self.assertIsNone(accommodation.star_rating)
    
    def test_accommodation_ordering(self):
        """Test default ordering by creation date."""
        # Create multiple accommodations
        acc1 = Accommodation.objects.create(**self.accommodation_data)
        
        data2 = self.accommodation_data.copy()
        data2['name'] = 'Second Hotel'
        acc2 = Accommodation.objects.create(**data2)
        
        accommodations = list(Accommodation.objects.all())
        # Should be ordered by -created_at (newest first)
        self.assertEqual(accommodations[0], acc2)
        self.assertEqual(accommodations[1], acc1)
    
    def test_accommodation_update(self):
        """Test updating accommodation information."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        
        # Update accommodation
        accommodation.name = 'Updated Hotel Name'
        accommodation.price_per_night = Decimal('2000000')
        accommodation.status = 'inactive'
        accommodation.save()
        
        # Refresh from database
        accommodation.refresh_from_db()
        
        self.assertEqual(accommodation.name, 'Updated Hotel Name')
        self.assertEqual(accommodation.price_per_night, Decimal('2000000'))
        self.assertEqual(accommodation.status, 'inactive')
    
    def test_accommodation_deletion(self):
        """Test accommodation deletion."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        accommodation_id = accommodation.id
        
        accommodation.delete()
        
        with self.assertRaises(Accommodation.DoesNotExist):
            Accommodation.objects.get(id=accommodation_id)
    
    def test_host_deletion_cascades(self):
        """Test that deleting host also deletes accommodations."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        accommodation_id = accommodation.id
        
        self.host_user.delete()
        
        with self.assertRaises(Accommodation.DoesNotExist):
            Accommodation.objects.get(id=accommodation_id)
    
    def test_accommodation_display_methods(self):
        """Test display methods for choices."""
        accommodation = Accommodation.objects.create(**self.accommodation_data)
        
        self.assertEqual(accommodation.get_accommodation_type_display(), 'Hotel')
        self.assertEqual(accommodation.get_city_display(), 'Tehran')
        self.assertEqual(accommodation.get_status_display(), 'Active')
    
    def test_accommodation_search_functionality(self):
        """Test accommodation search and filtering."""
        # Create accommodations in different cities
        acc1 = Accommodation.objects.create(**self.accommodation_data)
        
        data2 = self.accommodation_data.copy()
        data2['name'] = 'Shiraz Villa'
        data2['city'] = 'shiraz'
        data2['accommodation_type'] = 'villa'
        acc2 = Accommodation.objects.create(**data2)
        
        # Test city filtering
        tehran_accommodations = Accommodation.objects.filter(city='tehran')
        self.assertIn(acc1, tehran_accommodations)
        self.assertNotIn(acc2, tehran_accommodations)
        
        # Test type filtering
        hotels = Accommodation.objects.filter(accommodation_type='hotel')
        self.assertIn(acc1, hotels)
        self.assertNotIn(acc2, hotels)
        
        # Test status filtering
        active_accommodations = Accommodation.objects.filter(status='active')
        self.assertIn(acc1, active_accommodations)
        self.assertIn(acc2, active_accommodations)
    
    def test_accommodation_price_range_filtering(self):
        """Test price range filtering."""
        # Create accommodations with different prices
        acc1 = Accommodation.objects.create(**self.accommodation_data)
        
        data2 = self.accommodation_data.copy()
        data2['name'] = 'Expensive Hotel'
        data2['price_per_night'] = Decimal('5000000')
        acc2 = Accommodation.objects.create(**data2)
        
        # Test price range filtering
        affordable = Accommodation.objects.filter(price_per_night__lte=Decimal('2000000'))
        expensive = Accommodation.objects.filter(price_per_night__gte=Decimal('3000000'))
        
        self.assertIn(acc1, affordable)
        self.assertNotIn(acc2, affordable)
        self.assertNotIn(acc1, expensive)
        self.assertIn(acc2, expensive)
