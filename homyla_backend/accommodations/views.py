"""
Views for accommodation management in NomadPersia platform.

This module contains views for searching, creating, editing, and managing
accommodations for guests, hosts, and admins.
"""

import os
from datetime import datetime, date
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView, CreateView, UpdateView

from accounts.views import role_required
from .forms import AccommodationForm, AccommodationSearchForm, AccommodationStatusForm
from .models import Accommodation, AccommodationPricing, AccommodationAvailability, GuestCapacityRule


def search_view(request):
    """
    Public view for searching and filtering accommodations.
    """
    form = AccommodationSearchForm(request.GET or None)
    accommodations = Accommodation.objects.filter(status='active')

    if form.is_valid():
        # Apply filters
        if form.cleaned_data.get('city'):
            accommodations = accommodations.filter(city=form.cleaned_data['city'])

        if form.cleaned_data.get('accommodation_type'):
            accommodations = accommodations.filter(
                accommodation_type=form.cleaned_data['accommodation_type']
            )

        if form.cleaned_data.get('min_price'):
            accommodations = accommodations.filter(
                price_per_night__gte=form.cleaned_data['min_price']
            )

        if form.cleaned_data.get('max_price'):
            accommodations = accommodations.filter(
                price_per_night__lte=form.cleaned_data['max_price']
            )

        if form.cleaned_data.get('star_rating'):
            accommodations = accommodations.filter(
                star_rating=form.cleaned_data['star_rating']
            )

        if form.cleaned_data.get('search_query'):
            query = form.cleaned_data['search_query']
            accommodations = accommodations.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(amenities__icontains=query)
            )

        # Apply sorting
        sort_by = form.cleaned_data.get('sort_by')
        if sort_by == 'price_asc':
            accommodations = accommodations.order_by('price_per_night')
        elif sort_by == 'price_desc':
            accommodations = accommodations.order_by('-price_per_night')
        elif sort_by == 'name':
            accommodations = accommodations.order_by('name')
        elif sort_by == 'newest':
            accommodations = accommodations.order_by('-created_at')
        elif sort_by == 'rating':
            accommodations = accommodations.order_by('-star_rating')

    # Pagination
    paginator = Paginator(accommodations, 12)  # 12 accommodations per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics for display
    total_count = accommodations.count()
    cities_count = accommodations.values('city').distinct().count()

    context = {
        'form': form,
        'page_obj': page_obj,
        'accommodations': page_obj,
        'total_count': total_count,
        'cities_count': cities_count,
        'is_filtered': any(form.cleaned_data.values()) if form.is_valid() else False,
    }

    return render(request, 'accommodations/search.html', context)


def accommodation_detail_view(request, pk):
    """
    Detail view for a specific accommodation.
    """
    # Allow hosts to see their own accommodations regardless of status
    if request.user.is_authenticated:
        try:
            accommodation = Accommodation.objects.get(pk=pk, host=request.user)
        except Accommodation.DoesNotExist:
            # If not the host, only show active accommodations
            accommodation = get_object_or_404(Accommodation, pk=pk, status='active')
    else:
        # Anonymous users can only see active accommodations
        accommodation = get_object_or_404(Accommodation, pk=pk, status='active')

    # Check if user can book (must be guest and accommodation must be active)
    can_book = (
        request.user.is_authenticated and
        hasattr(request.user, 'profile') and
        request.user.profile.is_guest() and
        accommodation.status == 'active'
    )

    # Check if user is the host
    is_host = (
        request.user.is_authenticated and
        accommodation.host == request.user
    )

    # Check if user can write a review
    can_review = False
    if request.user.is_authenticated and hasattr(request.user, 'profile'):
        from bookings.models import Booking
        from reviews.models import Review

        # User must be a guest to write reviews
        if request.user.profile.is_guest():
            # Check if user has completed booking and hasn't reviewed yet
            completed_booking = Booking.objects.filter(
                guest=request.user,
                accommodation=accommodation,
                status='completed'
            ).exists()

            existing_review = Review.objects.filter(
                guest=request.user,
                accommodation=accommodation
            ).exists()

            can_review = completed_booking and not existing_review

    # Get accommodation photos
    photos = get_accommodation_photos(accommodation)

    # Get pricing and availability data for booking system
    from datetime import date, timedelta
    from .models import AccommodationPricing, AccommodationAvailability, GuestCapacityRule

    # Get pricing rules for the next 90 days
    today = date.today()
    end_date = today + timedelta(days=90)

    pricing_rules = AccommodationPricing.objects.filter(
        accommodation=accommodation,
        is_active=True,
        end_date__gte=today
    ).order_by('start_date')

    # Get availability rules
    availability_rules = AccommodationAvailability.objects.filter(
        accommodation=accommodation,
        end_date__gte=today
    ).order_by('start_date')

    # Get guest capacity rules
    capacity_rules = GuestCapacityRule.objects.filter(
        accommodation=accommodation
    ).order_by('age_category')

    # Generate pricing calendar for next 90 days
    pricing_calendar = {}
    current_date = today
    while current_date <= end_date:
        pricing_calendar[current_date.isoformat()] = {
            'date': current_date,
            'price': accommodation.get_price_for_date(current_date),
            'available': True,
            'minimum_nights': 1
        }
        current_date += timedelta(days=1)

    # Apply availability rules to calendar
    for rule in availability_rules:
        rule_start = max(rule.start_date, today)
        rule_end = min(rule.end_date, end_date)
        current_date = rule_start

        while current_date <= rule_end:
            if current_date.isoformat() in pricing_calendar:
                pricing_calendar[current_date.isoformat()]['available'] = not rule.is_blocked
                pricing_calendar[current_date.isoformat()]['minimum_nights'] = rule.minimum_nights
            current_date += timedelta(days=1)

    context = {
        'accommodation': accommodation,
        'can_book': can_book,
        'is_host': is_host,
        'can_review': can_review,
        'amenities_list': accommodation.get_amenities_list(),
        'photos': photos,
        'pricing_rules': pricing_rules,
        'availability_rules': availability_rules,
        'capacity_rules': capacity_rules,
        'pricing_calendar': pricing_calendar,
        'base_price': accommodation.price_per_night,
        'extra_guest_fee': accommodation.extra_guest_fee,
        'standard_capacity': accommodation.standard_capacity,
        'max_capacity': accommodation.max_capacity,
    }

    return render(request, 'accommodations/detail.html', context)


@login_required
@role_required(['host'])
def host_accommodations_view(request):
    """
    View for hosts to manage their accommodations.
    """
    accommodations = Accommodation.objects.filter(host=request.user).order_by('-created_at')

    # Statistics
    total_count = accommodations.count()
    active_count = accommodations.filter(status='active').count()
    pending_count = accommodations.filter(status='pending').count()

    # Pagination
    paginator = Paginator(accommodations, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'accommodations': page_obj,
        'total_count': total_count,
        'active_count': active_count,
        'pending_count': pending_count,
    }

    return render(request, 'accommodations/host_list.html', context)


@login_required
@role_required(['host'])
def create_accommodation_view(request):
    """
    View for hosts to create new accommodations.
    """
    if request.method == 'POST':
        form = AccommodationForm(request.POST)
        if form.is_valid():
            accommodation = form.save(commit=False)
            accommodation.host = request.user
            accommodation.save()

            # Debug: Print uploaded files
            print(f"🔍 DEBUG: Files in request: {list(request.FILES.keys())}")
            for key, file in request.FILES.items():
                print(f"🔍 DEBUG: File {key}: {file.name} ({file.size} bytes)")

            # Handle photo uploads
            photo_count = handle_photo_uploads(request, accommodation)

            if photo_count > 0:
                messages.success(
                    request,
                    f'Accommodation "{accommodation.name}" has been created with {photo_count} photos and is pending approval.'
                )
            else:
                messages.success(
                    request,
                    f'Accommodation "{accommodation.name}" has been created and is pending approval. No photos were uploaded.'
                )
            return redirect('accommodations:host_accommodations')
    else:
        form = AccommodationForm()

    context = {
        'form': form,
        'title': 'Add New Accommodation',
    }

    return render(request, 'accommodations/form.html', context)


@login_required
@role_required(['host'])
def edit_accommodation_view(request, pk):
    """
    View for hosts to edit their accommodations.
    """
    accommodation = get_object_or_404(
        Accommodation,
        pk=pk,
        host=request.user
    )

    if request.method == 'POST':
        form = AccommodationForm(request.POST, instance=accommodation)
        if form.is_valid():
            # Reset status to pending if accommodation was previously rejected
            if accommodation.status == 'rejected':
                accommodation.status = 'pending'

            accommodation = form.save()

            # Process dynamic pricing rules
            pricing_rules_processed = process_pricing_rules(request, accommodation)

            # Process availability rules
            availability_rules_processed = process_availability_rules(request, accommodation)

            # Process guest capacity rules
            capacity_rules_processed = process_capacity_rules(request, accommodation)

            # Debug: Print uploaded files
            print(f"🔍 DEBUG: Files in request: {list(request.FILES.keys())}")
            for key, file in request.FILES.items():
                print(f"🔍 DEBUG: File {key}: {file.name} ({file.size} bytes)")

            # Handle photo uploads
            photo_count = handle_photo_uploads(request, accommodation)

            # Create success message with details
            success_parts = [f'Accommodation "{accommodation.name}" has been updated']
            if photo_count > 0:
                success_parts.append(f'{photo_count} new photos uploaded')
            if pricing_rules_processed > 0:
                success_parts.append(f'{pricing_rules_processed} pricing rules saved')
            if availability_rules_processed > 0:
                success_parts.append(f'{availability_rules_processed} availability rules saved')
            if capacity_rules_processed > 0:
                success_parts.append(f'{capacity_rules_processed} capacity rules saved')

            messages.success(request, '. '.join(success_parts) + '.')
            return redirect('accommodations:host_accommodations')
    else:
        form = AccommodationForm(instance=accommodation)

    # Get existing photos for editing
    photos = get_accommodation_photos(accommodation)

    # Get existing pricing rules
    pricing_rules = AccommodationPricing.objects.filter(
        accommodation=accommodation
    ).order_by('start_date')

    # Get existing availability rules
    availability_rules = AccommodationAvailability.objects.filter(
        accommodation=accommodation
    ).order_by('start_date')

    # Get existing capacity rules
    capacity_rules = GuestCapacityRule.objects.filter(
        accommodation=accommodation
    ).order_by('age_category')

    context = {
        'form': form,
        'accommodation': accommodation,
        'photos': photos,
        'pricing_rules': pricing_rules,
        'availability_rules': availability_rules,
        'capacity_rules': capacity_rules,
        'title': f'Edit {accommodation.name}',
    }

    return render(request, 'accommodations/form.html', context)


@login_required
@role_required(['host'])
def toggle_accommodation_status_view(request, pk):
    """
    View for hosts to activate/deactivate their accommodations.
    """
    accommodation = get_object_or_404(
        Accommodation,
        pk=pk,
        host=request.user
    )

    if accommodation.status == 'active':
        accommodation.status = 'inactive'
        messages.success(request, f'"{accommodation.name}" has been deactivated.')
    elif accommodation.status == 'inactive':
        accommodation.status = 'active'
        messages.success(request, f'"{accommodation.name}" has been activated.')
    else:
        messages.error(request, 'Cannot change status of pending or rejected accommodations.')
        return redirect('accommodations:host_accommodations')

    accommodation.save()
    return redirect('accommodations:host_accommodations')


def handle_photo_uploads(request, accommodation):
    """
    Handle photo uploads for an accommodation using AccommodationPhoto model.
    Creates proper database records and handles file storage automatically.
    """
    from .models import AccommodationPhoto
    import os
    photo_count = 0

    print(f"🔍 DEBUG: Starting photo upload for accommodation {accommodation.id}")
    print(f"🔍 DEBUG: Request FILES keys: {list(request.FILES.keys())}")

    # Ensure media directory exists and has correct permissions
    media_dir = os.path.join(settings.MEDIA_ROOT, 'accommodations', str(accommodation.id), 'photos')
    try:
        os.makedirs(media_dir, exist_ok=True)
        print(f"🔍 DEBUG: Created/verified media directory: {media_dir}")
    except Exception as e:
        print(f"❌ ERROR: Could not create media directory: {e}")

    # Photo categories to handle with their display names
    photo_categories = {
        'main': 'main',
        'interior': 'interior',
        'bedroom': 'bedroom',
        'kitchen': 'kitchen',
        'bathroom': 'bathroom'
    }

    # Handle single category photos
    for category, photo_type in photo_categories.items():
        photo_file = request.FILES.get(f'{category}Photo')
        if photo_file:
            try:
                print(f"🔍 DEBUG: Processing {category} photo: {photo_file.name} ({photo_file.size} bytes)")

                # Create AccommodationPhoto instance
                photo = AccommodationPhoto.objects.create(
                    accommodation=accommodation,
                    image=photo_file,
                    photo_type=photo_type,
                    is_main=(category == 'main'),
                    order=photo_count,
                    caption=f'{category.title()} Photo'
                )
                photo_count += 1
                print(f"✅ Saved {category} photo: {photo.image.name} (URL: {photo.image.url})")

            except Exception as e:
                print(f"❌ Error saving {category} photo: {e}")
                import traceback
                traceback.print_exc()

    # Handle additional photos
    additional_photos = request.FILES.getlist('additionalPhotos')
    print(f"🔍 DEBUG: Found {len(additional_photos)} additional photos")

    for i, photo_file in enumerate(additional_photos):
        if photo_file:
            try:
                print(f"🔍 DEBUG: Processing additional photo {i+1}: {photo_file.name} ({photo_file.size} bytes)")

                # Create AccommodationPhoto instance for additional photos
                photo = AccommodationPhoto.objects.create(
                    accommodation=accommodation,
                    image=photo_file,
                    photo_type='other',
                    is_main=False,
                    order=photo_count,
                    caption=f'Additional photo {i+1}'
                )
                photo_count += 1
                print(f"✅ Saved additional photo {i+1}: {photo.image.name} (URL: {photo.image.url})")

            except Exception as e:
                print(f"❌ Error saving additional photo {i+1}: {e}")
                import traceback
                traceback.print_exc()

    print(f"🔍 DEBUG: Photo upload completed. Total photos saved: {photo_count}")
    return photo_count


def get_accommodation_photos(accommodation):
    """
    Get list of photos for an accommodation using AccommodationPhoto model.
    Returns dict with photo categories and their AccommodationPhoto objects.
    """
    from .models import AccommodationPhoto

    photos = {
        'main': None,
        'interior': None,
        'bedroom': None,
        'kitchen': None,
        'bathroom': None,
        'additional': []
    }

    try:
        # Get all photos for this accommodation
        accommodation_photos = AccommodationPhoto.objects.filter(
            accommodation=accommodation
        ).order_by('order', 'created_at')

        print(f"🔍 DEBUG: Found {accommodation_photos.count()} photos for accommodation {accommodation.id}")

        for photo in accommodation_photos:
            print(f"🔍 DEBUG: Photo type: {photo.photo_type}, is_main: {photo.is_main}, image: {photo.image.name}")

            if photo.is_main or photo.photo_type == 'main':
                photos['main'] = photo
            elif photo.photo_type == 'interior':
                photos['interior'] = photo
            elif photo.photo_type == 'bedroom':
                photos['bedroom'] = photo
            elif photo.photo_type == 'kitchen':
                photos['kitchen'] = photo
            elif photo.photo_type == 'bathroom':
                photos['bathroom'] = photo
            else:
                # All other photos go to additional
                photos['additional'].append(photo)

    except Exception as e:
        print(f"❌ ERROR in get_accommodation_photos: {e}")

    return photos


def process_pricing_rules(request, accommodation):
    """Process dynamic pricing rules from form submission."""
    try:
        rules_processed = 0

        # Clear existing pricing rules for this accommodation
        AccommodationPricing.objects.filter(accommodation=accommodation).delete()

        # Process pricing rules from form data
        pricing_counter = 1
        while f'pricing_type_{pricing_counter}' in request.POST:
            pricing_type = request.POST.get(f'pricing_type_{pricing_counter}')
            start_date_str = request.POST.get(f'pricing_start_{pricing_counter}')
            end_date_str = request.POST.get(f'pricing_end_{pricing_counter}')
            price_str = request.POST.get(f'pricing_price_{pricing_counter}')
            description = request.POST.get(f'pricing_desc_{pricing_counter}', '')

            # Validate required fields
            if pricing_type and start_date_str and end_date_str and price_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                    price = int(float(price_str))

                    # Create pricing rule
                    AccommodationPricing.objects.create(
                        accommodation=accommodation,
                        pricing_type=pricing_type,
                        start_date=start_date,
                        end_date=end_date,
                        price_per_night=price,
                        description=description,
                        is_active=True
                    )
                    rules_processed += 1
                    print(f"🔍 DEBUG: Created pricing rule: {pricing_type} from {start_date} to {end_date} at {price} IRR")

                except (ValueError, TypeError) as e:
                    print(f"❌ ERROR: Invalid pricing rule data: {e}")

            pricing_counter += 1

        print(f"🔍 DEBUG: Processed {rules_processed} pricing rules")
        return rules_processed

    except Exception as e:
        print(f"❌ ERROR in process_pricing_rules: {e}")
        return 0


def process_availability_rules(request, accommodation):
    """Process availability rules from form submission."""
    try:
        rules_processed = 0

        # Clear existing availability rules for this accommodation
        AccommodationAvailability.objects.filter(accommodation=accommodation).delete()

        # Process availability rules from form data
        availability_counter = 1
        while f'availability_start_{availability_counter}' in request.POST:
            start_date_str = request.POST.get(f'availability_start_{availability_counter}')
            end_date_str = request.POST.get(f'availability_end_{availability_counter}')
            is_blocked_str = request.POST.get(f'availability_blocked_{availability_counter}', 'false')
            minimum_nights_str = request.POST.get(f'availability_min_nights_{availability_counter}', '1')
            reason = request.POST.get(f'availability_reason_{availability_counter}', '')

            # Validate required fields
            if start_date_str and end_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                    is_blocked = is_blocked_str.lower() == 'true'
                    minimum_nights = int(minimum_nights_str) if minimum_nights_str else 1

                    # Create availability rule
                    AccommodationAvailability.objects.create(
                        accommodation=accommodation,
                        start_date=start_date,
                        end_date=end_date,
                        is_blocked=is_blocked,
                        minimum_nights=minimum_nights,
                        reason=reason
                    )
                    rules_processed += 1
                    print(f"🔍 DEBUG: Created availability rule: {start_date} to {end_date}, blocked: {is_blocked}")

                except (ValueError, TypeError) as e:
                    print(f"❌ ERROR: Invalid availability rule data: {e}")

            availability_counter += 1

        print(f"🔍 DEBUG: Processed {rules_processed} availability rules")
        return rules_processed

    except Exception as e:
        print(f"❌ ERROR in process_availability_rules: {e}")
        return 0


def process_capacity_rules(request, accommodation):
    """Process guest capacity rules from form submission."""
    try:
        rules_processed = 0

        # Clear existing capacity rules for this accommodation
        GuestCapacityRule.objects.filter(accommodation=accommodation).delete()

        # Process capacity rules for each age category
        age_categories = ['infant', 'child', 'teen', 'adult']

        for category in age_categories:
            price_str = request.POST.get(f'{category}_fee', '0')
            counts_in_capacity_str = request.POST.get(f'{category}_counts_capacity', 'false')

            try:
                price = int(float(price_str)) if price_str else 0
                counts_in_capacity = counts_in_capacity_str.lower() == 'true'

                # Only create rule if there's a price or specific capacity setting
                if price > 0 or not counts_in_capacity:
                    GuestCapacityRule.objects.create(
                        accommodation=accommodation,
                        age_category=category,
                        price_per_night=price,
                        counts_in_capacity=counts_in_capacity
                    )
                    rules_processed += 1
                    print(f"🔍 DEBUG: Created capacity rule: {category} at {price} IRR, counts: {counts_in_capacity}")

            except (ValueError, TypeError) as e:
                print(f"❌ ERROR: Invalid capacity rule data for {category}: {e}")

        print(f"🔍 DEBUG: Processed {rules_processed} capacity rules")
        return rules_processed

    except Exception as e:
        print(f"❌ ERROR in process_capacity_rules: {e}")
        return 0
