"""
Accommodation models for NomadPersia platform.

This module defines models for accommodations including hotels, villas, and suites
across Tehran, Shiraz, and Mashhad with proper relationships and constraints.
"""

from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _


class Accommodation(models.Model):
    """
    Model representing an accommodation listing.

    Supports three types: Hotel, Villa, Suite
    Available in three cities: Tehran, Shiraz, Mashhad
    """

    ACCOMMODATION_TYPES = [
        ('villa_suite', _('Villa & Suite')),
        ('apartment', _('Apartment')),
        ('cottage', _('Cottage')),
    ]

    CITIES = [
        ('tehran', _('Tehran')),
        ('shiraz', _('Shiraz')),
        ('mashhad', _('Mashhad')),
    ]

    STAR_RATINGS = [
        (1, _('1 Star')),
        (2, _('2 Stars')),
        (3, _('3 Stars')),
        (4, _('4 Stars')),
        (5, _('5 Stars')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending Approval')),
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('rejected', _('Rejected')),
    ]

    # Basic Information
    name = models.CharField(
        max_length=200,
        help_text=_('Name of the accommodation')
    )

    description = models.TextField(
        help_text=_('Detailed description of the accommodation')
    )

    accommodation_type = models.CharField(
        max_length=15,
        choices=ACCOMMODATION_TYPES,
        help_text=_('Type of accommodation')
    )

    city = models.CharField(
        max_length=10,
        choices=CITIES,
        help_text=_('City where accommodation is located')
    )

    # Pricing
    price_per_night = models.DecimalField(
        max_digits=14,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text=_('Price per night (supports international currencies)')
    )

    # Rating (only for hotels)
    star_rating = models.PositiveSmallIntegerField(
        choices=STAR_RATINGS,
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_('Star rating (only applicable for hotels)')
    )

    # Amenities
    amenities = models.TextField(
        blank=True,
        help_text=_('List of amenities (comma-separated for MVP)')
    )

    # Images (MVP: text placeholders only)
    image_placeholder = models.CharField(
        max_length=500,
        default='[Accommodation Image Placeholder]',
        help_text=_('Text placeholder for accommodation images (MVP only)')
    )

    # Enhanced fields - now available after migration

    # Capacity and Layout
    standard_capacity = models.PositiveSmallIntegerField(
        default=2,
        validators=[MinValueValidator(1), MaxValueValidator(20)],
        help_text=_('Standard number of guests included in base price')
    )

    max_capacity = models.PositiveSmallIntegerField(
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(30)],
        help_text=_('Maximum number of guests allowed (optional)')
    )

    bedrooms = models.PositiveSmallIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Number of bedrooms')
    )

    bathrooms = models.PositiveSmallIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(10)],
        help_text=_('Number of bathrooms')
    )

    property_size = models.PositiveIntegerField(
        blank=True,
        null=True,
        validators=[MinValueValidator(20)],
        help_text=_('Property size in square meters')
    )

    # Enhanced Pricing
    extra_guest_fee = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        help_text=_('Additional fee per guest above standard capacity')
    )

    # Booking Policies
    instant_booking = models.BooleanField(
        default=False,
        help_text=_('Allow instant booking without host approval')
    )

    CHECK_IN_TIME_CHOICES = [
        ('12:00', _('12:00 PM')),
        ('14:00', _('2:00 PM')),
        ('16:00', _('4:00 PM')),
    ]

    check_in_time = models.CharField(
        max_length=5,
        choices=CHECK_IN_TIME_CHOICES,
        default='14:00',
        help_text=_('Available check-in time')
    )

    CANCELLATION_POLICIES = [
        ('flexible', _('Flexible - 24 hours before')),
        ('moderate', _('Moderate - 5 days before')),
        ('strict', _('Strict - 10 days before')),
    ]

    cancellation_policy = models.CharField(
        max_length=10,
        choices=CANCELLATION_POLICIES,
        default='moderate',
        help_text=_('Cancellation policy for guests')
    )

    check_in_time = models.CharField(
        max_length=5,
        blank=True,
        help_text=_('Latest check-in time (e.g., 22:00)')
    )

    CHECK_OUT_TIME_CHOICES = [
        ('12:00', _('12:00 PM')),
        ('14:00', _('2:00 PM')),
        ('16:00', _('4:00 PM')),
    ]

    check_out_time = models.CharField(
        max_length=5,
        choices=CHECK_OUT_TIME_CHOICES,
        default='12:00',
        help_text=_('Required check-out time')
    )

    additional_policies = models.TextField(
        blank=True,
        help_text=_('Additional house rules and policies for guests')
    )

    town = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('Town, suburb, or district within the city')
    )

    # Contact and Location Details
    address = models.TextField(
        help_text=_('Complete property address including street, neighborhood, and postal code')
    )

    reservation_phone = models.CharField(
        max_length=20,
        help_text=_('Phone number for reservations and guest contact')
    )

    geolocation = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text=_('GPS coordinates in format: latitude,longitude')
    )

    # Iranian-Specific Booking Policies
    smoking_allowed = models.BooleanField(
        default=False,
        help_text=_('Smoking is permitted in the property')
    )

    same_gender_groups_only = models.BooleanField(
        default=False,
        help_text=_('Only accept all-male or all-female groups')
    )

    national_id_required = models.BooleanField(
        default=False,
        help_text=_('Guests must present national ID upon check-in')
    )

    pets_not_allowed = models.BooleanField(
        default=True,
        help_text=_('Pets are prohibited in the property')
    )

    events_not_allowed = models.BooleanField(
        default=True,
        help_text=_('Events and parties are prohibited')
    )

    no_24hour_checkin = models.BooleanField(
        default=True,
        help_text=_('24-hour check-in is not available')
    )

    # Ownership and Status
    host = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        related_name='accommodations',
        help_text=_('Host who owns this accommodation')
    )

    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default='pending',
        help_text=_('Current status of the accommodation listing')
    )

    # Review and rating fields
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name=_('Average Rating'),
        help_text=_('Average rating from all reviews (calculated automatically)')
    )

    total_reviews = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Total Reviews'),
        help_text=_('Total number of published reviews (calculated automatically)')
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Accommodation')
        verbose_name_plural = _('Accommodations')
        indexes = [
            models.Index(fields=['city', 'accommodation_type']),
            models.Index(fields=['price_per_night']),
            models.Index(fields=['star_rating']),
            models.Index(fields=['status']),
            models.Index(fields=['host']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_city_display()})"

    def is_villa_suite(self):
        """Check if accommodation is a villa or suite."""
        return self.accommodation_type == 'villa_suite'

    def is_active(self):
        """Check if accommodation is active and bookable."""
        return self.status == 'active'

    def get_amenities_list(self):
        """Return amenities as a list."""
        if self.amenities:
            return [amenity.strip() for amenity in self.amenities.split(',')]
        return []

    def get_effective_max_capacity(self):
        """Get the maximum capacity, defaulting to standard capacity if not set."""
        return self.max_capacity or self.standard_capacity

    def calculate_total_price(self, nights, guests):
        """Calculate total price for given nights and guests."""
        base_price = self.price_per_night * nights
        extra_guests = max(0, guests - self.standard_capacity)
        extra_fee = extra_guests * self.extra_guest_fee  # One-time fee, not per night
        return base_price + extra_fee

    def update_average_rating(self):
        """Update the average rating and total reviews count."""
        from django.db.models import Avg, Count

        # Get published reviews for this accommodation
        reviews_data = self.reviews.filter(is_published=True).aggregate(
            avg_rating=Avg('overall_rating'),
            total_count=Count('id')
        )

        self.average_rating = reviews_data['avg_rating']
        self.total_reviews = reviews_data['total_count'] or 0
        self.save(update_fields=['average_rating', 'total_reviews'])

    @property
    def star_display(self):
        """Return star display for average rating."""
        if self.average_rating:
            rating = int(round(self.average_rating))
            return '★' * rating + '☆' * (5 - rating)
        return '☆' * 5

    @property
    def rating_percentage(self):
        """Return rating as percentage for progress bars."""
        if self.average_rating:
            return (self.average_rating / 5) * 100
        return 0

    def clean(self):
        """Custom validation."""
        from django.core.exceptions import ValidationError

        # Max capacity should be >= standard capacity
        if self.max_capacity and self.max_capacity < self.standard_capacity:
            raise ValidationError(_('Maximum capacity cannot be less than standard capacity.'))

        # Extra guest fee should be set if max capacity > standard capacity
        if (self.max_capacity and self.max_capacity > self.standard_capacity and
            self.extra_guest_fee == 0):
            raise ValidationError(_('Extra guest fee should be set when maximum capacity exceeds standard capacity.'))

    def save(self, *args, **kwargs):
        """Override save to run validation."""
        self.full_clean()
        super().save(*args, **kwargs)

    def get_main_photo(self):
        """Get the main photo for this accommodation."""
        try:
            return self.photos.filter(is_main=True).first()
        except:
            return None

    def get_photos_by_type(self, photo_type):
        """Get photos of a specific type."""
        try:
            return self.photos.filter(photo_type=photo_type)
        except:
            return []

    def has_photos(self):
        """Check if this accommodation has any photos."""
        try:
            return self.photos.exists()
        except:
            return False

    def get_price_for_date(self, date):
        """Get the price for a specific date, considering dynamic pricing."""
        try:
            from datetime import datetime
            if isinstance(date, str):
                date = datetime.strptime(date, '%Y-%m-%d').date()

            # Check for specific date pricing first (most specific to least specific)
            date_pricing = self.date_pricing.filter(
                start_date__lte=date,
                end_date__gte=date,
                is_active=True
            ).order_by('-created_at').first()

            if date_pricing:
                print(f"🔍 DEBUG: Using dynamic pricing for {date}: {date_pricing.price_per_night} IRR ({date_pricing.get_pricing_type_display()})")
                return int(date_pricing.price_per_night)

            # Fall back to base price
            print(f"🔍 DEBUG: Using base pricing for {date}: {self.price_per_night} IRR")
            return int(self.price_per_night)
        except Exception as e:
            print(f"❌ ERROR in get_price_for_date: {e}")
            return int(self.price_per_night)

    def calculate_total_price(self, start_date, end_date, guest_count, adults=0, teens=0, children=0, infants=0):
        """Calculate total price for a stay including extra guest fees and age-specific pricing."""
        from datetime import timedelta

        try:
            total_price = 0
            current_date = start_date
            nights = (end_date - start_date).days

            print(f"🔍 DEBUG: Calculating price for {nights} nights, {guest_count} guests")
            print(f"🔍 DEBUG: Guest breakdown - Adults: {adults}, Teens: {teens}, Children: {children}, Infants: {infants}")

            # Calculate accommodation cost per night
            accommodation_cost = 0
            while current_date < end_date:
                daily_price = self.get_price_for_date(current_date)
                accommodation_cost += daily_price
                current_date += timedelta(days=1)

            total_price += accommodation_cost
            print(f"🔍 DEBUG: Base accommodation cost: {accommodation_cost} IRR")

            # Calculate extra guest fees (guests beyond standard capacity) - one-time fee
            if guest_count > self.standard_capacity:
                extra_guests = min(
                    guest_count - self.standard_capacity,
                    self.max_capacity - self.standard_capacity
                )
                extra_guest_cost = extra_guests * self.extra_guest_fee  # One-time fee, not per night
                total_price += extra_guest_cost
                print(f"🔍 DEBUG: Extra guest fee: {extra_guests} guests × {self.extra_guest_fee} IRR = {extra_guest_cost} IRR (one-time fee)")

            # Calculate age-specific fees if capacity rules exist
            try:
                capacity_rules = self.capacity_rules.all()
                if capacity_rules.exists():
                    age_specific_cost = 0

                    for rule in capacity_rules:
                        guest_count_for_category = 0

                        if rule.age_category == 'infant':
                            guest_count_for_category = infants
                        elif rule.age_category == 'child':
                            guest_count_for_category = children
                        elif rule.age_category == 'teen':
                            guest_count_for_category = teens
                        elif rule.age_category == 'adult':
                            guest_count_for_category = adults

                        if guest_count_for_category > 0 and rule.price_per_night > 0:
                            category_cost = guest_count_for_category * rule.price_per_night * nights
                            age_specific_cost += category_cost
                            print(f"🔍 DEBUG: {rule.get_age_category_display()} fee: {guest_count_for_category} × {rule.price_per_night} IRR × {nights} nights = {category_cost} IRR")

                    total_price += age_specific_cost
                    print(f"🔍 DEBUG: Total age-specific fees: {age_specific_cost} IRR")
            except Exception as e:
                print(f"⚠️ WARNING: Could not calculate age-specific fees: {e}")

            print(f"🔍 DEBUG: Final total price: {total_price} IRR")
            return int(total_price)

        except Exception as e:
            print(f"❌ ERROR in calculate_total_price: {e}")
            # Fallback calculation
            nights = (end_date - start_date).days
            fallback_price = self.price_per_night * nights
            if guest_count > self.standard_capacity:
                extra_guests = guest_count - self.standard_capacity
                fallback_price += extra_guests * self.extra_guest_fee  # One-time fee, not per night
            return int(fallback_price)


# Photo-related models

import os
from django.core.validators import FileExtensionValidator


def accommodation_photo_upload_path(instance, filename):
    """Generate upload path for accommodation photos."""
    return f'accommodations/{instance.accommodation.id}/photos/{filename}'


class AccommodationPhoto(models.Model):
    """Model for storing accommodation photos."""

    PHOTO_TYPES = [
        ('main', _('Main Photo')),
        ('interior', _('Interior View')),
        ('bedroom', _('Bedroom')),
        ('kitchen', _('Kitchen')),
        ('bathroom', _('Bathroom')),
        ('exterior', _('Exterior View')),
        ('amenity', _('Amenity')),
        ('other', _('Other')),
    ]

    accommodation = models.ForeignKey(
        Accommodation,
        on_delete=models.CASCADE,
        related_name='photos',
        help_text=_('Accommodation this photo belongs to')
    )

    image = models.ImageField(
        upload_to=accommodation_photo_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp'])],
        help_text=_('Photo file (JPG, PNG, WebP)')
    )

    photo_type = models.CharField(
        max_length=20,
        choices=PHOTO_TYPES,
        default='other',
        help_text=_('Type/category of this photo')
    )

    caption = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Optional caption for this photo')
    )

    order = models.PositiveSmallIntegerField(
        default=0,
        help_text=_('Display order (0 = first)')
    )

    is_main = models.BooleanField(
        default=False,
        help_text=_('Is this the main/featured photo?')
    )

    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'uploaded_at']
        verbose_name = _('Accommodation Photo')
        verbose_name_plural = _('Accommodation Photos')

    def __str__(self):
        return f"{self.accommodation.name} - {self.get_photo_type_display()}"

    def save(self, *args, **kwargs):
        # If this is set as main photo, unset other main photos
        if self.is_main:
            AccommodationPhoto.objects.filter(
                accommodation=self.accommodation,
                is_main=True
            ).exclude(pk=self.pk).update(is_main=False)
        super().save(*args, **kwargs)


class AccommodationPricing(models.Model):
    """
    Model for dynamic pricing based on date ranges.
    """

    PRICING_TYPES = [
        ('weekday', _('Weekday Pricing')),
        ('weekend', _('Weekend Pricing')),
        ('holiday', _('Public Holiday')),
        ('peak_season', _('Peak Season')),
        ('custom', _('Custom Date Range')),
    ]

    accommodation = models.ForeignKey(
        Accommodation,
        on_delete=models.CASCADE,
        related_name='date_pricing',
        help_text=_('Accommodation this pricing applies to')
    )

    pricing_type = models.CharField(
        max_length=20,
        choices=PRICING_TYPES,
        default='custom',
        help_text=_('Type of pricing rule')
    )

    start_date = models.DateField(
        help_text=_('Start date for this pricing rule')
    )

    end_date = models.DateField(
        help_text=_('End date for this pricing rule')
    )

    price_per_night = models.PositiveIntegerField(
        help_text=_('Price per night in Toman for this date range')
    )

    description = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Optional description for this pricing rule')
    )

    is_active = models.BooleanField(
        default=True,
        help_text=_('Whether this pricing rule is active')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Accommodation Pricing')
        verbose_name_plural = _('Accommodation Pricing Rules')

    def __str__(self):
        return f"{self.accommodation.name} - {self.get_pricing_type_display()} ({self.start_date} to {self.end_date})"

    def clean(self):
        """Validate pricing rule."""
        from django.core.exceptions import ValidationError

        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError('Start date cannot be after end date.')


class AccommodationAvailability(models.Model):
    """
    Model for managing accommodation availability and booking rules.
    """

    accommodation = models.ForeignKey(
        Accommodation,
        on_delete=models.CASCADE,
        related_name='availability_rules',
        help_text=_('Accommodation this availability rule applies to')
    )

    start_date = models.DateField(
        help_text=_('Start date for blocked period')
    )

    end_date = models.DateField(
        help_text=_('End date for blocked period')
    )

    is_blocked = models.BooleanField(
        default=True,
        help_text=_('Whether these dates are blocked for booking')
    )

    minimum_nights = models.PositiveSmallIntegerField(
        default=1,
        help_text=_('Minimum number of nights required for this period')
    )

    reason = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Reason for blocking these dates')
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['start_date']
        verbose_name = _('Accommodation Availability')
        verbose_name_plural = _('Accommodation Availability Rules')

    def __str__(self):
        status = 'Blocked' if self.is_blocked else 'Available'
        return f"{self.accommodation.name} - {status} ({self.start_date} to {self.end_date})"

    def clean(self):
        """Validate availability rule."""
        from django.core.exceptions import ValidationError

        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError('Start date cannot be after end date.')


class GuestCapacityRule(models.Model):
    """
    Model for managing guest capacity and age-specific pricing rules.
    """

    AGE_CATEGORIES = [
        ('infant', _('Infant (0-2 years)')),
        ('child', _('Child (3-12 years)')),
        ('teen', _('Teen (13-17 years)')),
        ('adult', _('Adult (18+ years)')),
    ]

    accommodation = models.ForeignKey(
        Accommodation,
        on_delete=models.CASCADE,
        related_name='capacity_rules',
        help_text=_('Accommodation this capacity rule applies to')
    )

    age_category = models.CharField(
        max_length=20,
        choices=AGE_CATEGORIES,
        help_text=_('Age category for this rule')
    )

    price_per_night = models.PositiveIntegerField(
        default=0,
        help_text=_('Additional price per night for this age category (0 = free)')
    )

    is_counted_in_capacity = models.BooleanField(
        default=True,
        help_text=_('Whether this age category counts towards total capacity')
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['accommodation', 'age_category']
        verbose_name = _('Guest Capacity Rule')
        verbose_name_plural = _('Guest Capacity Rules')

    def __str__(self):
        return f"{self.accommodation.name} - {self.get_age_category_display()}"
