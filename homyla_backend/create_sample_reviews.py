#!/usr/bin/env python
"""
Simple script to create sample reviews for testing
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from accommodations.models import Accommodation
from bookings.models import Booking
from reviews.models import Review

User = get_user_model()

def create_sample_reviews():
    print("Creating sample reviews...")
    
    # Get some accommodations and users
    accommodations = Accommodation.objects.filter(
        status='active',
        accommodation_type__in=['villa_suite', 'apartment', 'cottage']
    )[:5]
    
    users = User.objects.filter(profile__role='guest')[:5]
    
    if not accommodations.exists():
        print("No valid accommodations found!")
        return
    
    if not users.exists():
        print("No guest users found!")
        return
    
    # Sample review data
    reviews_data = [
        {
            'overall_rating': 5,
            'cleanliness_rating': 5,
            'location_rating': 4,
            'value_rating': 5,
            'communication_rating': 5,
            'checkin_rating': 4,
            'accuracy_rating': 5,
            'review_text': 'اقامتگاه بسیار تمیز و مرتب بود. میزبان بسیار مهربان و پاسخگو بود. قطعا دوباره رزرو خواهم کرد.',
            'host_response': 'از اقامت شما خوشحالیم. امیدواریم دوباره میهمان ما باشید.'
        },
        {
            'overall_rating': 4,
            'cleanliness_rating': 4,
            'location_rating': 5,
            'value_rating': 4,
            'communication_rating': 4,
            'checkin_rating': 4,
            'accuracy_rating': 4,
            'review_text': 'موقعیت عالی و دسترسی آسان به مراکز خرید. امکانات کامل و مطابق توضیحات.',
            'host_response': 'متشکریم از نظر مثبت شما. سعی می‌کنیم همیشه بهترین خدمات را ارائه دهیم.'
        },
        {
            'overall_rating': 5,
            'cleanliness_rating': 5,
            'location_rating': 5,
            'value_rating': 4,
            'communication_rating': 5,
            'checkin_rating': 5,
            'accuracy_rating': 5,
            'review_text': 'تجربه فوق‌العاده‌ای داشتیم. اقامتگاه دقیقا مطابق عکس‌ها بود و حتی بهتر.',
            'host_response': 'خوشحالیم که تجربه خوبی داشتید. منتظر بازگشت شما هستیم.'
        },
        {
            'overall_rating': 4,
            'cleanliness_rating': 4,
            'location_rating': 4,
            'value_rating': 5,
            'communication_rating': 5,
            'checkin_rating': 4,
            'accuracy_rating': 4,
            'review_text': 'قیمت مناسب و کیفیت عالی. پیشنهاد می‌کنم به دوستان.',
            'host_response': None
        },
        {
            'overall_rating': 3,
            'cleanliness_rating': 3,
            'location_rating': 4,
            'value_rating': 3,
            'communication_rating': 4,
            'checkin_rating': 3,
            'accuracy_rating': 3,
            'review_text': 'تجربه خوبی بود اما انتظار بیشتری داشتم. در کل راضی هستم.',
            'host_response': 'سپاس از انتخاب اقامتگاه ما. نظرات شما برای ما ارزشمند است.'
        }
    ]
    
    created_count = 0
    
    for i, (accommodation, user) in enumerate(zip(accommodations, users)):
        try:
            # Check if review already exists
            if Review.objects.filter(guest=user, accommodation=accommodation).exists():
                print(f"Review already exists for {user.username} -> {accommodation.name}")
                continue
            
            # Create a booking first (directly in database to bypass validation)
            check_in = timezone.now().date() - timedelta(days=30)
            check_out = check_in + timedelta(days=3)
            
            booking = Booking(
                accommodation=accommodation,
                guest=user,
                check_in_date=check_in,
                check_out_date=check_out,
                adults=2,
                teens=0,
                children=0,
                infants=0,
                total_price=accommodation.price_per_night * 3,
                status='completed'
            )
            # Save without calling full_clean to bypass validation
            booking.save()
            
            # Create review
            review_data = reviews_data[i % len(reviews_data)]
            
            review = Review(
                guest=user,
                accommodation=accommodation,
                booking=booking,
                **review_data
            )
            
            if review_data['host_response']:
                review.host_response_date = timezone.now()
            
            review.save()
            
            created_count += 1
            print(f"Created review {created_count}: {user.username} -> {accommodation.name}")
            
        except Exception as e:
            print(f"Failed to create review {i+1}: {str(e)}")
            continue
    
    print(f"Successfully created {created_count} sample reviews!")
    
    # Update accommodation ratings
    print("Updating accommodation average ratings...")
    for accommodation in accommodations:
        try:
            accommodation.update_average_rating()
            print(f"Updated rating for {accommodation.name}: {accommodation.average_rating}")
        except Exception as e:
            print(f"Failed to update rating for {accommodation.name}: {str(e)}")
    
    print("Sample review creation completed!")

if __name__ == '__main__':
    create_sample_reviews()
