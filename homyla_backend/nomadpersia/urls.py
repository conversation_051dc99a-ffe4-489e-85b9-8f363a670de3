"""
URL configuration for nomadpersia project.

NomadPersia - Travel accommodation booking platform for Iran
Main URL routing for the entire application.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect
from django.views.generic import TemplateView

def home_redirect(request):
    """Redirect home page to accommodations search."""
    return redirect('accommodations:search')

urlpatterns = [
    # Admin interface
    path("admin/", admin.site.urls),

    # Home page
    path("", TemplateView.as_view(template_name='home.html'), name="home"),

    # App URLs
    path("accounts/", include("accounts.urls")),
    path("accommodations/", include("accommodations.urls")),
    path("bookings/", include("bookings.urls")),
    path("dashboard/", include("dashboard.urls")),
    path("api/", include("api.urls")),
    path("reviews/", include("reviews.urls")),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
