#!/usr/bin/env python3
"""
Enable Photo Gallery functionality for NomadPersia.
Run this script to add photo gallery database tables and enable full photo functionality.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.core.management import execute_from_command_line
from django.db import connection
from accommodations.models import Accommodation

def check_photo_table_exists():
    """Check if the photo table already exists."""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='accommodations_accommodationphoto';
        """)
        return cursor.fetchone() is not None

def create_photo_tables():
    """Create the photo tables using Django migrations."""
    print("🔧 Creating photo gallery database tables...")
    
    try:
        # Run the photo migration
        execute_from_command_line(['manage.py', 'migrate', 'accommodations', '0003'])
        print("✅ Photo gallery tables created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def test_photo_functionality():
    """Test that photo functionality is working."""
    print("🧪 Testing photo functionality...")
    
    try:
        # Test that we can access photo methods without errors
        accommodation = Accommodation.objects.first()
        if accommodation:
            has_photos = accommodation.has_photos()
            main_photo = accommodation.get_main_photo()
            print(f"✅ Photo functionality test passed!")
            print(f"   - Has photos: {has_photos}")
            print(f"   - Main photo: {main_photo}")
        else:
            print("⚠️  No accommodations found to test with")
        return True
    except Exception as e:
        print(f"❌ Photo functionality test failed: {e}")
        return False

def show_status():
    """Show current photo gallery status."""
    print("📊 Photo Gallery Status:")
    
    # Check if table exists
    table_exists = check_photo_table_exists()
    print(f"   Database tables: {'✅ Ready' if table_exists else '❌ Missing'}")
    
    # Check accommodation count
    accommodation_count = Accommodation.objects.count()
    print(f"   Accommodations: {accommodation_count} properties")
    
    if table_exists:
        from accommodations.models import AccommodationPhoto
        photo_count = AccommodationPhoto.objects.count()
        print(f"   Photos uploaded: {photo_count} photos")
    
    return table_exists

def main():
    print("📸 NomadPersia Photo Gallery Setup")
    print("=" * 50)
    
    # Check current status
    table_exists = show_status()
    
    if table_exists:
        print("\n✅ Photo gallery is already enabled!")
        print("🎯 You can now:")
        print("   1. Upload photos when creating/editing accommodations")
        print("   2. View photo galleries on property detail pages")
        print("   3. Manage photos through the admin interface")
        return
    
    print("\n🤔 Photo gallery tables are missing.")
    response = input("Do you want to enable photo gallery functionality? (y/N): ")
    
    if response.lower() not in ['y', 'yes']:
        print("❌ Photo gallery setup cancelled.")
        return
    
    # Create tables
    success = create_photo_tables()
    if not success:
        print("\n❌ Failed to create photo tables.")
        print("💡 Try running manually: python manage.py migrate accommodations")
        return
    
    # Test functionality
    test_success = test_photo_functionality()
    if not test_success:
        print("\n⚠️  Photo tables created but functionality test failed.")
        print("💡 Check the Django logs for more details.")
        return
    
    print("\n🎉 Photo gallery successfully enabled!")
    print("\n🚀 What's now available:")
    print("   ✅ Photo upload interface in property creation/editing")
    print("   ✅ Photo gallery display on property detail pages")
    print("   ✅ Photo management in Django admin")
    print("   ✅ Professional image handling and storage")
    
    print("\n📋 Next steps:")
    print("   1. Create or edit an accommodation")
    print("   2. Upload photos using the gallery interface")
    print("   3. View the enhanced property listings")
    
    print("\n🎯 Photo categories supported:")
    print("   - Main Photo (featured image)")
    print("   - Interior View (living spaces)")
    print("   - Bedroom (sleeping areas)")
    print("   - Kitchen (cooking facilities)")
    print("   - Bathroom (sanitary facilities)")
    print("   - Additional Photos (unlimited)")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("💡 Make sure you're running this from the project directory.")
        sys.exit(1)
