#!/usr/bin/env python
"""
Create test bookings for multiple accommodations to test review system
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation
from bookings.models import Booking
from datetime import timedelta
from django.utils import timezone

User = get_user_model()

def create_test_bookings():
    print("=== CREATING TEST BOOKINGS FOR REVIEW SYSTEM ===")
    
    # Get guest users
    guest_users = User.objects.filter(profile__role='guest')
    print(f"Found {guest_users.count()} guest users")
    
    # Get all accommodations
    accommodations = Accommodation.objects.all()
    print(f"Found {accommodations.count()} accommodations")
    
    if accommodations.count() == 0:
        print("❌ No accommodations found. Please run: python manage.py load_sample_data")
        return
    
    created_count = 0
    
    # Create bookings for each guest user across different accommodations
    for i, user in enumerate(guest_users[:5]):  # Take first 5 guest users
        for j, accommodation in enumerate(accommodations[:3]):  # Take first 3 accommodations
            # Skip if booking already exists
            existing_booking = Booking.objects.filter(
                guest=user, 
                accommodation=accommodation
            ).first()
            
            if existing_booking:
                # Update existing booking to completed if it's not
                if existing_booking.status != 'completed':
                    existing_booking.status = 'completed'
                    existing_booking.save(update_fields=['status'])
                    print(f"✅ Updated existing booking for {user.username} at {accommodation.name} to completed")
                else:
                    print(f"📝 Booking already exists and completed for {user.username} at {accommodation.name}")
                continue
            
            # Create a completed booking
            try:
                booking = Booking.objects.create(
                    accommodation=accommodation,
                    guest=user,
                    check_in_date=timezone.now().date() + timedelta(days=80 + i*10 + j*5),
                    check_out_date=timezone.now().date() + timedelta(days=83 + i*10 + j*5),
                    adults=2,
                    teens=0,
                    children=0,
                    infants=0,
                    total_price=accommodation.price_per_night * 3,
                    status='completed'
                )
                print(f"✅ Created completed booking for {user.username} at {accommodation.name} (ID: {accommodation.id})")
                created_count += 1
            except Exception as e:
                print(f"❌ Error creating booking for {user.username} at {accommodation.name}: {e}")
    
    print(f"\n🎉 Created {created_count} new bookings")
    
    # Show review eligibility summary
    print("\n=== REVIEW ELIGIBILITY SUMMARY ===")
    for accommodation in accommodations:
        eligible_users = []
        for user in guest_users:
            completed_booking = Booking.objects.filter(
                guest=user,
                accommodation=accommodation,
                status='completed'
            ).exists()
            
            if completed_booking:
                eligible_users.append(user.username)
        
        print(f"🏠 {accommodation.name} (ID: {accommodation.id})")
        print(f"   Eligible reviewers: {', '.join(eligible_users) if eligible_users else 'None'}")

if __name__ == '__main__':
    create_test_bookings()
