#!/usr/bin/env python3
"""
Test script to verify template paths are correctly configured
after frontend/backend separation.
"""

import os
import sys
from pathlib import Path

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')

try:
    import django
    from django.conf import settings
    from django.template.loader import get_template
    from django.template import TemplateDoesNotExist
    
    # Initialize Django
    django.setup()
    
    print("🔍 Testing template path configuration...")
    print(f"📁 Template directories: {settings.TEMPLATES[0]['DIRS']}")
    
    # Test if template directories exist
    for template_dir in settings.TEMPLATES[0]['DIRS']:
        if template_dir.exists():
            print(f"✅ Template directory exists: {template_dir}")
            print(f"   Contents: {list(template_dir.iterdir())[:5]}...")  # Show first 5 items
        else:
            print(f"❌ Template directory missing: {template_dir}")
    
    # Test loading some key templates
    test_templates = [
        'base.html',
        'home.html',
        'accounts/login.html',
        'accommodations/search.html',
        'dashboard/admin_dashboard.html'
    ]
    
    print("\n🧪 Testing template loading...")
    for template_name in test_templates:
        try:
            template = get_template(template_name)
            print(f"✅ Successfully loaded: {template_name}")
        except TemplateDoesNotExist as e:
            print(f"❌ Failed to load: {template_name} - {e}")
        except Exception as e:
            print(f"⚠️  Error loading {template_name}: {e}")
    
    print("\n📊 Static files configuration:")
    print(f"   STATIC_URL: {settings.STATIC_URL}")
    print(f"   STATIC_ROOT: {settings.STATIC_ROOT}")
    print(f"   STATICFILES_DIRS: {settings.STATICFILES_DIRS}")
    
    # Check if static directories exist
    for static_dir in settings.STATICFILES_DIRS:
        if static_dir.exists():
            print(f"✅ Static directory exists: {static_dir}")
        else:
            print(f"❌ Static directory missing: {static_dir}")
    
    print("\n🎉 Template path test completed!")
    
except ImportError as e:
    print(f"❌ Django not available: {e}")
    print("💡 You may need to install dependencies or activate a virtual environment")
except Exception as e:
    print(f"❌ Error during test: {e}")
