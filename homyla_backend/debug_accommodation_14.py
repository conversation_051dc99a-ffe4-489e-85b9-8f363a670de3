#!/usr/bin/env python
"""
Debug script to check accommodation ID 14
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from accommodations.models import Accommodation
from bookings.models import Booking

def debug_accommodation_14():
    print("=== DEBUGGING ACCOMMODATION ID 14 ===")
    
    try:
        accommodation = Accommodation.objects.get(id=14)
        print(f"✅ Accommodation found: {accommodation.name}")
        print(f"   Status: {accommodation.status}")
        print(f"   Host: {accommodation.host}")
        print(f"   Price: {accommodation.price_per_night} IRR")
        print(f"   Capacity: {accommodation.standard_capacity}-{accommodation.max_capacity}")
        
        # Check recent bookings for this accommodation
        recent_bookings = Booking.objects.filter(accommodation=accommodation).order_by('-created_at')[:5]
        print(f"\n📋 Recent bookings ({recent_bookings.count()}):")
        
        for booking in recent_bookings:
            print(f"   - Booking {booking.id}: {booking.guest.username} ({booking.status})")
            print(f"     Dates: {booking.check_in_date} to {booking.check_out_date}")
            print(f"     Has accommodation: {booking.accommodation is not None}")
            
    except Accommodation.DoesNotExist:
        print("❌ Accommodation ID 14 not found")
        
        # Show available accommodations
        accommodations = Accommodation.objects.all()
        print(f"\n📋 Available accommodations ({accommodations.count()}):")
        for acc in accommodations:
            print(f"   - ID {acc.id}: {acc.name} ({acc.status})")
    
    # Check for bookings without accommodations
    orphaned_bookings = Booking.objects.filter(accommodation__isnull=True)
    print(f"\n🔍 Orphaned bookings (no accommodation): {orphaned_bookings.count()}")
    
    for booking in orphaned_bookings:
        print(f"   - Booking {booking.id}: {booking.guest.username} ({booking.status})")
        print(f"     Dates: {booking.check_in_date} to {booking.check_out_date}")

if __name__ == '__main__':
    debug_accommodation_14()
