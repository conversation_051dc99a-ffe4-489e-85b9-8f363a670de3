#!/usr/bin/env python3
"""
Simple API test script for NomadPersia API

This script tests the basic functionality of the REST API endpoints.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8082/api"

def test_api_endpoints():
    """Test basic API endpoints."""
    
    print("🔍 Testing NomadPersia API Endpoints")
    print("=" * 50)
    
    # Test 1: API Documentation
    print("\n1. Testing API Documentation...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ API Documentation accessible")
        else:
            print(f"❌ API Documentation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ API Documentation error: {e}")
    
    # Test 2: Accommodations List (Public)
    print("\n2. Testing Accommodations List (Public)...")
    try:
        response = requests.get(f"{BASE_URL}/v1/accommodations/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Accommodations List: {data.get('count', 0)} accommodations found")
            if data.get('results'):
                print(f"   Sample accommodation: {data['results'][0].get('name', 'N/A')}")
        else:
            print(f"❌ Accommodations List failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Accommodations List error: {e}")
    
    # Test 3: Accommodations Search
    print("\n3. Testing Accommodations Search...")
    try:
        response = requests.get(f"{BASE_URL}/v1/accommodations/search/", params={
            'city': 'tehran',
            'min_price': 100000
        })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Accommodations Search: {len(data)} results for Tehran")
        else:
            print(f"❌ Accommodations Search failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Accommodations Search error: {e}")
    
    # Test 4: User Registration
    print("\n4. Testing User Registration...")
    try:
        user_data = {
            'username': 'testuser_api',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'phone_number': '09123456789',
            'is_guest': True
        }
        response = requests.post(f"{BASE_URL}/auth/register/", json=user_data)
        if response.status_code == 201:
            print("✅ User Registration successful")
        elif response.status_code == 400:
            error_data = response.json()
            if 'already exists' in str(error_data):
                print("✅ User Registration (user already exists)")
            else:
                print(f"❌ User Registration validation error: {error_data}")
        else:
            print(f"❌ User Registration failed: {response.status_code}")
    except Exception as e:
        print(f"❌ User Registration error: {e}")
    
    # Test 5: Authentication
    print("\n5. Testing Authentication...")
    try:
        auth_data = {
            'username': 'testuser_api',
            'password': 'testpass123'
        }
        response = requests.post(f"{BASE_URL}/auth/login/", json=auth_data)
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access')
            print("✅ Authentication successful")
            
            # Test authenticated endpoint
            headers = {'Authorization': f'Bearer {access_token}'}
            profile_response = requests.get(f"{BASE_URL}/auth/profile/", headers=headers)
            if profile_response.status_code == 200:
                profile_data = profile_response.json()
                print(f"✅ Profile access: {profile_data.get('user', {}).get('username', 'N/A')}")
            else:
                print(f"❌ Profile access failed: {profile_response.status_code}")
                
        else:
            print(f"❌ Authentication failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Authentication error: {e}")
    
    # Test 6: Bookings (requires authentication)
    print("\n6. Testing Bookings Endpoint...")
    try:
        # Try without authentication first
        response = requests.get(f"{BASE_URL}/v1/bookings/")
        if response.status_code == 401:
            print("✅ Bookings endpoint properly protected (401 Unauthorized)")
        else:
            print(f"❌ Bookings endpoint security issue: {response.status_code}")
    except Exception as e:
        print(f"❌ Bookings endpoint error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API Testing Complete!")
    print("\nAPI Endpoints Available:")
    print("📖 Documentation: http://localhost:8082/api/")
    print("🏠 Accommodations: http://localhost:8082/api/v1/accommodations/")
    print("🔍 Search: http://localhost:8082/api/v1/accommodations/search/")
    print("📅 Bookings: http://localhost:8082/api/v1/bookings/")
    print("🔐 Auth: http://localhost:8082/api/auth/")

if __name__ == "__main__":
    test_api_endpoints()
