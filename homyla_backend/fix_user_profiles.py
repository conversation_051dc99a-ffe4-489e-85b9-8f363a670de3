#!/usr/bin/env python
"""
Fix user profiles for review system
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import UserProfile
from bookings.models import Booking
from accommodations.models import Accommodation

User = get_user_model()

def fix_user_profiles():
    print("=== FIXING USER PROFILES ===")
    
    # Create profiles for users who don't have them
    users_without_profiles = []
    for user in User.objects.all():
        if not hasattr(user, 'profile'):
            users_without_profiles.append(user)
    
    print(f"Found {len(users_without_profiles)} users without profiles")
    
    for user in users_without_profiles:
        # Determine role based on username
        if 'guest' in user.username.lower() or 'test_reviewer' in user.username:
            role = 'guest'
        elif 'host' in user.username.lower():
            role = 'host'
        elif 'admin' in user.username.lower():
            role = 'admin'
        else:
            role = 'guest'  # Default to guest
        
        profile = UserProfile.objects.create(
            user=user,
            role=role
        )
        print(f"✅ Created {role} profile for {user.username}")
    
    print("Profile creation completed!")
    
    # Fix guest_sara to have a completed booking for accommodation 12
    try:
        guest_sara = User.objects.get(username='guest_sara')
        accommodation = Accommodation.objects.get(id=12)
        
        # Check if she has any bookings for accommodation 12
        sara_booking_12 = Booking.objects.filter(
            guest=guest_sara,
            accommodation=accommodation
        ).first()
        
        if not sara_booking_12:
            # Create a completed booking for her
            from datetime import timedelta
            from django.utils import timezone
            
            booking = Booking.objects.create(
                accommodation=accommodation,
                guest=guest_sara,
                check_in_date=timezone.now().date() + timedelta(days=70),
                check_out_date=timezone.now().date() + timedelta(days=73),
                adults=2,
                teens=0,
                children=0,
                infants=0,
                total_price=accommodation.price_per_night * 3,
                status='completed'
            )
            print(f"✅ Created completed booking for guest_sara")
        else:
            # Update existing booking to completed
            sara_booking_12.status = 'completed'
            sara_booking_12.save(update_fields=['status'])
            print(f"✅ Updated existing booking for guest_sara to completed")
            
    except Exception as e:
        print(f"❌ Error fixing guest_sara: {e}")
    
    # Also fix john user to have a completed booking
    try:
        john = User.objects.get(username='john')
        accommodation = Accommodation.objects.get(id=12)
        
        # Check if he has any bookings for accommodation 12
        john_booking_12 = Booking.objects.filter(
            guest=john,
            accommodation=accommodation
        ).first()
        
        if not john_booking_12:
            # Create a completed booking for him
            from datetime import timedelta
            from django.utils import timezone
            
            booking = Booking.objects.create(
                accommodation=accommodation,
                guest=john,
                check_in_date=timezone.now().date() + timedelta(days=80),
                check_out_date=timezone.now().date() + timedelta(days=82),
                adults=1,
                teens=0,
                children=0,
                infants=0,
                total_price=accommodation.price_per_night * 2,
                status='completed'
            )
            print(f"✅ Created completed booking for john")
            
    except Exception as e:
        print(f"❌ Error fixing john: {e}")

if __name__ == '__main__':
    fix_user_profiles()
