#!/usr/bin/env python
"""
Check for orphaned bookings without accommodations
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from bookings.models import Booking
from accommodations.models import Accommodation

def check_orphaned_bookings():
    print("=== CHECKING FOR ORPHANED BOOKINGS ===")
    
    # Check for bookings without accommodations
    try:
        orphaned_bookings = Booking.objects.filter(accommodation__isnull=True)
        print(f"Found {orphaned_bookings.count()} orphaned bookings (no accommodation)")
        
        for booking in orphaned_bookings:
            print(f"  - Booking ID {booking.id}: Guest {booking.guest.username}")
            print(f"    Dates: {booking.check_in_date} to {booking.check_out_date}")
            print(f"    Status: {booking.status}")
        
        if orphaned_bookings.exists():
            print(f"\n🔧 FIXING: Deleting orphaned bookings...")
            deleted_count = orphaned_bookings.delete()[0]
            print(f"✅ Deleted {deleted_count} orphaned bookings")
        
    except Exception as e:
        print(f"❌ Error checking orphaned bookings: {e}")
    
    # Check for bookings with invalid accommodation references
    try:
        all_bookings = Booking.objects.all()
        print(f"\nChecking {all_bookings.count()} total bookings for invalid accommodation references...")
        
        invalid_bookings = []
        for booking in all_bookings:
            try:
                # Try to access the accommodation
                acc_name = booking.accommodation.name
                print(f"✅ Booking {booking.id}: {acc_name}")
            except Exception as e:
                print(f"❌ Booking {booking.id}: Invalid accommodation reference - {e}")
                invalid_bookings.append(booking)
        
        if invalid_bookings:
            print(f"\n🔧 FIXING: Found {len(invalid_bookings)} bookings with invalid accommodation references")
            for booking in invalid_bookings:
                print(f"  Deleting booking {booking.id}")
                booking.delete()
            print(f"✅ Deleted {len(invalid_bookings)} invalid bookings")
        else:
            print(f"✅ All bookings have valid accommodation references")
            
    except Exception as e:
        print(f"❌ Error checking booking accommodation references: {e}")
    
    # Check accommodation 13 specifically
    try:
        accommodation_13 = Accommodation.objects.get(id=13)
        print(f"\n✅ Accommodation 13 exists: {accommodation_13.name}")
        print(f"   Status: {accommodation_13.status}")
        print(f"   Host: {accommodation_13.host}")
        
        # Check bookings for accommodation 13
        bookings_13 = Booking.objects.filter(accommodation=accommodation_13)
        print(f"   Bookings: {bookings_13.count()}")
        
        for booking in bookings_13:
            print(f"     - Booking {booking.id}: {booking.guest.username} ({booking.status})")
            
    except Accommodation.DoesNotExist:
        print(f"❌ Accommodation 13 does not exist")
    except Exception as e:
        print(f"❌ Error checking accommodation 13: {e}")
    
    print(f"\n=== ORPHANED BOOKING CHECK COMPLETE ===")

if __name__ == '__main__':
    check_orphaned_bookings()
