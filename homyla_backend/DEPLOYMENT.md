# 🚀 NomadPersia Deployment Guide

This guide provides comprehensive instructions for deploying NomadPersia to production environments.

## 📋 Pre-Deployment Checklist

### 1. Environment Preparation
- [ ] Python 3.8+ installed
- [ ] PostgreSQL database set up
- [ ] Web server (Nginx/Apache) configured
- [ ] SSL certificate obtained
- [ ] Domain name configured
- [ ] Email service configured

### 2. Security Configuration
- [ ] `DEBUG=False` in production
- [ ] Strong `SECRET_KEY` generated
- [ ] `ALLOWED_HOSTS` properly configured
- [ ] Database credentials secured
- [ ] HTTPS enforced
- [ ] Security headers enabled

### 3. Performance Optimization
- [ ] Static files optimization
- [ ] Database indexing
- [ ] Caching configured
- [ ] Media files CDN (optional)

## 🐧 Ubuntu/Debian Deployment

### 1. System Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install python3 python3-pip python3-venv postgresql postgresql-contrib nginx git -y

# Install additional dependencies
sudo apt install python3-dev libpq-dev build-essential -y
```

### 2. Database Setup

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE nomadpersia;
CREATE USER nomadpersia_user WITH PASSWORD 'your_secure_password';
ALTER ROLE nomadpersia_user SET client_encoding TO 'utf8';
ALTER ROLE nomadpersia_user SET default_transaction_isolation TO 'read committed';
ALTER ROLE nomadpersia_user SET timezone TO 'UTC';
GRANT ALL PRIVILEGES ON DATABASE nomadpersia TO nomadpersia_user;
\q
```

### 3. Application Deployment

```bash
# Create application directory
sudo mkdir -p /var/www/nomadpersia
sudo chown $USER:$USER /var/www/nomadpersia

# Clone repository
cd /var/www/nomadpersia
git clone https://github.com/yourusername/nomadpersia.git .

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install gunicorn psycopg2-binary

# Create production environment file
cat > .env << EOF
SECRET_KEY=your-very-secure-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgresql://nomadpersia_user:your_secure_password@localhost:5432/nomadpersia
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
EOF

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Load sample data (optional)
python manage.py load_sample_data
```

### 4. Gunicorn Configuration

```bash
# Create Gunicorn configuration
cat > gunicorn.conf.py << EOF
bind = "127.0.0.1:8000"
workers = 3
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "www-data"
group = "www-data"
EOF

# Create systemd service
sudo cat > /etc/systemd/system/nomadpersia.service << EOF
[Unit]
Description=NomadPersia Django Application
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/nomadpersia
Environment=PATH=/var/www/nomadpersia/venv/bin
ExecStart=/var/www/nomadpersia/venv/bin/gunicorn --config gunicorn.conf.py nomadpersia.wsgi:application
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# Set permissions
sudo chown -R www-data:www-data /var/www/nomadpersia
sudo chmod -R 755 /var/www/nomadpersia

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable nomadpersia
sudo systemctl start nomadpersia
sudo systemctl status nomadpersia
```

### 5. Nginx Configuration

```bash
# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/nomadpersia << EOF
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";

    client_max_body_size 10M;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_redirect off;
    }

    location /static/ {
        alias /var/www/nomadpersia/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /var/www/nomadpersia/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/nomadpersia /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🐳 Docker Deployment

### 1. Dockerfile

```dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . /app/

# Collect static files
RUN python manage.py collectstatic --noinput

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "nomadpersia.wsgi:application"]
```

### 2. Docker Compose

```yaml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: nomadpersia
      POSTGRES_USER: nomadpersia_user
      POSTGRES_PASSWORD: your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"

  web:
    build: .
    command: gunicorn nomadpersia.wsgi:application --bind 0.0.0.0:8000
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=**********************************************************/nomadpersia
    depends_on:
      - db

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

### 3. Deploy with Docker

```bash
# Build and run
docker-compose up -d --build

# Run migrations
docker-compose exec web python manage.py migrate

# Create superuser
docker-compose exec web python manage.py createsuperuser

# Load sample data
docker-compose exec web python manage.py load_sample_data
```

## ☁️ Cloud Platform Deployment

### Heroku Deployment

```bash
# Install Heroku CLI
# Create Procfile
echo "web: gunicorn nomadpersia.wsgi:application --log-file -" > Procfile

# Create runtime.txt
echo "python-3.11.0" > runtime.txt

# Create Heroku app
heroku create your-app-name

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Set environment variables
heroku config:set SECRET_KEY=your-secret-key
heroku config:set DEBUG=False
heroku config:set ALLOWED_HOSTS=your-app-name.herokuapp.com

# Deploy
git add .
git commit -m "Deploy to Heroku"
git push heroku main

# Run migrations
heroku run python manage.py migrate

# Create superuser
heroku run python manage.py createsuperuser
```

### AWS EC2 Deployment

1. **Launch EC2 Instance**
   - Choose Ubuntu 20.04 LTS
   - Configure security groups (HTTP, HTTPS, SSH)
   - Create or use existing key pair

2. **Connect and Setup**
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-ip
   # Follow Ubuntu deployment steps above
   ```

3. **Configure Load Balancer** (optional)
   - Create Application Load Balancer
   - Configure target groups
   - Set up health checks

## 🔧 Post-Deployment Configuration

### 1. SSL Certificate Setup

```bash
# Using Let's Encrypt (Certbot)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### 2. Monitoring Setup

```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Set up log rotation
sudo cat > /etc/logrotate.d/nomadpersia << EOF
/var/www/nomadpersia/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

### 3. Backup Configuration

```bash
# Create backup script
cat > /var/www/nomadpersia/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
pg_dump nomadpersia > /var/backups/nomadpersia_\$DATE.sql
tar -czf /var/backups/nomadpersia_media_\$DATE.tar.gz /var/www/nomadpersia/media/
find /var/backups -name "nomadpersia_*" -mtime +7 -delete
EOF

chmod +x /var/www/nomadpersia/backup.sh

# Add to crontab
echo "0 2 * * * /var/www/nomadpersia/backup.sh" | sudo crontab -
```

## 🚨 Troubleshooting

### Common Issues

1. **Static files not loading**
   ```bash
   python manage.py collectstatic --clear
   sudo systemctl restart nomadpersia
   ```

2. **Database connection errors**
   - Check PostgreSQL service status
   - Verify database credentials
   - Check firewall settings

3. **Permission errors**
   ```bash
   sudo chown -R www-data:www-data /var/www/nomadpersia
   sudo chmod -R 755 /var/www/nomadpersia
   ```

4. **Service not starting**
   ```bash
   sudo journalctl -u nomadpersia -f
   sudo systemctl status nomadpersia
   ```

### Performance Optimization

1. **Database Optimization**
   ```sql
   -- Add indexes for frequently queried fields
   CREATE INDEX idx_accommodation_city ON accommodations_accommodation(city);
   CREATE INDEX idx_accommodation_status ON accommodations_accommodation(status);
   CREATE INDEX idx_booking_status ON bookings_booking(status);
   ```

2. **Caching Setup**
   ```bash
   # Install Redis
   sudo apt install redis-server
   
   # Add to Django settings
   CACHES = {
       'default': {
           'BACKEND': 'django_redis.cache.RedisCache',
           'LOCATION': 'redis://127.0.0.1:6379/1',
           'OPTIONS': {
               'CLIENT_CLASS': 'django_redis.client.DefaultClient',
           }
       }
   }
   ```

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# Create health check endpoint
curl -f http://localhost:8000/health/ || exit 1

# Monitor logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
sudo journalctl -u nomadpersia -f
```

### Regular Maintenance

1. **Weekly Tasks**
   - Review error logs
   - Check disk space
   - Monitor performance metrics

2. **Monthly Tasks**
   - Update dependencies
   - Review security logs
   - Database maintenance

3. **Quarterly Tasks**
   - Security audit
   - Performance optimization
   - Backup testing

---

**For additional support, refer to the main README.md or contact the development team.**
