# 📚 NomadPersia API Documentation

This document provides comprehensive API documentation for the NomadPersia platform, including all endpoints, request/response formats, and authentication requirements.

## 🔐 Authentication

NomadPersia uses Django's session-based authentication. All authenticated endpoints require a valid session cookie.

### Authentication Flow

1. **Register**: `POST /accounts/register/`
2. **Login**: `POST /accounts/login/`
3. **Access Protected Resources**: Include session cookie in requests
4. **Logout**: `POST /accounts/logout/`

### Role-Based Access Control

- **Guest**: Can search accommodations and make bookings
- **Host**: Can manage properties and bookings
- **Admin**: Full platform access

## 🏠 Accounts API

### Register User
```http
POST /accounts/register/
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
username=newuser
email=<EMAIL>
password1=securepassword123
password2=securepassword123
first_name=John
last_name=Doe
role=guest
```

**Response (Success):**
```http
HTTP/1.1 302 Found
Location: /accounts/profile/
```

**Response (Error):**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Form with validation errors -->
```

### Login User
```http
POST /accounts/login/
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
username=testuser
password=securepassword123
```

**Response (Success):**
```http
HTTP/1.1 302 Found
Location: /
Set-Cookie: sessionid=abc123...; HttpOnly; Path=/
```

### Get User Profile
```http
GET /accounts/profile/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- User profile page -->
```

### Update User Profile
```http
POST /accounts/profile/edit/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
first_name=John
last_name=Doe
email=<EMAIL>
phone_number=+************
bio=Travel enthusiast
```

### Logout User
```http
POST /accounts/logout/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 302 Found
Location: /
```

## 🏨 Accommodations API

### Search Accommodations
```http
GET /accommodations/
```

**Query Parameters:**
- `search_query` (string): Search in name and description
- `city` (string): Filter by city (tehran, shiraz, mashhad)
- `accommodation_type` (string): Filter by type (hotel, villa, suite)
- `star_rating` (integer): Filter by star rating (1-5)
- `min_price` (decimal): Minimum price per night
- `max_price` (decimal): Maximum price per night
- `sort_by` (string): Sort order (price, -price, name, -created_at)
- `page` (integer): Page number for pagination

**Example Request:**
```http
GET /accommodations/?city=tehran&accommodation_type=hotel&min_price=1000000&max_price=3000000&page=1
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Search results page with accommodations -->
```

### Get Accommodation Details
```http
GET /accommodations/{id}/
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Accommodation detail page -->
```

### Create Accommodation (Host Only)
```http
POST /accommodations/host/create/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
name=Beautiful Tehran Hotel
description=A luxurious hotel in the heart of Tehran
accommodation_type=hotel
city=tehran
price_per_night=2500000
star_rating=4
amenities=WiFi, Pool, Spa, Restaurant
image_placeholder=Hotel exterior and interior photos
```

**Response (Success):**
```http
HTTP/1.1 302 Found
Location: /accommodations/host/
```

### Update Accommodation (Host Only)
```http
POST /accommodations/host/{id}/edit/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:** (Same as create)

### Toggle Accommodation Status (Host Only)
```http
POST /accommodations/host/{id}/toggle-status/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 302 Found
Location: /accommodations/{id}/
```

### Get Host Accommodations (Host Only)
```http
GET /accommodations/host/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Host accommodations list -->
```

## 📅 Bookings API

### Create Booking (Guest Only)
```http
POST /bookings/create/{accommodation_id}/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
check_in_date=2024-07-15
check_out_date=2024-07-18
number_of_guests=2
special_requests=Late check-in please
```

**Response (Success):**
```http
HTTP/1.1 302 Found
Location: /bookings/{booking_id}/
```

### Get Booking Details
```http
GET /bookings/{id}/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Booking detail page -->
```

### Update Booking Status (Host/Admin Only)
```http
POST /bookings/{id}/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
status=confirmed
host_notes=Welcome! Looking forward to hosting you.
```

### Cancel Booking (Guest Only)
```http
POST /bookings/{id}/cancel/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
reason=Change of travel plans
```

### Get User Bookings
```http
GET /bookings/my-bookings/
Cookie: sessionid=abc123...
```

**Query Parameters:**
- `status` (string): Filter by status
- `accommodation_name` (string): Search by accommodation name
- `check_in_from` (date): Filter by check-in date range
- `check_in_to` (date): Filter by check-in date range
- `sort_by` (string): Sort order
- `page` (integer): Page number

### Get Host Bookings (Host Only)
```http
GET /bookings/host-bookings/
Cookie: sessionid=abc123...
```

**Query Parameters:** (Same as user bookings)

## 📊 Dashboard API

### Get Host Dashboard (Host Only)
```http
GET /dashboard/host/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Host dashboard with statistics -->
```

### Get Admin Dashboard (Admin Only)
```http
GET /dashboard/admin/
Cookie: sessionid=abc123...
```

**Response:**
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Admin dashboard with platform statistics -->
```

### User Management (Admin Only)
```http
GET /dashboard/admin/users/
Cookie: sessionid=abc123...
```

**Query Parameters:**
- `role` (string): Filter by user role
- `search` (string): Search users
- `page` (integer): Page number

### Accommodation Moderation (Admin Only)
```http
GET /dashboard/admin/accommodations/
Cookie: sessionid=abc123...
```

**Query Parameters:**
- `status` (string): Filter by status
- `city` (string): Filter by city
- `page` (integer): Page number

### Booking Analytics (Admin Only)
```http
GET /dashboard/admin/bookings/
Cookie: sessionid=abc123...
```

**Query Parameters:**
- `range` (integer): Date range in days (default: 30)

### Site Settings (Admin Only)
```http
GET /dashboard/admin/settings/
Cookie: sessionid=abc123...
```

```http
POST /dashboard/admin/settings/
Cookie: sessionid=abc123...
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
site_name=NomadPersia
site_description=Discover Iran's Hidden Gems
contact_email=<EMAIL>
maintenance_mode=false
allow_registration=true
```

## 📝 Data Models

### User Profile
```json
{
  "id": 1,
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User"
  },
  "role": "guest",
  "phone_number": "+************",
  "date_of_birth": "1990-01-01",
  "bio": "Travel enthusiast",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Accommodation
```json
{
  "id": 1,
  "name": "Luxury Hotel Tehran",
  "description": "A beautiful hotel in Tehran",
  "accommodation_type": "hotel",
  "city": "tehran",
  "price_per_night": "2500000.00",
  "star_rating": 4,
  "amenities": "WiFi, Pool, Spa, Restaurant",
  "image_placeholder": "Hotel photos description",
  "host": {
    "id": 2,
    "username": "host_tehran",
    "first_name": "Ahmad",
    "last_name": "Hosseini"
  },
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Booking
```json
{
  "id": 1,
  "guest": {
    "id": 1,
    "username": "guest_sara",
    "first_name": "Sara",
    "last_name": "Ahmadi"
  },
  "accommodation": {
    "id": 1,
    "name": "Luxury Hotel Tehran",
    "city": "tehran"
  },
  "check_in_date": "2024-07-15",
  "check_out_date": "2024-07-18",
  "number_of_guests": 2,
  "price_per_night": "2500000.00",
  "total_price": "7500000.00",
  "special_requests": "Late check-in please",
  "host_notes": "Welcome!",
  "status": "confirmed",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "confirmed_at": "2024-01-01T01:00:00Z",
  "cancelled_at": null
}
```

## 🔒 Security Considerations

### Rate Limiting
- Authentication endpoints: 5 attempts per hour per IP
- Booking creation: 10 requests per hour per user
- Search endpoints: 100 requests per hour per IP

### Input Validation
- All form inputs are validated server-side
- CSRF protection on all POST requests
- XSS prevention through template escaping
- SQL injection protection through ORM

### Session Security
- HttpOnly session cookies
- Secure cookies in production (HTTPS)
- Session timeout after 24 hours
- Session invalidation on logout

## 📊 Response Codes

### Success Codes
- `200 OK`: Successful GET request
- `302 Found`: Successful POST request (redirect)

### Error Codes
- `400 Bad Request`: Invalid form data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## 🧪 Testing Examples

### Using cURL

**Register User:**
```bash
curl -X POST http://localhost:8000/accounts/register/ \
  -d "username=testuser&email=<EMAIL>&password1=testpass123&password2=testpass123&role=guest" \
  -c cookies.txt
```

**Login:**
```bash
curl -X POST http://localhost:8000/accounts/login/ \
  -d "username=testuser&password=testpass123" \
  -c cookies.txt -b cookies.txt
```

**Search Accommodations:**
```bash
curl -X GET "http://localhost:8000/accommodations/?city=tehran&accommodation_type=hotel" \
  -b cookies.txt
```

**Create Booking:**
```bash
curl -X POST http://localhost:8000/bookings/create/1/ \
  -d "check_in_date=2024-07-15&check_out_date=2024-07-18&number_of_guests=2" \
  -b cookies.txt
```

### Using Python Requests

```python
import requests

# Create session
session = requests.Session()

# Login
login_data = {
    'username': 'testuser',
    'password': 'testpass123'
}
response = session.post('http://localhost:8000/accounts/login/', data=login_data)

# Search accommodations
params = {
    'city': 'tehran',
    'accommodation_type': 'hotel',
    'min_price': 1000000,
    'max_price': 3000000
}
response = session.get('http://localhost:8000/accommodations/', params=params)

# Create booking
booking_data = {
    'check_in_date': '2024-07-15',
    'check_out_date': '2024-07-18',
    'number_of_guests': 2,
    'special_requests': 'Late check-in please'
}
response = session.post('http://localhost:8000/bookings/create/1/', data=booking_data)
```

## 📞 Support

For API support and questions:
- **Documentation**: This file and README.md
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

---

**API Version**: 1.0  
**Last Updated**: 2024-01-01  
**Base URL**: `https://nomadpersia.com` (production) / `http://localhost:8000` (development)
