#!/usr/bin/env python
"""
Quick test to verify booking creation fix
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation
from bookings.models import Booking
from datetime import date, timedelta

User = get_user_model()

def test_booking_fix():
    print("=== TESTING BOOKING CREATION FIX ===")
    
    # Get accommodation 13
    try:
        accommodation = Accommodation.objects.get(id=13)
        print(f"✅ Found accommodation: {accommodation.name}")
    except Accommodation.DoesNotExist:
        print("❌ Accommodation ID 13 not found")
        return
    
    # Get guest user
    guest_user = User.objects.filter(profile__role='guest').first()
    if not guest_user:
        print("❌ No guest users found")
        return
    
    print(f"👤 Using guest: {guest_user.username}")
    
    # Test creating booking object directly (as done in the new view)
    try:
        booking = Booking(
            accommodation=accommodation,
            guest=guest_user,
            check_in_date=date.today() + timedelta(days=5),
            check_out_date=date.today() + timedelta(days=8),
            number_of_guests=2,
            special_requests='Test booking',
        )
        
        print(f"✅ Booking object created successfully")
        print(f"   Accommodation: {booking.accommodation}")
        print(f"   Guest: {booking.guest}")
        print(f"   __str__: {booking}")
        
        # Set additional fields
        booking.adults = 2
        booking.teens = 0
        booking.children = 0
        booking.infants = 0
        booking.total_guests = 2
        
        # Calculate total price
        booking.total_price = accommodation.calculate_total_price(
            booking.check_in_date,
            booking.check_out_date,
            booking.total_guests,
            adults=booking.adults,
            teens=booking.teens,
            children=booking.children,
            infants=booking.infants
        )
        
        print(f"✅ Total price calculated: {booking.total_price} IRR")
        
        # Test clean method
        booking.clean()
        print(f"✅ Booking.clean() successful")
        
        print(f"✅ All tests passed - booking creation should work now")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_booking_fix()
