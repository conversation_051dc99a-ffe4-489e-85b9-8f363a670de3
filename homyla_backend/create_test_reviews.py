#!/usr/bin/env python
"""
Create test reviews directly in database
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from accommodations.models import Accommodation
from bookings.models import Booking
from reviews.models import Review

User = get_user_model()

def create_test_reviews():
    print("Creating test reviews...")
    
    # Get accommodation
    try:
        accommodation = Accommodation.objects.get(id=12)  # Espinas
        print(f"Found accommodation: {accommodation.name}")
    except Accommodation.DoesNotExist:
        print("Accommodation with ID 12 not found!")
        return
    
    # Get or create test users
    test_users = []
    for i in range(3):
        username = f"test_guest_{i+1}"
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': f'{username}@example.com',
                'first_name': f'Test',
                'last_name': f'Guest {i+1}'
            }
        )
        if created:
            print(f"Created user: {username}")
        
        # Ensure user has guest profile
        if hasattr(user, 'profile'):
            user.profile.role = 'guest'
            user.profile.save()
        
        test_users.append(user)
    
    # Sample review data
    reviews_data = [
        {
            'overall_rating': 5,
            'cleanliness_rating': 5,
            'location_rating': 4,
            'value_rating': 5,
            'communication_rating': 5,
            'checkin_rating': 4,
            'accuracy_rating': 5,
            'review_text': 'Excellent accommodation! Very clean and well-maintained. The host was extremely helpful and responsive. Would definitely book again.',
            'host_response': 'Thank you for your wonderful review! We are delighted that you enjoyed your stay and look forward to welcoming you back.'
        },
        {
            'overall_rating': 4,
            'cleanliness_rating': 4,
            'location_rating': 5,
            'value_rating': 4,
            'communication_rating': 4,
            'checkin_rating': 4,
            'accuracy_rating': 4,
            'review_text': 'Great location with easy access to shopping centers. Facilities were complete and as described in the listing.',
            'host_response': 'Thank you for your positive feedback. We strive to provide the best service and are glad you found everything as described.'
        },
        {
            'overall_rating': 5,
            'cleanliness_rating': 5,
            'location_rating': 5,
            'value_rating': 4,
            'communication_rating': 5,
            'checkin_rating': 5,
            'accuracy_rating': 5,
            'review_text': 'Amazing experience! The accommodation was exactly as shown in the photos, even better. Highly recommend to friends.',
            'host_response': None
        }
    ]
    
    created_count = 0
    
    for i, (user, review_data) in enumerate(zip(test_users, reviews_data)):
        try:
            # Check if review already exists
            if Review.objects.filter(guest=user, accommodation=accommodation).exists():
                print(f"Review already exists for {user.username}")
                continue
            
            # Create a booking first (directly without validation)
            check_in = timezone.now().date() - timedelta(days=30 + i*10)
            check_out = check_in + timedelta(days=3)
            
            # Create booking directly in database
            booking = Booking.objects.create(
                accommodation=accommodation,
                guest=user,
                check_in_date=check_in,
                check_out_date=check_out,
                adults=2,
                teens=0,
                children=0,
                infants=0,
                total_price=accommodation.price_per_night * 3,
                status='completed'
            )
            print(f"Created booking for {user.username}")
            
            # Create review
            review = Review.objects.create(
                guest=user,
                accommodation=accommodation,
                booking=booking,
                overall_rating=review_data['overall_rating'],
                cleanliness_rating=review_data['cleanliness_rating'],
                location_rating=review_data['location_rating'],
                value_rating=review_data['value_rating'],
                communication_rating=review_data['communication_rating'],
                checkin_rating=review_data['checkin_rating'],
                accuracy_rating=review_data['accuracy_rating'],
                review_text=review_data['review_text'],
                host_response=review_data['host_response'],
                host_response_date=timezone.now() if review_data['host_response'] else None,
                is_verified=True,
                is_published=True
            )
            
            created_count += 1
            print(f"Created review {created_count}: {user.username} -> {accommodation.name}")
            
        except Exception as e:
            print(f"Failed to create review for {user.username}: {str(e)}")
            continue
    
    print(f"Successfully created {created_count} test reviews!")
    
    # Update accommodation rating
    try:
        accommodation.update_average_rating()
        print(f"Updated rating for {accommodation.name}: {accommodation.average_rating} ({accommodation.total_reviews} reviews)")
    except Exception as e:
        print(f"Failed to update rating: {str(e)}")
    
    print("Test review creation completed!")

if __name__ == '__main__':
    create_test_reviews()
