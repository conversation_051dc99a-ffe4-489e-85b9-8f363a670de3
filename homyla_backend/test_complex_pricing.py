#!/usr/bin/env python
"""
Test script for complex pricing scenarios combining seasonal pricing and extra guest fees
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation, AccommodationPricing
from bookings.models import Booking
from datetime import date, timedelta
from decimal import Decimal

User = get_user_model()

def test_complex_pricing_scenarios():
    print("=== TESTING COMPLEX PRICING SCENARIOS ===")
    
    # Get or create test accommodation
    accommodation = Accommodation.objects.first()
    if not accommodation:
        print("❌ No accommodations found. Please run: python manage.py load_sample_data")
        return
    
    print(f"🏠 Testing with accommodation: {accommodation.name}")
    
    # Set up the exact scenario from the issue description
    # Base rental price: $1,000 per night (convert to IRR for testing)
    base_price = 1000 * 50000  # Assuming 1 USD = 50,000 IRR
    accommodation.price_per_night = base_price
    accommodation.standard_capacity = 6  # Standard capacity: 6 guests
    accommodation.max_capacity = 8       # Maximum capacity: 8 guests
    accommodation.extra_guest_fee = 500 * 50000  # Extra guest fee: $500 per guest (in IRR)
    accommodation.save()
    
    print(f"   Base price: {accommodation.price_per_night:,} IRR (${accommodation.price_per_night // 50000})")
    print(f"   Standard capacity: {accommodation.standard_capacity} guests")
    print(f"   Maximum capacity: {accommodation.max_capacity} guests")
    print(f"   Extra guest fee: {accommodation.extra_guest_fee:,} IRR (${accommodation.extra_guest_fee // 50000}) per guest")
    
    # Create holiday pricing rule (50% increase)
    today = date.today()
    holiday_start = today + timedelta(days=10)
    holiday_end = today + timedelta(days=13)  # 3 nights
    holiday_price = int(accommodation.price_per_night * Decimal('1.5'))  # 50% increase
    
    holiday_pricing, created = AccommodationPricing.objects.get_or_create(
        accommodation=accommodation,
        pricing_type='holiday',
        start_date=holiday_start,
        end_date=holiday_end,
        defaults={
            'price_per_night': holiday_price,
            'description': 'Holiday Premium (50% increase)',
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ Created holiday pricing: {holiday_start} to {holiday_end}")
    else:
        # Update existing rule
        holiday_pricing.price_per_night = holiday_price
        holiday_pricing.is_active = True
        holiday_pricing.save()
        print(f"📝 Updated existing holiday pricing")
    
    print(f"   Holiday price: {holiday_price:,} IRR (${holiday_price // 50000}) per night")
    
    print("\n=== TEST SCENARIO: 8 GUESTS, 3 NIGHTS, HOLIDAY PERIOD ===")
    
    # Test the exact scenario from the issue
    guest_count = 8
    nights = 3
    start_date = holiday_start
    end_date = holiday_start + timedelta(days=nights)
    
    print(f"📅 Booking period: {start_date} to {end_date} ({nights} nights)")
    print(f"👥 Guest count: {guest_count} guests")
    print(f"   - Standard capacity: {accommodation.standard_capacity} guests (included)")
    print(f"   - Extra guests: {guest_count - accommodation.standard_capacity} guests")
    
    # Calculate expected pricing manually
    print(f"\n🧮 EXPECTED CALCULATION:")
    print(f"   Base price: {accommodation.price_per_night:,} IRR/night")
    print(f"   Holiday price (50% increase): {holiday_price:,} IRR/night")
    print(f"   Accommodation cost: {holiday_price:,} × {nights} nights = {holiday_price * nights:,} IRR")
    
    extra_guests = guest_count - accommodation.standard_capacity
    extra_guest_total = extra_guests * accommodation.extra_guest_fee
    print(f"   Extra guest fee: {extra_guests} guests × {accommodation.extra_guest_fee:,} IRR = {extra_guest_total:,} IRR (one-time)")
    
    expected_total = (holiday_price * nights) + extra_guest_total
    print(f"   EXPECTED TOTAL: {expected_total:,} IRR (${expected_total // 50000})")
    
    # Test accommodation pricing method
    print(f"\n🔍 TESTING ACCOMMODATION PRICING METHOD:")
    calculated_total = accommodation.calculate_total_price(
        start_date, end_date, guest_count,
        adults=guest_count, teens=0, children=0, infants=0
    )
    
    print(f"   Calculated total: {calculated_total:,} IRR")
    print(f"   Expected total:   {expected_total:,} IRR")
    print(f"   ✅ Match: {calculated_total == expected_total}")
    
    if calculated_total != expected_total:
        print(f"   ❌ MISMATCH! Difference: {abs(calculated_total - expected_total):,} IRR")
    
    # Test individual date pricing
    print(f"\n🔍 TESTING DAILY PRICING:")
    for i in range(nights):
        test_date = start_date + timedelta(days=i)
        daily_price = accommodation.get_price_for_date(test_date)
        print(f"   {test_date}: {daily_price:,} IRR")
        
        if daily_price != holiday_price:
            print(f"   ❌ Expected {holiday_price:,} IRR, got {daily_price:,} IRR")
    
    print(f"\n=== TEST SCENARIO: MIXED PRICING PERIOD ===")
    
    # Test mixed pricing (some regular days, some holiday days)
    mixed_start = holiday_start - timedelta(days=2)  # 2 days before holiday
    mixed_end = holiday_end + timedelta(days=1)      # 1 day after holiday
    mixed_nights = (mixed_end - mixed_start).days
    
    print(f"📅 Mixed period: {mixed_start} to {mixed_end} ({mixed_nights} nights)")
    
    # Calculate expected mixed pricing
    expected_mixed = 0
    for i in range(mixed_nights):
        test_date = mixed_start + timedelta(days=i)
        if holiday_start <= test_date < holiday_end:
            expected_mixed += holiday_price
            print(f"   {test_date}: {holiday_price:,} IRR (holiday)")
        else:
            expected_mixed += accommodation.price_per_night
            print(f"   {test_date}: {accommodation.price_per_night:,} IRR (regular)")
    
    expected_mixed += extra_guest_total  # Add extra guest fee
    
    calculated_mixed = accommodation.calculate_total_price(
        mixed_start, mixed_end, guest_count,
        adults=guest_count, teens=0, children=0, infants=0
    )
    
    print(f"   Expected mixed total: {expected_mixed:,} IRR")
    print(f"   Calculated mixed total: {calculated_mixed:,} IRR")
    print(f"   ✅ Match: {calculated_mixed == expected_mixed}")
    
    print(f"\n=== TEST SCENARIO: REGULAR PERIOD (NO SPECIAL PRICING) ===")
    
    # Test regular period with extra guests
    regular_start = today + timedelta(days=5)
    regular_end = regular_start + timedelta(days=2)
    regular_nights = 2
    
    expected_regular = (accommodation.price_per_night * regular_nights) + extra_guest_total
    calculated_regular = accommodation.calculate_total_price(
        regular_start, regular_end, guest_count,
        adults=guest_count, teens=0, children=0, infants=0
    )
    
    print(f"📅 Regular period: {regular_start} to {regular_end} ({regular_nights} nights)")
    print(f"   Expected regular total: {expected_regular:,} IRR")
    print(f"   Calculated regular total: {calculated_regular:,} IRR")
    print(f"   ✅ Match: {calculated_regular == expected_regular}")
    
    print(f"\n=== SUMMARY ===")
    print(f"✅ Holiday pricing with extra guests: {'PASS' if calculated_total == expected_total else 'FAIL'}")
    print(f"✅ Mixed pricing period: {'PASS' if calculated_mixed == expected_mixed else 'FAIL'}")
    print(f"✅ Regular pricing with extra guests: {'PASS' if calculated_regular == expected_regular else 'FAIL'}")
    
    # Test booking creation
    print(f"\n=== TESTING BOOKING CREATION ===")
    
    guest_user = User.objects.filter(profile__role='guest').first()
    if guest_user:
        print(f"👤 Testing booking creation for user: {guest_user.username}")
        
        # Create test booking (don't save to avoid conflicts)
        test_booking = Booking(
            accommodation=accommodation,
            guest=guest_user,
            check_in_date=start_date,
            check_out_date=end_date,
            adults=guest_count,
            teens=0,
            children=0,
            infants=0,
            total_guests=guest_count
        )
        
        # Set the calculated price
        test_booking.total_price = calculated_total
        
        print(f"   Booking total price: {test_booking.total_price:,} IRR")
        print(f"   Expected price: {expected_total:,} IRR")
        print(f"   ✅ Booking price correct: {test_booking.total_price == expected_total}")
    else:
        print("❌ No guest users found for booking test")

if __name__ == '__main__':
    test_complex_pricing_scenarios()
