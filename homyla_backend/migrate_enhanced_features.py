#!/usr/bin/env python3
"""
Manual migration script for enhanced accommodation features.
Run this script to add the new database columns for enhanced villa/suite listings.

Usage:
    python migrate_enhanced_features.py

This script will:
1. Add new columns to the accommodations_accommodation table
2. Set default values for existing records
3. Update accommodation types from old to new categories
"""

import os
import sys
import django
from django.db import connection

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

def run_migration():
    """Run the manual migration to add enhanced features."""
    
    with connection.cursor() as cursor:
        print("🚀 Starting enhanced features migration...")
        
        try:
            # 1. Add new columns for capacity management
            print("📊 Adding capacity management columns...")
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN standard_capacity INTEGER DEFAULT 2 NOT NULL;
            """)
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN max_capacity INTEGER NULL;
            """)
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN bedrooms INTEGER DEFAULT 1 NOT NULL;
            """)
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN bathrooms INTEGER DEFAULT 1 NOT NULL;
            """)
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN property_size INTEGER NULL;
            """)
            
            print("✅ Capacity management columns added successfully!")
            
            # 2. Add enhanced pricing columns
            print("💰 Adding enhanced pricing columns...")
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN extra_guest_fee DECIMAL(10,2) DEFAULT 0 NOT NULL;
            """)
            
            print("✅ Enhanced pricing columns added successfully!")
            
            # 3. Add booking policy columns
            print("📋 Adding booking policy columns...")
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN instant_booking BOOLEAN DEFAULT 0 NOT NULL;
            """)
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN cancellation_policy VARCHAR(10) DEFAULT 'moderate' NOT NULL;
            """)
            
            cursor.execute("""
                ALTER TABLE accommodations_accommodation 
                ADD COLUMN check_in_time VARCHAR(5) DEFAULT '' NOT NULL;
            """)
            
            print("✅ Booking policy columns added successfully!")
            
            # 4. Update accommodation types
            print("🏠 Updating accommodation types...")
            
            # Update existing types to new categories
            cursor.execute("""
                UPDATE accommodations_accommodation 
                SET accommodation_type = 'villa_suite' 
                WHERE accommodation_type IN ('hotel', 'villa', 'suite');
            """)
            
            print("✅ Accommodation types updated successfully!")
            
            # 5. Set reasonable defaults for existing records
            print("🔧 Setting defaults for existing records...")
            
            # Set standard capacity based on existing data patterns
            cursor.execute("""
                UPDATE accommodations_accommodation 
                SET standard_capacity = 4, 
                    bedrooms = 2, 
                    bathrooms = 1,
                    cancellation_policy = 'moderate'
                WHERE standard_capacity = 2;
            """)
            
            print("✅ Defaults set for existing records!")
            
            print("🎉 Migration completed successfully!")
            print("\n📋 Summary of changes:")
            print("   ✅ Added capacity management (standard_capacity, max_capacity, bedrooms, bathrooms, property_size)")
            print("   ✅ Added enhanced pricing (extra_guest_fee)")
            print("   ✅ Added booking policies (instant_booking, cancellation_policy, check_in_time)")
            print("   ✅ Updated accommodation types to new categories")
            print("   ✅ Set reasonable defaults for existing records")
            print("\n🚀 You can now uncomment the enhanced fields in accommodations/models.py")
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            print("💡 This might be because the columns already exist or there's a database issue.")
            print("   Check the database schema and try again.")
            return False
    
    return True

def check_migration_status():
    """Check if the migration has already been applied."""
    
    with connection.cursor() as cursor:
        try:
            # Try to query one of the new columns
            cursor.execute("SELECT standard_capacity FROM accommodations_accommodation LIMIT 1;")
            print("✅ Enhanced features are already migrated!")
            return True
        except Exception:
            print("📋 Enhanced features need to be migrated.")
            return False

if __name__ == "__main__":
    print("🏨 NomadPersia Enhanced Features Migration")
    print("=" * 50)
    
    # Check if migration is needed
    if check_migration_status():
        print("🎯 No migration needed. Enhanced features are already available!")
        sys.exit(0)
    
    # Confirm before running
    response = input("\n🤔 Do you want to run the enhanced features migration? (y/N): ")
    if response.lower() not in ['y', 'yes']:
        print("❌ Migration cancelled.")
        sys.exit(0)
    
    # Run the migration
    success = run_migration()
    
    if success:
        print("\n🎊 Migration completed! You can now:")
        print("   1. Uncomment the enhanced fields in accommodations/models.py")
        print("   2. Restart the Django server")
        print("   3. Enjoy the enhanced villa and suite listing features!")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
        sys.exit(1)
