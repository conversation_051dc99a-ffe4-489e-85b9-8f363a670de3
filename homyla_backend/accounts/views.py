"""
Authentication and user management views for NomadPersia platform.

This module contains views for user registration, login, logout, profile management,
and role-based functionality.
"""

from django.contrib import messages
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import CreateView
from django.utils.decorators import method_decorator

from .forms import CustomUserCreationForm, CustomAuthenticationForm, UserProfileForm, UserProfileUpdateForm
from .models import UserProfile


class CustomLoginView(LoginView):
    """
    Custom login view with enhanced styling and functionality.
    """
    form_class = CustomAuthenticationForm
    template_name = 'accounts/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        """Redirect based on user role."""
        user = self.request.user
        try:
            profile = user.profile
            if profile.is_admin_user():
                return reverse_lazy('dashboard:admin')
            elif profile.is_host():
                return reverse_lazy('dashboard:host_dashboard')
            else:
                return reverse_lazy('accommodations:search')
        except UserProfile.DoesNotExist:
            return reverse_lazy('accommodations:search')

    def form_valid(self, form):
        """Add success message on login."""
        messages.success(self.request, f'Welcome back, {form.get_user().first_name}!')
        return super().form_valid(form)


class RegisterView(CreateView):
    """
    User registration view with role selection.
    """
    form_class = CustomUserCreationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('accounts:login')

    def form_valid(self, form):
        """Add success message and log user in."""
        response = super().form_valid(form)
        user = self.object  # Get the user from the saved object
        messages.success(
            self.request,
            f'Account created successfully! Welcome to NomadPersia, {user.first_name}!'
        )
        # Automatically log in the user
        login(self.request, user)

        # Redirect based on role
        try:
            profile = user.profile
            if profile.is_host:
                return redirect('dashboard:host_dashboard')
            else:
                return redirect('accommodations:search')
        except UserProfile.DoesNotExist:
            return redirect('accommodations:search')


@login_required
def profile_view(request):
    """
    Comprehensive user profile management page.
    """
    try:
        profile = request.user.profile
    except UserProfile.DoesNotExist:
        # Create profile if it doesn't exist
        profile = UserProfile.objects.create(user=request.user)

    if request.method == 'POST':
        form = UserProfileUpdateForm(
            request.POST,
            request.FILES,
            instance=profile,
            user=request.user
        )
        if form.is_valid():
            form.save()
            messages.success(
                request,
                'Profile updated successfully! Changes will take effect after 24 hours if submitted before 23:30.'
            )
            return redirect('accounts:profile')
    else:
        form = UserProfileUpdateForm(instance=profile, user=request.user)

    context = {
        'form': form,
        'profile': profile,
        'user': request.user,
        'verification_status': profile.get_verification_status(),
        'profile_complete': profile.is_profile_complete(),
        'bank_info_complete': profile.is_bank_info_complete(),
        'identity_verified': profile.is_identity_verified_complete(),
        'ownership_complete': profile.is_ownership_complete(),
        'ownership_status': profile.get_ownership_status(),
        'required_documents': profile.get_required_documents(),
    }
    return render(request, 'accounts/profile.html', context)


@login_required
def edit_profile_view(request):
    """
    Legacy edit profile view - redirects to main profile view.
    """
    return redirect('accounts:profile')


@login_required
def logout_view(request):
    """
    Log out the user and redirect to home page.
    """
    user_name = request.user.first_name or request.user.username
    logout(request)
    messages.success(request, f'Goodbye, {user_name}! You have been logged out successfully.')
    return redirect('accommodations:search')


def role_required(allowed_roles):
    """
    Decorator to restrict access based on user roles.

    Args:
        allowed_roles: List of allowed roles ('guest', 'host', 'admin')
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                messages.error(request, 'You must be logged in to access this page.')
                return redirect('accounts:login')

            try:
                profile = request.user.profile
                if profile.role not in allowed_roles:
                    messages.error(request, 'You do not have permission to access this page.')
                    return redirect('accommodations:search')
            except UserProfile.DoesNotExist:
                messages.error(request, 'Profile not found. Please contact support.')
                return redirect('accommodations:search')

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
