"""
Security utilities and decorators for NomadPersia platform.

This module contains security-related functions, decorators,
and utilities for enhanced platform security.
"""

import hashlib
import secrets
import logging
from functools import wraps
from django.contrib.auth import logout
from django.contrib import messages
from django.shortcuts import redirect
from django.http import HttpResponseForbidden, JsonResponse
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings
from django.contrib.auth.models import User
from datetime import timedelta

logger = logging.getLogger(__name__)


def rate_limit(max_requests=10, window_minutes=60, key_func=None):
    """
    Rate limiting decorator.
    
    Args:
        max_requests: Maximum number of requests allowed
        window_minutes: Time window in minutes
        key_func: Function to generate cache key (default: uses IP)
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(request)
            else:
                ip = get_client_ip(request)
                cache_key = f"rate_limit_{view_func.__name__}_{ip}"
            
            # Check current request count
            current_requests = cache.get(cache_key, 0)
            
            if current_requests >= max_requests:
                logger.warning(
                    f"Rate limit exceeded for {cache_key}: "
                    f"{current_requests} requests"
                )
                
                if request.headers.get('Accept') == 'application/json':
                    return JsonResponse(
                        {'error': 'Rate limit exceeded. Please try again later.'},
                        status=429
                    )
                else:
                    messages.error(
                        request,
                        'Too many requests. Please try again later.'
                    )
                    return redirect('home')
            
            # Increment request count
            cache.set(cache_key, current_requests + 1, window_minutes * 60)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def require_verified_user(view_func):
    """
    Decorator to require user verification.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        # Check if user has a profile
        if not hasattr(request.user, 'profile'):
            messages.error(request, 'Please complete your profile first.')
            return redirect('accounts:profile_create')
        
        # Check if user is active
        if not request.user.is_active:
            logout(request)
            messages.error(request, 'Your account has been deactivated.')
            return redirect('accounts:login')
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def require_secure_session(view_func):
    """
    Decorator to require secure session validation.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        # Check session age
        session_start = request.session.get('session_start')
        if session_start:
            session_age = timezone.now() - timezone.datetime.fromisoformat(session_start)
            if session_age > timedelta(hours=24):  # 24-hour session limit
                logout(request)
                messages.warning(
                    request,
                    'Your session has expired for security reasons. Please log in again.'
                )
                return redirect('accounts:login')
        else:
            request.session['session_start'] = timezone.now().isoformat()
        
        # Validate session integrity
        expected_hash = generate_session_hash(request.user, request.session.session_key)
        stored_hash = request.session.get('session_hash')
        
        if stored_hash and stored_hash != expected_hash:
            logger.warning(
                f"Session integrity check failed for user {request.user.username}"
            )
            logout(request)
            messages.error(
                request,
                'Session security validation failed. Please log in again.'
            )
            return redirect('accounts:login')
        
        # Store session hash if not present
        if not stored_hash:
            request.session['session_hash'] = expected_hash
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def log_security_event(event_type, user=None, details=None, request=None):
    """
    Log security events for monitoring and analysis.
    
    Args:
        event_type: Type of security event
        user: User involved (if any)
        details: Additional details
        request: HTTP request object (if available)
    """
    try:
        log_data = {
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'user': user.username if user else 'Anonymous',
            'details': details or {},
        }
        
        if request:
            log_data.update({
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown'),
                'path': request.path,
                'method': request.method,
            })
        
        logger.warning(f"SECURITY EVENT: {log_data}")
        
        # Store in database for critical events
        critical_events = [
            'failed_login_attempt',
            'account_lockout',
            'suspicious_activity',
            'privilege_escalation_attempt',
        ]
        
        # Database logging disabled for now
        # if event_type in critical_events:
        #     try:
        #         from dashboard.models import ActivityLog
        #         ActivityLog.objects.create(
        #             activity_type='admin_action',
        #             description=f"SECURITY: {event_type}",
        #             user=user,
        #             ip_address=log_data.get('ip_address', ''),
        #             metadata={
        #                 'event_type': event_type,
        #                 'user_agent': log_data.get('user_agent', '')[:255],
        #                 'details': str(details) if details else '',
        #                 'security_event': True
        #             }
        #         )
        #     except Exception as e:
        #         logger.error(f"Failed to log security event to database: {e}")
    
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")


def check_password_strength(password):
    """
    Check password strength and return validation results.
    
    Args:
        password: Password to validate
        
    Returns:
        dict: Validation results with score and feedback
    """
    score = 0
    feedback = []
    
    # Length check
    if len(password) >= 8:
        score += 1
    else:
        feedback.append("Password must be at least 8 characters long")
    
    # Character variety checks
    if any(c.islower() for c in password):
        score += 1
    else:
        feedback.append("Password must contain lowercase letters")
    
    if any(c.isupper() for c in password):
        score += 1
    else:
        feedback.append("Password must contain uppercase letters")
    
    if any(c.isdigit() for c in password):
        score += 1
    else:
        feedback.append("Password must contain numbers")
    
    if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        score += 1
    else:
        feedback.append("Password must contain special characters")
    
    # Common password check
    common_passwords = [
        'password', '123456', 'password123', 'admin', 'qwerty',
        'letmein', 'welcome', 'monkey', '1234567890'
    ]
    
    if password.lower() in common_passwords:
        score = max(0, score - 2)
        feedback.append("Password is too common")
    
    # Determine strength level
    if score >= 4:
        strength = "Strong"
    elif score >= 3:
        strength = "Medium"
    elif score >= 2:
        strength = "Weak"
    else:
        strength = "Very Weak"
    
    return {
        'score': score,
        'strength': strength,
        'feedback': feedback,
        'is_valid': score >= 3
    }


def generate_secure_token(length=32):
    """
    Generate a cryptographically secure random token.
    
    Args:
        length: Length of the token
        
    Returns:
        str: Secure random token
    """
    return secrets.token_urlsafe(length)


def generate_session_hash(user, session_key):
    """
    Generate a hash for session integrity validation.
    
    Args:
        user: User object
        session_key: Session key
        
    Returns:
        str: Session hash
    """
    data = f"{user.id}:{user.username}:{session_key}:{settings.SECRET_KEY}"
    return hashlib.sha256(data.encode()).hexdigest()


def get_client_ip(request):
    """
    Get client IP address from request.
    
    Args:
        request: HTTP request object
        
    Returns:
        str: Client IP address
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', '')
    return ip


def sanitize_input(input_string, max_length=255):
    """
    Sanitize user input to prevent XSS and other attacks.
    
    Args:
        input_string: Input to sanitize
        max_length: Maximum allowed length
        
    Returns:
        str: Sanitized input
    """
    if not input_string:
        return ''
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', 'javascript:', 'data:']
    sanitized = str(input_string)
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    # Limit length
    sanitized = sanitized[:max_length]
    
    # Strip whitespace
    sanitized = sanitized.strip()
    
    return sanitized


def validate_file_upload(uploaded_file, allowed_types=None, max_size_mb=5):
    """
    Validate uploaded files for security.
    
    Args:
        uploaded_file: Uploaded file object
        allowed_types: List of allowed MIME types
        max_size_mb: Maximum file size in MB
        
    Returns:
        dict: Validation results
    """
    if not uploaded_file:
        return {'valid': False, 'error': 'No file provided'}
    
    # Default allowed types
    if allowed_types is None:
        allowed_types = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'text/plain'
        ]
    
    # Check file size
    max_size_bytes = max_size_mb * 1024 * 1024
    if uploaded_file.size > max_size_bytes:
        return {
            'valid': False,
            'error': f'File size exceeds {max_size_mb}MB limit'
        }
    
    # Check file type
    if uploaded_file.content_type not in allowed_types:
        return {
            'valid': False,
            'error': f'File type {uploaded_file.content_type} not allowed'
        }
    
    # Check file extension
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.txt']
    file_extension = uploaded_file.name.lower().split('.')[-1]
    if f'.{file_extension}' not in allowed_extensions:
        return {
            'valid': False,
            'error': f'File extension .{file_extension} not allowed'
        }
    
    return {'valid': True, 'error': None}
