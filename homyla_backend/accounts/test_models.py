"""
Test cases for accounts app models.

This module contains comprehensive tests for UserProfile model
and related functionality.
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from .models import UserProfile


class UserProfileModelTest(TestCase):
    """Test cases for UserProfile model."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_create_user_profile(self):
        """Test creating a user profile."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest',
            phone_number='+************',
            date_of_birth='1990-01-01'
        )
        
        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.role, 'guest')
        self.assertEqual(profile.phone_number, '+************')
        self.assertTrue(profile.is_guest())
        self.assertFalse(profile.is_host())
        self.assertFalse(profile.is_admin_user())
    
    def test_user_profile_str_method(self):
        """Test string representation of UserProfile."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='host'
        )
        
        expected_str = f"{self.user.username} - Host"
        self.assertEqual(str(profile), expected_str)
    
    def test_role_validation(self):
        """Test role validation."""
        # Valid roles
        valid_roles = ['guest', 'host', 'admin']
        for role in valid_roles:
            profile = UserProfile(user=self.user, role=role)
            profile.full_clean()  # Should not raise ValidationError
    
    def test_phone_number_validation(self):
        """Test phone number validation."""
        # Valid phone numbers
        valid_phones = ['+************', '+98-************', '09123456789']
        for phone in valid_phones:
            profile = UserProfile(
                user=self.user,
                role='guest',
                phone_number=phone
            )
            try:
                profile.full_clean()
            except ValidationError:
                self.fail(f"Valid phone number {phone} raised ValidationError")
    
    def test_unique_user_constraint(self):
        """Test that each user can have only one profile."""
        UserProfile.objects.create(user=self.user, role='guest')
        
        with self.assertRaises(IntegrityError):
            UserProfile.objects.create(user=self.user, role='host')
    
    def test_role_methods(self):
        """Test role checking methods."""
        # Test guest role
        guest_profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        self.assertTrue(guest_profile.is_guest())
        self.assertFalse(guest_profile.is_host())
        self.assertFalse(guest_profile.is_admin_user())
        
        # Test host role
        guest_profile.role = 'host'
        guest_profile.save()
        self.assertFalse(guest_profile.is_guest())
        self.assertTrue(guest_profile.is_host())
        self.assertFalse(guest_profile.is_admin_user())
        
        # Test admin role
        guest_profile.role = 'admin'
        guest_profile.save()
        self.assertFalse(guest_profile.is_guest())
        self.assertFalse(guest_profile.is_host())
        self.assertTrue(guest_profile.is_admin_user())
    
    def test_profile_creation_signal(self):
        """Test that profile is created automatically for new users."""
        # This would test the signal if implemented
        new_user = User.objects.create_user(
            username='newuser',
            email='<EMAIL>',
            password='newpass123'
        )
        
        # Check if profile exists (if auto-creation signal is implemented)
        # For now, we manually create profiles
        self.assertFalse(hasattr(new_user, 'profile'))
    
    def test_profile_update(self):
        """Test updating profile information."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest',
            phone_number='+************'
        )
        
        # Update profile
        profile.role = 'host'
        profile.phone_number = '+989987654321'
        profile.save()
        
        # Refresh from database
        profile.refresh_from_db()
        
        self.assertEqual(profile.role, 'host')
        self.assertEqual(profile.phone_number, '+989987654321')
    
    def test_profile_deletion(self):
        """Test profile deletion."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        
        profile_id = profile.id
        profile.delete()
        
        with self.assertRaises(UserProfile.DoesNotExist):
            UserProfile.objects.get(id=profile_id)
    
    def test_user_deletion_cascades(self):
        """Test that deleting user also deletes profile."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        
        profile_id = profile.id
        self.user.delete()
        
        with self.assertRaises(UserProfile.DoesNotExist):
            UserProfile.objects.get(id=profile_id)
    
    def test_profile_timestamps(self):
        """Test that timestamps are set correctly."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        
        self.assertIsNotNone(profile.created_at)
        self.assertIsNotNone(profile.updated_at)
        
        # Update profile and check updated_at changes
        original_updated_at = profile.updated_at
        profile.role = 'host'
        profile.save()
        
        self.assertGreater(profile.updated_at, original_updated_at)
    
    def test_profile_optional_fields(self):
        """Test that optional fields can be None."""
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        
        self.assertIsNone(profile.phone_number)
        self.assertIsNone(profile.date_of_birth)
        self.assertEqual(profile.bio, '')
    
    def test_profile_bio_max_length(self):
        """Test bio field max length."""
        long_bio = 'x' * 1001  # Assuming max_length is 1000
        
        profile = UserProfile(
            user=self.user,
            role='guest',
            bio=long_bio
        )
        
        with self.assertRaises(ValidationError):
            profile.full_clean()
    
    def test_get_full_name_method(self):
        """Test getting user's full name through profile."""
        self.user.first_name = 'John'
        self.user.last_name = 'Doe'
        self.user.save()
        
        profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        
        # Assuming we add a get_full_name method to profile
        expected_name = 'John Doe'
        self.assertEqual(self.user.get_full_name(), expected_name)
    
    def test_profile_permissions(self):
        """Test role-based permissions."""
        guest_profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
        
        # Test guest permissions
        self.assertTrue(guest_profile.is_guest())
        # Add more specific permission tests based on your requirements
        
        # Test host permissions
        guest_profile.role = 'host'
        guest_profile.save()
        self.assertTrue(guest_profile.is_host())
        
        # Test admin permissions
        guest_profile.role = 'admin'
        guest_profile.save()
        self.assertTrue(guest_profile.is_admin_user())
