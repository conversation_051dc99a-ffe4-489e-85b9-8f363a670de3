"""
Authentication and user management forms for NomadPersia platform.

This module contains forms for user registration, login, profile management,
and role-based functionality.
"""

import re
from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from .models import UserProfile


class CustomUserCreationForm(UserCreationForm):
    """
    Extended user registration form with role selection and additional fields.
    """
    
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address'
        })
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your first name'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your last name'
        })
    )
    
    role = forms.ChoiceField(
        choices=[('guest', 'Guest - Book accommodations'), ('host', 'Host - List accommodations')],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        initial='guest',
        help_text='Choose your role on the platform'
    )
    
    phone_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your phone number (optional)'
        })
    )
    
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Choose a username'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes to password fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter a strong password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirm your password'
        })
    
    def clean_email(self):
        """Validate that email is unique."""
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError(_('A user with this email already exists.'))
        return email
    
    def save(self, commit=True):
        """Save user and create associated profile."""
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        
        if commit:
            user.save()
            # Create user profile (only if it doesn't exist)
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'role': self.cleaned_data['role'],
                    'phone_number': self.cleaned_data.get('phone_number', '')
                }
            )
        return user


class CustomAuthenticationForm(AuthenticationForm):
    """
    Custom login form with enhanced styling.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter your username or email'
        })
        self.fields['password'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter your password'
        })


class UserProfileForm(forms.ModelForm):
    """
    Form for updating user profile information.
    """
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'First name'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Last name'
        })
    )
    
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Email address'
        })
    )
    
    class Meta:
        model = UserProfile
        fields = ['phone_number', 'date_of_birth', 'profile_image_placeholder']
        widgets = {
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Phone number'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'profile_image_placeholder': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Profile image description'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.user:
            self.fields['first_name'].initial = self.instance.user.first_name
            self.fields['last_name'].initial = self.instance.user.last_name
            self.fields['email'].initial = self.instance.user.email
    
    def save(self, commit=True):
        """Save both User and UserProfile data."""
        profile = super().save(commit=False)
        
        if commit:
            # Update User fields
            user = profile.user
            user.first_name = self.cleaned_data['first_name']
            user.last_name = self.cleaned_data['last_name']
            user.email = self.cleaned_data['email']
            user.save()
            
            # Save profile
            profile.save()
        
        return profile


class UserProfileUpdateForm(forms.ModelForm):
    """
    Comprehensive form for updating user profile information.
    """

    # User fields
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your first name'
        })
    )

    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your last name'
        })
    )

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your email address'
        })
    )

    # Profile image
    profile_image = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        })
    )

    # Personal information
    national_id = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your national ID code'
        })
    )

    phone_number = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your mobile number'
        })
    )

    emergency_contact = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter emergency contact number'
        })
    )

    # Date of birth fields
    birth_day = forms.ChoiceField(
        choices=[(i, i) for i in range(1, 32)],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    birth_month = forms.ChoiceField(
        choices=[
            (1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'),
            (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
            (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    birth_year = forms.ChoiceField(
        choices=[(i, i) for i in range(1950, 2010)],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # IBAN field
    iban_number = forms.CharField(
        max_length=26,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'IR00 0000 0000 0000 0000 0000 00',
            'pattern': 'IR[0-9]{24}'
        })
    )

    # Identity verification
    identity_document = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*,.pdf'
        })
    )

    # Ownership documentation
    ownership_type = forms.ChoiceField(
        choices=UserProfile.OWNERSHIP_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'ownership-type-select'
        })
    )

    user_national_id = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*,.pdf'
        })
    )

    owner_national_id = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*,.pdf'
        })
    )

    property_deed = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*,.pdf'
        })
    )

    lease_agreement = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*,.pdf'
        })
    )

    commitment_letter = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*,.pdf'
        })
    )

    class Meta:
        model = UserProfile
        fields = [
            'profile_image', 'national_id', 'phone_number', 'emergency_contact',
            'gender', 'iban_number', 'identity_document', 'ownership_type',
            'user_national_id', 'owner_national_id', 'property_deed',
            'lease_agreement', 'commitment_letter'
        ]
        widgets = {
            'gender': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if user:
            # Populate user fields
            self.fields['first_name'].initial = user.first_name
            self.fields['last_name'].initial = user.last_name
            self.fields['email'].initial = user.email

            # Populate date of birth fields
            if self.instance.date_of_birth:
                self.fields['birth_day'].initial = self.instance.date_of_birth.day
                self.fields['birth_month'].initial = self.instance.date_of_birth.month
                self.fields['birth_year'].initial = self.instance.date_of_birth.year

    def clean_iban_number(self):
        """Validate IBAN number format."""
        iban = self.cleaned_data.get('iban_number')
        if iban:
            # Remove spaces and convert to uppercase
            iban = iban.replace(' ', '').upper()

            # Check Iranian IBAN format
            if not re.match(r'^IR\d{24}$', iban):
                raise ValidationError('Please enter a valid Iranian IBAN number (IR followed by 24 digits)')

            return iban
        return iban

    def clean_phone_number(self):
        """Validate phone number format."""
        phone = self.cleaned_data.get('phone_number')
        if phone:
            # Remove spaces and special characters except +
            phone = re.sub(r'[^\d+]', '', phone)

            # Check international phone number format
            # Support international format (+country code + number) or local format (starting with 0)
            international_format = re.match(r'^\+[1-9]\d{1,3}\d{4,15}$', phone)
            local_format = re.match(r'^0\d{7,15}$', phone)

            if not (international_format or local_format):
                raise ValidationError('Please enter a valid phone number (international format: +[country code][number] or local format starting with 0)')

            return phone
        return phone

    def clean_national_id(self):
        """Validate national ID format."""
        national_id = self.cleaned_data.get('national_id')
        if national_id:
            # Remove spaces and special characters
            national_id = re.sub(r'[^\d]', '', national_id)

            # Check length
            if len(national_id) != 10:
                raise ValidationError('National ID must be exactly 10 digits')

            return national_id
        return national_id

    def save(self, commit=True):
        """Save the profile with user information."""
        profile = super().save(commit=False)

        # Handle date of birth
        day = self.cleaned_data.get('birth_day')
        month = self.cleaned_data.get('birth_month')
        year = self.cleaned_data.get('birth_year')

        if day and month and year:
            from datetime import date
            try:
                profile.date_of_birth = date(int(year), int(month), int(day))
            except ValueError:
                pass  # Invalid date, skip

        if commit:
            # Update User fields
            user = profile.user
            user.first_name = self.cleaned_data['first_name']
            user.last_name = self.cleaned_data['last_name']
            user.email = self.cleaned_data['email']
            user.save()

            # Save profile
            profile.save()

        return profile
