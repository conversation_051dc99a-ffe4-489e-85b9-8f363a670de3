"""
Security middleware for NomadPersia platform.

This module contains middleware for enhanced security, user tracking,
and access control.
"""

import logging
from django.contrib.auth import logout
from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.http import HttpResponseForbidden
from django.conf import settings

logger = logging.getLogger(__name__)


class SecurityMiddleware(MiddlewareMixin):
    """
    Enhanced security middleware for the platform.
    """
    
    def process_request(self, request):
        """Process incoming requests for security checks."""
        
        # Check for maintenance mode
        if getattr(settings, 'MAINTENANCE_MODE', False):
            if not request.user.is_superuser:
                return HttpResponseForbidden(
                    "Site is under maintenance. Please try again later."
                )
        
        # Rate limiting for authentication endpoints
        if request.path in ['/accounts/login/', '/accounts/register/']:
            ip = self.get_client_ip(request)
            cache_key = f"auth_attempts_{ip}"
            attempts = cache.get(cache_key, 0)
            
            if attempts >= 5:  # Max 5 attempts per hour
                return HttpResponseForbidden(
                    "Too many authentication attempts. Please try again later."
                )
        
        # Log suspicious activity
        if (request.method == 'POST' and hasattr(request, 'user') and
            request.user.is_authenticated):
            self.log_user_activity(request)
    
    def process_response(self, request, response):
        """Process responses for security headers."""
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Track failed authentication attempts
        if (request.path in ['/accounts/login/', '/accounts/register/'] and 
            response.status_code in [400, 401, 403]):
            ip = self.get_client_ip(request)
            cache_key = f"auth_attempts_{ip}"
            attempts = cache.get(cache_key, 0) + 1
            cache.set(cache_key, attempts, 3600)  # 1 hour timeout
        
        return response
    
    def get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def log_user_activity(self, request):
        """Log user activity for security monitoring."""
        try:
            logger.info(
                f"User activity: {request.user.username} - "
                f"{request.method} {request.path} - "
                f"IP: {self.get_client_ip(request)}"
            )
        except Exception as e:
            logger.error(f"Failed to log user activity: {e}")


class ProfileRequiredMiddleware(MiddlewareMixin):
    """
    Middleware to ensure authenticated users have a profile.
    """
    
    def process_request(self, request):
        """Check if authenticated user has a profile."""

        # Skip for certain paths
        skip_paths = [
            '/accounts/logout/',
            '/accounts/profile/create/',
            '/admin/',
            '/static/',
            '/media/',
            '/favicon.ico',
        ]

        if any(request.path.startswith(path) for path in skip_paths):
            return None

        # Check if user attribute exists (after AuthenticationMiddleware)
        if not hasattr(request, 'user'):
            return None

        # Check if user is authenticated but has no profile
        if (request.user.is_authenticated and
            not hasattr(request.user, 'profile') and
            not request.user.is_superuser):

            messages.warning(
                request,
                'Please complete your profile to continue using the platform.'
            )
            return redirect('accounts:profile')

        return None


class SessionSecurityMiddleware(MiddlewareMixin):
    """
    Enhanced session security middleware.
    """
    
    def process_request(self, request):
        """Process request for session security."""

        # Check if user attribute exists (after AuthenticationMiddleware)
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None
        
        # Check for session hijacking (relaxed for development)
        current_ip = self.get_client_ip(request)
        session_ip = request.session.get('ip_address')

        # Only enforce IP checking in production or when explicitly enabled
        from django.conf import settings
        enforce_ip_check = getattr(settings, 'ENFORCE_SESSION_IP_CHECK', not settings.DEBUG)

        if enforce_ip_check and session_ip and session_ip != current_ip:
            logger.warning(
                f"Potential session hijacking detected: "
                f"User {request.user.username} - "
                f"Session IP: {session_ip}, Current IP: {current_ip}"
            )

            # Force logout for security
            logout(request)
            messages.error(
                request,
                'Your session has been terminated for security reasons. '
                'Please log in again.'
            )
            return redirect('accounts:login')
        elif not enforce_ip_check and session_ip and session_ip != current_ip:
            # In development, just log the IP change but don't terminate session
            logger.info(
                f"IP address changed for user {request.user.username}: "
                f"{session_ip} -> {current_ip} (development mode - allowing)"
            )
        
        # Store IP in session for future checks
        if not session_ip:
            request.session['ip_address'] = current_ip
        
        # Update last activity
        request.session['last_activity'] = timezone.now().isoformat()
        
        return None
    
    def get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ContentSecurityMiddleware(MiddlewareMixin):
    """
    Content Security Policy middleware.
    """
    
    def process_response(self, request, response):
        """Add CSP headers to response."""
        
        # Basic CSP policy
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://cdn.jsdelivr.net; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        )
        
        response['Content-Security-Policy'] = csp_policy
        
        return response


class AuditLogMiddleware(MiddlewareMixin):
    """
    Middleware for audit logging of important actions.
    """
    
    LOGGED_ACTIONS = [
        'POST',  # All POST requests
        'PUT',   # All PUT requests
        'DELETE', # All DELETE requests
    ]
    
    SENSITIVE_PATHS = [
        '/admin/',
        '/dashboard/admin/',
        '/accounts/',
    ]
    
    def process_request(self, request):
        """Log important user actions."""

        if (request.method in self.LOGGED_ACTIONS or
            any(request.path.startswith(path) for path in self.SENSITIVE_PATHS)):

            self.log_action(request)
    
    def log_action(self, request):
        """Log user action to audit trail."""
        try:
            user = (request.user if hasattr(request, 'user') and
                   request.user.is_authenticated else 'Anonymous')
            
            logger.info(
                f"AUDIT: User: {user} - "
                f"Action: {request.method} {request.path} - "
                f"IP: {self.get_client_ip(request)} - "
                f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}"
            )
            
            # Log to database for critical actions (disabled for now)
            # if any(request.path.startswith(path) for path in self.SENSITIVE_PATHS):
            #     try:
            #         from dashboard.models import ActivityLog
            #         ActivityLog.objects.create(
            #             activity_type='admin_action',
            #             description=f"{request.method} {request.path}",
            #             user=(request.user if hasattr(request, 'user') and
            #                  request.user.is_authenticated else None),
            #             ip_address=self.get_client_ip(request),
            #             metadata={
            #                 'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown')[:255],
            #                 'method': request.method,
            #                 'path': request.path
            #             }
            #         )
            #     except Exception as e:
            #         logger.error(f"Failed to log to database: {e}")
        
        except Exception as e:
            logger.error(f"Failed to log audit action: {e}")
    
    def get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
