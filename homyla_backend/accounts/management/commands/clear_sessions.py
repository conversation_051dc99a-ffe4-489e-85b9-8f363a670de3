"""
Management command to clear all user sessions.
Useful for resolving session-related issues.
"""

from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from django.utils import timezone


class Command(BaseCommand):
    help = 'Clear all user sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--expired-only',
            action='store_true',
            help='Only clear expired sessions',
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Skip confirmation prompt',
        )

    def handle(self, *args, **options):
        """Handle the command execution."""
        
        if options['expired_only']:
            # Clear only expired sessions
            expired_sessions = Session.objects.filter(expire_date__lt=timezone.now())
            count = expired_sessions.count()
            
            if not options['confirm']:
                confirm = input(f'This will clear {count} expired sessions. Continue? (y/N): ')
                if confirm.lower() not in ['y', 'yes']:
                    self.stdout.write(self.style.WARNING('Operation cancelled.'))
                    return
            
            expired_sessions.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully cleared {count} expired sessions.')
            )
        else:
            # Clear all sessions
            all_sessions = Session.objects.all()
            count = all_sessions.count()
            
            if not options['confirm']:
                confirm = input(f'This will clear ALL {count} sessions (all users will be logged out). Continue? (y/N): ')
                if confirm.lower() not in ['y', 'yes']:
                    self.stdout.write(self.style.WARNING('Operation cancelled.'))
                    return
            
            all_sessions.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully cleared all {count} sessions.')
            )
            self.stdout.write(
                self.style.WARNING('All users have been logged out.')
            )
