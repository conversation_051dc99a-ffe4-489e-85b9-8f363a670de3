"""
Test cases for accounts app views.

This module contains comprehensive tests for authentication views,
user registration, login, and profile management.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.auth import get_user

from .models import UserProfile


class AuthenticationViewsTest(TestCase):
    """Test cases for authentication views."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='guest'
        )
    
    def test_register_view_get(self):
        """Test GET request to register view."""
        response = self.client.get(reverse('accounts:register'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Register')
        self.assertContains(response, 'form')
    
    def test_register_view_post_valid(self):
        """Test POST request to register view with valid data."""
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'newpass123!',
            'password2': 'newpass123!',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'guest'
        }
        
        response = self.client.post(reverse('accounts:register'), data)
        
        # Should redirect after successful registration
        self.assertEqual(response.status_code, 302)
        
        # Check user was created
        self.assertTrue(User.objects.filter(username='newuser').exists())
        
        # Check profile was created
        new_user = User.objects.get(username='newuser')
        self.assertTrue(hasattr(new_user, 'profile'))
        self.assertEqual(new_user.profile.role, 'guest')
    
    def test_register_view_post_invalid(self):
        """Test POST request to register view with invalid data."""
        data = {
            'username': 'testuser',  # Already exists
            'email': 'invalid-email',  # Invalid email
            'password1': 'weak',  # Weak password
            'password2': 'different',  # Different password
            'role': 'guest'
        }
        
        response = self.client.post(reverse('accounts:register'), data)
        
        # Should stay on the same page with errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'error')
        
        # User should not be created
        self.assertEqual(User.objects.filter(username='testuser').count(), 1)
    
    def test_login_view_get(self):
        """Test GET request to login view."""
        response = self.client.get(reverse('accounts:login'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Login')
        self.assertContains(response, 'form')
    
    def test_login_view_post_valid(self):
        """Test POST request to login view with valid credentials."""
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        response = self.client.post(reverse('accounts:login'), data)
        
        # Should redirect after successful login
        self.assertEqual(response.status_code, 302)
        
        # Check user is logged in
        user = get_user(self.client)
        self.assertTrue(user.is_authenticated)
        self.assertEqual(user.username, 'testuser')
    
    def test_login_view_post_invalid(self):
        """Test POST request to login view with invalid credentials."""
        data = {
            'username': 'testuser',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(reverse('accounts:login'), data)
        
        # Should stay on the same page with errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'error')
        
        # User should not be logged in
        user = get_user(self.client)
        self.assertFalse(user.is_authenticated)
    
    def test_logout_view(self):
        """Test logout view."""
        # Login first
        self.client.login(username='testuser', password='testpass123')
        user = get_user(self.client)
        self.assertTrue(user.is_authenticated)
        
        # Logout
        response = self.client.get(reverse('accounts:logout'))
        
        # Should redirect
        self.assertEqual(response.status_code, 302)
        
        # User should be logged out
        user = get_user(self.client)
        self.assertFalse(user.is_authenticated)
    
    def test_profile_view_authenticated(self):
        """Test profile view for authenticated user."""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get(reverse('accounts:profile'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'testuser')
        self.assertContains(response, 'Guest')
    
    def test_profile_view_unauthenticated(self):
        """Test profile view for unauthenticated user."""
        response = self.client.get(reverse('accounts:profile'))
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, f"{reverse('accounts:login')}?next={reverse('accounts:profile')}")
    
    def test_profile_edit_view_get(self):
        """Test GET request to profile edit view."""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get(reverse('accounts:profile_edit'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edit Profile')
        self.assertContains(response, 'form')
    
    def test_profile_edit_view_post_valid(self):
        """Test POST request to profile edit view with valid data."""
        self.client.login(username='testuser', password='testpass123')
        
        data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'email': '<EMAIL>',
            'phone_number': '+************',
            'bio': 'Updated bio'
        }
        
        response = self.client.post(reverse('accounts:profile_edit'), data)
        
        # Should redirect after successful update
        self.assertEqual(response.status_code, 302)
        
        # Check user was updated
        self.user.refresh_from_db()
        self.profile.refresh_from_db()
        
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.profile.phone_number, '+************')
        self.assertEqual(self.profile.bio, 'Updated bio')
    
    def test_role_required_decorator(self):
        """Test role_required decorator functionality."""
        # Create host user
        host_user = User.objects.create_user(
            username='hostuser',
            email='<EMAIL>',
            password='hostpass123'
        )
        host_profile = UserProfile.objects.create(
            user=host_user,
            role='host'
        )
        
        # Test guest trying to access host-only view
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('accommodations:host_accommodations'))
        
        # Should be forbidden or redirected
        self.assertIn(response.status_code, [302, 403])
        
        # Test host accessing host-only view
        self.client.login(username='hostuser', password='hostpass123')
        response = self.client.get(reverse('accommodations:host_accommodations'))
        
        # Should be allowed
        self.assertEqual(response.status_code, 200)
    
    def test_password_change_view(self):
        """Test password change functionality."""
        self.client.login(username='testuser', password='testpass123')
        
        # Test GET request
        response = self.client.get(reverse('accounts:password_change'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Change Password')
        
        # Test POST request with valid data
        data = {
            'old_password': 'testpass123',
            'new_password1': 'newtestpass123!',
            'new_password2': 'newtestpass123!'
        }
        
        response = self.client.post(reverse('accounts:password_change'), data)
        
        # Should redirect after successful change
        self.assertEqual(response.status_code, 302)
        
        # Test login with new password
        self.client.logout()
        login_success = self.client.login(username='testuser', password='newtestpass123!')
        self.assertTrue(login_success)
    
    def test_user_registration_creates_profile(self):
        """Test that user registration automatically creates a profile."""
        data = {
            'username': 'profileuser',
            'email': '<EMAIL>',
            'password1': 'profilepass123!',
            'password2': 'profilepass123!',
            'first_name': 'Profile',
            'last_name': 'User',
            'role': 'host'
        }
        
        response = self.client.post(reverse('accounts:register'), data)
        
        # Check user and profile were created
        user = User.objects.get(username='profileuser')
        self.assertTrue(hasattr(user, 'profile'))
        self.assertEqual(user.profile.role, 'host')
    
    def test_redirect_after_login(self):
        """Test redirect to next parameter after login."""
        # Try to access protected page
        protected_url = reverse('accounts:profile')
        response = self.client.get(protected_url)
        
        # Should redirect to login with next parameter
        expected_redirect = f"{reverse('accounts:login')}?next={protected_url}"
        self.assertRedirects(response, expected_redirect)
        
        # Login with next parameter
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        
        response = self.client.post(expected_redirect, data)
        
        # Should redirect to original protected page
        self.assertRedirects(response, protected_url)
    
    def test_user_profile_display(self):
        """Test user profile display information."""
        self.client.login(username='testuser', password='testpass123')
        
        # Update profile with more information
        self.user.first_name = 'Test'
        self.user.last_name = 'User'
        self.user.save()
        
        self.profile.phone_number = '+************'
        self.profile.bio = 'Test user bio'
        self.profile.save()
        
        response = self.client.get(reverse('accounts:profile'))
        
        self.assertContains(response, 'Test User')
        self.assertContains(response, '+************')
        self.assertContains(response, 'Test user bio')
        self.assertContains(response, 'Guest')
    
    def test_invalid_role_registration(self):
        """Test registration with invalid role."""
        data = {
            'username': 'invalidrole',
            'email': '<EMAIL>',
            'password1': 'invalidpass123!',
            'password2': 'invalidpass123!',
            'role': 'invalid_role'
        }
        
        response = self.client.post(reverse('accounts:register'), data)
        
        # Should show form errors
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'error')
        
        # User should not be created
        self.assertFalse(User.objects.filter(username='invalidrole').exists())
