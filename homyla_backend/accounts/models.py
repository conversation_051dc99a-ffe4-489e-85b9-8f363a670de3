"""
User account models for NomadPersia platform.

This module defines the user profile model that extends Django's built-in User model
to support three distinct user roles: Guest, Host, and Admin.
"""

from django.contrib.auth.models import User
from django.db import models
from django.utils.translation import gettext_lazy as _


class UserProfile(models.Model):
    """
    Extended User model for NomadPersia platform.

    Supports three user roles:
    - Guest: Can search and book accommodations
    - Host: Can list and manage accommodations, view bookings
    - Admin: Full platform management access
    """

    USER_ROLES = [
        ('guest', _('Guest')),
        ('host', _('Host')),
        ('admin', _('Admin')),
    ]

    # Link to Django's built-in User model
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile'
    )

    role = models.Char<PERSON>ield(
        max_length=10,
        choices=USER_ROLES,
        default='guest',
        help_text=_('User role determines access permissions')
    )

    # Contact Information
    phone_number = models.Char<PERSON><PERSON>(
        max_length=20,
        blank=True,
        null=True,
        help_text=_('Mobile phone number')
    )

    emergency_contact = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text=_('Emergency contact number')
    )

    # Personal Information
    national_id = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text=_('National ID code')
    )

    date_of_birth = models.DateField(
        blank=True,
        null=True,
        help_text=_('Date of birth for age verification')
    )

    GENDER_CHOICES = [
        ('', _('Prefer not to say')),
        ('male', _('Male')),
        ('female', _('Female')),
        ('other', _('Other')),
    ]

    gender = models.CharField(
        max_length=10,
        choices=GENDER_CHOICES,
        blank=True,
        help_text=_('Gender (optional)')
    )

    # Profile Image
    profile_image = models.ImageField(
        upload_to='profile_images/',
        blank=True,
        null=True,
        help_text=_('Profile picture')
    )

    profile_image_placeholder = models.CharField(
        max_length=200,
        blank=True,
        default='[Profile Image Placeholder]',
        help_text=_('Text placeholder for profile image (MVP only)')
    )

    # Bank Account Information
    iban_number = models.CharField(
        max_length=26,
        blank=True,
        null=True,
        help_text=_('IBAN (Sheba) number for payments')
    )

    bank_account_verified = models.BooleanField(
        default=False,
        help_text=_('Whether bank account has been verified')
    )

    # Identity Verification
    identity_document = models.FileField(
        upload_to='identity_documents/',
        blank=True,
        null=True,
        help_text=_('National ID card or passport image')
    )

    identity_verified = models.BooleanField(
        default=False,
        help_text=_('Whether identity has been verified')
    )

    # Ownership Documentation
    OWNERSHIP_TYPE_CHOICES = [
        ('', _('Select ownership type')),
        ('owner_with_deed', _('I am the owner and have the deed')),
        ('owner_without_deed', _('I am the owner and do not have the deed')),
        ('tenant_with_lease', _('The owner is someone else and I have a lease agreement')),
        ('tenant_with_deed', _('The owner is someone else and I have the deed')),
        ('tenant_without_deed', _('The owner is someone else and I don\'t have the deed')),
    ]

    ownership_type = models.CharField(
        max_length=30,
        choices=OWNERSHIP_TYPE_CHOICES,
        blank=True,
        help_text=_('Type of property ownership or tenancy')
    )

    # User's National ID (always required for hosts)
    user_national_id = models.FileField(
        upload_to='ownership_documents/user_ids/',
        blank=True,
        null=True,
        help_text=_('Your national ID card image')
    )

    # Owner's National ID (required when user is not the owner)
    owner_national_id = models.FileField(
        upload_to='ownership_documents/owner_ids/',
        blank=True,
        null=True,
        help_text=_('Property owner\'s national ID card image')
    )

    # Property Deed
    property_deed = models.FileField(
        upload_to='ownership_documents/deeds/',
        blank=True,
        null=True,
        help_text=_('Property deed document')
    )

    # Lease Agreement
    lease_agreement = models.FileField(
        upload_to='ownership_documents/leases/',
        blank=True,
        null=True,
        help_text=_('Rental/lease agreement document')
    )

    # Commitment Letter
    commitment_letter = models.FileField(
        upload_to='ownership_documents/commitments/',
        blank=True,
        null=True,
        help_text=_('Signed commitment letter')
    )

    # Ownership verification status
    ownership_verified = models.BooleanField(
        default=False,
        help_text=_('Whether ownership documentation has been verified')
    )

    ownership_submitted_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text=_('When ownership documents were submitted')
    )

    is_verified = models.BooleanField(
        default=False,
        help_text=_('Whether the user account has been verified')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('User Profile')
        verbose_name_plural = _('User Profiles')
        db_table = 'accounts_userprofile'
        indexes = [
            models.Index(fields=['role']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} ({self.get_role_display()})"

    def is_guest(self):
        """Check if user is a guest."""
        return self.role == 'guest'

    def is_host(self):
        """Check if user is a host."""
        return self.role == 'host'

    def is_admin_user(self):
        """Check if user is an admin (not to be confused with Django's is_staff)."""
        return self.role == 'admin'

    def get_full_name(self):
        """Return the full name of the user."""
        full_name = f"{self.user.first_name} {self.user.last_name}".strip()
        return full_name if full_name else self.user.username

    def get_profile_image_url(self):
        """Get profile image URL or placeholder."""
        if self.profile_image:
            return self.profile_image.url
        return None

    def is_profile_complete(self):
        """Check if user profile is complete."""
        required_fields = [
            self.user.first_name,
            self.user.last_name,
            self.phone_number,
            self.national_id,
            self.date_of_birth
        ]
        return all(field for field in required_fields)

    def is_bank_info_complete(self):
        """Check if bank information is complete."""
        return bool(self.iban_number)

    def is_identity_verified_complete(self):
        """Check if identity verification is complete."""
        return bool(self.identity_document) and self.identity_verified

    def get_verification_status(self):
        """Get overall verification status."""
        try:
            ownership_verified = getattr(self, 'ownership_verified', False)
            ownership_type = getattr(self, 'ownership_type', None)

            if self.is_identity_verified_complete() and self.bank_account_verified and ownership_verified:
                return 'verified'
            elif self.identity_document or self.iban_number or ownership_type:
                return 'pending'
            else:
                return 'incomplete'
        except AttributeError:
            if self.is_identity_verified_complete() and self.bank_account_verified:
                return 'verified'
            elif self.identity_document or self.iban_number:
                return 'pending'
            else:
                return 'incomplete'

    def is_ownership_complete(self):
        """Check if ownership documentation is complete."""
        try:
            if not hasattr(self, 'ownership_type') or not self.ownership_type:
                return False

            # Check required documents based on ownership type
            if self.ownership_type == 'owner_with_deed':
                return bool(getattr(self, 'user_national_id', None) and getattr(self, 'property_deed', None))
            elif self.ownership_type == 'owner_without_deed':
                return bool(getattr(self, 'user_national_id', None))
            elif self.ownership_type in ['tenant_with_lease', 'tenant_with_deed', 'tenant_without_deed']:
                required_docs = [
                    getattr(self, 'user_national_id', None),
                    getattr(self, 'owner_national_id', None),
                    getattr(self, 'commitment_letter', None)
                ]
                if self.ownership_type == 'tenant_with_lease':
                    required_docs.append(getattr(self, 'lease_agreement', None))
                elif self.ownership_type == 'tenant_with_deed':
                    required_docs.append(getattr(self, 'property_deed', None))
                return all(doc for doc in required_docs)

            return False
        except AttributeError:
            return False

    def get_required_documents(self):
        """Get list of required documents based on ownership type."""
        try:
            if not hasattr(self, 'ownership_type') or not getattr(self, 'ownership_type', None):
                return []

            docs = []
            ownership_type = getattr(self, 'ownership_type', '')

            if ownership_type == 'owner_with_deed':
                docs = [
                    ('user_national_id', 'Your National ID'),
                    ('property_deed', 'Property Deed'),
                ]
            elif ownership_type == 'owner_without_deed':
                docs = [
                    ('user_national_id', 'Your National ID'),
                ]
            elif ownership_type in ['tenant_with_lease', 'tenant_with_deed', 'tenant_without_deed']:
                docs = [
                    ('user_national_id', 'Your National ID'),
                    ('owner_national_id', 'Owner\'s National ID'),
                    ('commitment_letter', 'Commitment Letter'),
                ]

                if ownership_type == 'tenant_with_lease':
                    docs.append(('lease_agreement', 'Lease Agreement'))
                elif ownership_type == 'tenant_with_deed':
                    docs.append(('property_deed', 'Property Deed'))

            return docs
        except AttributeError:
            return []

    def get_ownership_status(self):
        """Get ownership documentation status."""
        try:
            if not hasattr(self, 'ownership_type') or not getattr(self, 'ownership_type', None):
                return 'not_started'
            elif getattr(self, 'ownership_verified', False):
                return 'verified'
            elif self.is_ownership_complete():
                return 'pending'
            else:
                return 'incomplete'
        except AttributeError:
            return 'not_started'

    def save(self, *args, **kwargs):
        """Override save to set staff status for admin users."""
        if self.role == 'admin':
            self.user.is_staff = True
            self.user.save()
        super().save(*args, **kwargs)
