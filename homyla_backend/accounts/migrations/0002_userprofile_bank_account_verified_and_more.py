# Generated by Django 5.0.14 on 2025-07-18 05:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="bank_account_verified",
            field=models.BooleanField(
                default=False, help_text="Whether bank account has been verified"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="emergency_contact",
            field=models.CharField(
                blank=True,
                help_text="Emergency contact number",
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="gender",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "Prefer not to say"),
                    ("male", "Male"),
                    ("female", "Female"),
                    ("other", "Other"),
                ],
                help_text="Gender (optional)",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="iban_number",
            field=models.Char<PERSON><PERSON>(
                blank=True,
                help_text="IBAN (Sheba) number for payments",
                max_length=26,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="identity_document",
            field=models.FileField(
                blank=True,
                help_text="National ID card or passport image",
                null=True,
                upload_to="identity_documents/",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="identity_verified",
            field=models.BooleanField(
                default=False, help_text="Whether identity has been verified"
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="national_id",
            field=models.CharField(
                blank=True, help_text="National ID code", max_length=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="profile_image",
            field=models.ImageField(
                blank=True,
                help_text="Profile picture",
                null=True,
                upload_to="profile_images/",
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="phone_number",
            field=models.CharField(
                blank=True, help_text="Mobile phone number", max_length=20, null=True
            ),
        ),
    ]
