# Generated by Django 5.0.14 on 2025-07-18 05:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0002_userprofile_bank_account_verified_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="commitment_letter",
            field=models.FileField(
                blank=True,
                help_text="Signed commitment letter",
                null=True,
                upload_to="ownership_documents/commitments/",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="lease_agreement",
            field=models.FileField(
                blank=True,
                help_text="Rental/lease agreement document",
                null=True,
                upload_to="ownership_documents/leases/",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="owner_national_id",
            field=models.FileField(
                blank=True,
                help_text="Property owner's national ID card image",
                null=True,
                upload_to="ownership_documents/owner_ids/",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="ownership_submitted_at",
            field=models.DateTimeField(
                blank=True,
                help_text="When ownership documents were submitted",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="ownership_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "Select ownership type"),
                    ("owner_with_deed", "I am the owner and have the deed"),
                    ("owner_without_deed", "I am the owner and do not have the deed"),
                    (
                        "tenant_with_lease",
                        "The owner is someone else and I have a lease agreement",
                    ),
                    (
                        "tenant_with_deed",
                        "The owner is someone else and I have the deed",
                    ),
                    (
                        "tenant_without_deed",
                        "The owner is someone else and I don't have the deed",
                    ),
                ],
                help_text="Type of property ownership or tenancy",
                max_length=30,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="ownership_verified",
            field=models.BooleanField(
                default=False,
                help_text="Whether ownership documentation has been verified",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="property_deed",
            field=models.FileField(
                blank=True,
                help_text="Property deed document",
                null=True,
                upload_to="ownership_documents/deeds/",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="user_national_id",
            field=models.FileField(
                blank=True,
                help_text="Your national ID card image",
                null=True,
                upload_to="ownership_documents/user_ids/",
            ),
        ),
    ]
