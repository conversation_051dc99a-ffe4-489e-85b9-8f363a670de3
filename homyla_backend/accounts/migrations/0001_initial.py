# Generated by Django 5.0.14 on 2025-07-02 09:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.Char<PERSON>ield(
                        choices=[
                            ("guest", "Guest"),
                            ("host", "Host"),
                            ("admin", "Admin"),
                        ],
                        default="guest",
                        help_text="User role determines access permissions",
                        max_length=10,
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        help_text="Contact phone number",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "date_of_birth",
                    models.DateField(
                        blank=True,
                        help_text="Date of birth for age verification",
                        null=True,
                    ),
                ),
                (
                    "profile_image_placeholder",
                    models.CharField(
                        blank=True,
                        default="[Profile Image Placeholder]",
                        help_text="Text placeholder for profile image (MVP only)",
                        max_length=200,
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user account has been verified",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "db_table": "accounts_userprofile",
                "indexes": [
                    models.Index(fields=["role"], name="accounts_us_role_e16858_idx"),
                    models.Index(
                        fields=["is_verified"], name="accounts_us_is_veri_40a50a_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="accounts_us_created_70c995_idx"
                    ),
                ],
            },
        ),
    ]
