#!/usr/bin/env python
"""
Test script to check review eligibility across all accommodations
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation
from bookings.models import Booking
from reviews.models import Review
from datetime import timedelta
from django.utils import timezone

User = get_user_model()

def test_review_eligibility():
    print("=== REVIEW ELIGIBILITY TEST ===")
    
    # Get all accommodations
    accommodations = Accommodation.objects.all()
    print(f"Found {accommodations.count()} accommodations")
    
    for accommodation in accommodations:
        print(f"\n🏠 Accommodation: {accommodation.name} (ID: {accommodation.id})")
        
        # Get all guest users
        guest_users = User.objects.filter(profile__role='guest')
        print(f"   Testing {guest_users.count()} guest users")
        
        for user in guest_users:
            # Check if user has completed booking for this accommodation
            completed_booking = Booking.objects.filter(
                guest=user,
                accommodation=accommodation,
                status='completed'
            ).exists()
            
            # Check if user has existing review
            existing_review = Review.objects.filter(
                guest=user,
                accommodation=accommodation
            ).exists()
            
            can_review = completed_booking and not existing_review
            
            if completed_booking or existing_review or can_review:
                print(f"     👤 {user.username}:")
                print(f"        - Has completed booking: {completed_booking}")
                print(f"        - Has existing review: {existing_review}")
                print(f"        - Can review: {can_review}")

def create_test_bookings():
    print("\n=== CREATING TEST BOOKINGS ===")
    
    # Get guest users
    guest_users = User.objects.filter(profile__role='guest')[:3]  # Take first 3 guests
    accommodations = Accommodation.objects.all()[:5]  # Take first 5 accommodations
    
    for i, user in enumerate(guest_users):
        for j, accommodation in enumerate(accommodations):
            # Skip if booking already exists
            if Booking.objects.filter(guest=user, accommodation=accommodation).exists():
                continue
                
            # Create a completed booking
            booking = Booking.objects.create(
                accommodation=accommodation,
                guest=user,
                check_in_date=timezone.now().date() + timedelta(days=70 + i*10 + j),
                check_out_date=timezone.now().date() + timedelta(days=73 + i*10 + j),
                adults=2,
                teens=0,
                children=0,
                infants=0,
                total_price=accommodation.price_per_night * 3,
                status='completed'
            )
            print(f"✅ Created completed booking for {user.username} at {accommodation.name}")

def fix_existing_bookings():
    print("\n=== FIXING EXISTING BOOKINGS ===")
    
    # Get all pending bookings and mark some as completed
    pending_bookings = Booking.objects.filter(status='pending')
    
    for booking in pending_bookings[:10]:  # Fix first 10 pending bookings
        booking.status = 'completed'
        booking.save(update_fields=['status'])
        print(f"✅ Updated booking {booking.id} to completed for {booking.guest.username} at {booking.accommodation.name}")

if __name__ == '__main__':
    print("Testing review eligibility before fixes...")
    test_review_eligibility()
    
    print("\nCreating test bookings...")
    create_test_bookings()
    
    print("\nFixing existing bookings...")
    fix_existing_bookings()
    
    print("\nTesting review eligibility after fixes...")
    test_review_eligibility()
