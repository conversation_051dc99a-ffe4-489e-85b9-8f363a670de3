#!/usr/bin/env python
"""
Test script to verify booking creation fix
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation
from bookings.models import Booking
from bookings.forms import BookingForm
from datetime import date, timedelta

User = get_user_model()

def test_booking_creation():
    print("=== TESTING BOOKING CREATION FIX ===")
    
    # Get test accommodation
    try:
        accommodation = Accommodation.objects.get(id=13)
        print(f"✅ Found accommodation: {accommodation.name}")
    except Accommodation.DoesNotExist:
        print("❌ Accommodation ID 13 not found")
        accommodations = Accommodation.objects.all()[:5]
        if accommodations:
            accommodation = accommodations[0]
            print(f"📝 Using alternative accommodation: {accommodation.name} (ID: {accommodation.id})")
        else:
            print("❌ No accommodations found")
            return
    
    # Get test user
    guest_user = User.objects.filter(profile__role='guest').first()
    if not guest_user:
        print("❌ No guest users found")
        return
    
    print(f"👤 Using guest user: {guest_user.username}")
    
    # Test 1: Create booking using form (simulating the problematic scenario)
    print(f"\n🧪 Test 1: Creating booking using BookingForm")
    
    form_data = {
        'check_in_date': date.today() + timedelta(days=5),
        'check_out_date': date.today() + timedelta(days=8),
        'number_of_guests': 2,
        'special_requests': 'Test booking creation'
    }
    
    try:
        # Create form with accommodation context (as done in the view)
        form = BookingForm(form_data, accommodation=accommodation)
        
        if form.is_valid():
            print("✅ Form is valid")
            
            # Save with commit=False (as done in the view)
            booking = form.save(commit=False)
            print(f"✅ Form.save(commit=False) successful")
            print(f"   Booking accommodation: {booking.accommodation}")
            print(f"   Booking __str__: {booking}")
            
            # Set additional fields (as done in the view)
            booking.guest = guest_user
            booking.adults = 2
            booking.teens = 0
            booking.children = 0
            booking.infants = 0
            booking.total_guests = 2
            
            print(f"✅ Additional fields set")
            print(f"   Booking __str__ after setting fields: {booking}")
            
            # Test save (don't actually save to avoid conflicts)
            print(f"✅ Booking creation test successful - accommodation properly set")
            
        else:
            print(f"❌ Form validation failed: {form.errors}")
            
    except Exception as e:
        print(f"❌ Error during form-based booking creation: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Create booking directly (to test model methods)
    print(f"\n🧪 Test 2: Creating booking directly")
    
    try:
        booking = Booking(
            accommodation=accommodation,
            guest=guest_user,
            check_in_date=date.today() + timedelta(days=10),
            check_out_date=date.today() + timedelta(days=13),
            adults=2,
            teens=0,
            children=0,
            infants=0,
            total_guests=2,
            number_of_guests=2
        )
        
        print(f"✅ Direct booking creation successful")
        print(f"   Booking accommodation: {booking.accommodation}")
        print(f"   Booking __str__: {booking}")
        print(f"   Duration nights: {booking.duration_nights}")
        
        # Test clean method
        booking.clean()
        print(f"✅ Booking.clean() successful")
        
    except Exception as e:
        print(f"❌ Error during direct booking creation: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Test booking without accommodation (should fail gracefully)
    print(f"\n🧪 Test 3: Testing booking without accommodation")
    
    try:
        booking_no_acc = Booking(
            guest=guest_user,
            check_in_date=date.today() + timedelta(days=15),
            check_out_date=date.today() + timedelta(days=18),
            adults=2,
            total_guests=2
        )
        
        print(f"   Booking __str__ without accommodation: {booking_no_acc}")
        
        # This should fail gracefully
        try:
            booking_no_acc.clean()
            print(f"❌ Booking.clean() should have failed but didn't")
        except Exception as e:
            print(f"✅ Booking.clean() properly failed: {e}")
            
    except Exception as e:
        print(f"❌ Unexpected error in no-accommodation test: {e}")
    
    print(f"\n=== BOOKING CREATION TEST COMPLETE ===")

if __name__ == '__main__':
    test_booking_creation()
