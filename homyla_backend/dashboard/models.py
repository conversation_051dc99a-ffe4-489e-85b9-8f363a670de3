"""
Dashboard models for NomadPersia platform.

This module defines models for analytics, site settings, and admin functionality.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _


class SiteSettings(models.Model):
    """
    Model for storing site-wide settings and configuration.
    """

    # Site Information
    site_name = models.CharField(
        max_length=100,
        default='NomadPersia',
        help_text=_('Name of the website')
    )

    site_description = models.TextField(
        default='Travel accommodation booking platform for Iran',
        help_text=_('Description of the website')
    )

    # Featured Cities
    featured_cities = models.TextField(
        default='tehran,shiraz,mashhad',
        help_text=_('Comma-separated list of featured cities')
    )

    # Contact Information
    contact_email = models.EmailField(
        default='<EMAIL>',
        help_text=_('Contact email address')
    )

    support_phone = models.CharField(
        max_length=20,
        blank=True,
        help_text=_('Support phone number')
    )

    # Platform Settings
    auto_approve_accommodations = models.<PERSON><PERSON>anField(
        default=False,
        help_text=_('Automatically approve new accommodation listings')
    )

    auto_approve_bookings = models.BooleanField(
        default=True,
        help_text=_('Automatically approve booking requests')
    )

    # Site Control Settings (to be added in future migration)
    # maintenance_mode = models.BooleanField(
    #     default=False,
    #     help_text=_('Put the site in maintenance mode')
    # )
    #
    # allow_registration = models.BooleanField(
    #     default=True,
    #     help_text=_('Allow new user registration')
    # )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Site Settings')
        verbose_name_plural = _('Site Settings')

    def __str__(self):
        return f"Site Settings - {self.site_name}"

    def get_featured_cities_list(self):
        """Return featured cities as a list."""
        return [city.strip() for city in self.featured_cities.split(',')]

    @classmethod
    def get_settings(cls):
        """Get or create site settings instance."""
        settings, created = cls.objects.get_or_create(id=1)
        return settings


class ActivityLog(models.Model):
    """
    Model for logging important platform activities for analytics.
    """

    ACTIVITY_TYPES = [
        ('user_registration', _('User Registration')),
        ('accommodation_created', _('Accommodation Created')),
        ('accommodation_approved', _('Accommodation Approved')),
        ('booking_created', _('Booking Created')),
        ('booking_confirmed', _('Booking Confirmed')),
        ('booking_cancelled', _('Booking Cancelled')),
        ('user_login', _('User Login')),
        ('admin_action', _('Admin Action')),
    ]

    activity_type = models.CharField(
        max_length=30,
        choices=ACTIVITY_TYPES,
        help_text=_('Type of activity')
    )

    description = models.TextField(
        help_text=_('Description of the activity')
    )

    user = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='activities',
        help_text=_('User who performed the activity')
    )

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text=_('IP address of the user')
    )

    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text=_('Additional metadata about the activity')
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('Activity Log')
        verbose_name_plural = _('Activity Logs')
        indexes = [
            models.Index(fields=['activity_type']),
            models.Index(fields=['user']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_activity_type_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
