# Generated by Django 5.0.14 on 2025-07-02 09:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SiteSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "site_name",
                    models.CharField(
                        default="NomadPersia",
                        help_text="Name of the website",
                        max_length=100,
                    ),
                ),
                (
                    "site_description",
                    models.TextField(
                        default="Travel accommodation booking platform for Iran",
                        help_text="Description of the website",
                    ),
                ),
                (
                    "featured_cities",
                    models.TextField(
                        default="tehran,shiraz,mashhad",
                        help_text="Comma-separated list of featured cities",
                    ),
                ),
                (
                    "contact_email",
                    models.EmailField(
                        default="<EMAIL>",
                        help_text="Contact email address",
                        max_length=254,
                    ),
                ),
                (
                    "support_phone",
                    models.CharField(
                        blank=True, help_text="Support phone number", max_length=20
                    ),
                ),
                (
                    "auto_approve_accommodations",
                    models.BooleanField(
                        default=False,
                        help_text="Automatically approve new accommodation listings",
                    ),
                ),
                (
                    "auto_approve_bookings",
                    models.BooleanField(
                        default=True, help_text="Automatically approve booking requests"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Site Settings",
                "verbose_name_plural": "Site Settings",
            },
        ),
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "activity_type",
                    models.CharField(
                        choices=[
                            ("user_registration", "User Registration"),
                            ("accommodation_created", "Accommodation Created"),
                            ("accommodation_approved", "Accommodation Approved"),
                            ("booking_created", "Booking Created"),
                            ("booking_confirmed", "Booking Confirmed"),
                            ("booking_cancelled", "Booking Cancelled"),
                            ("user_login", "User Login"),
                            ("admin_action", "Admin Action"),
                        ],
                        help_text="Type of activity",
                        max_length=30,
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Description of the activity"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, help_text="IP address of the user", null=True
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional metadata about the activity",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who performed the activity",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Activity Log",
                "verbose_name_plural": "Activity Logs",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["activity_type"], name="dashboard_a_activit_4ea68e_idx"
                    ),
                    models.Index(
                        fields=["user"], name="dashboard_a_user_id_e8b689_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="dashboard_a_created_fb7b78_idx"
                    ),
                ],
            },
        ),
    ]
