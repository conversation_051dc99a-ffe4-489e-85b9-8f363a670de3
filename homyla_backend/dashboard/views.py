"""
Views for admin dashboard and analytics in NomadPersia platform.

This module contains views for comprehensive admin interface,
analytics, and system management.
"""

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.core.paginator import Paginator
from django.db.models import Count, Sum, Q
from django.shortcuts import render, redirect, get_object_or_404
from django.utils import timezone
from datetime import datetime, timedelta

from accounts.models import UserProfile
from accounts.views import role_required
from accommodations.models import Accommodation
from bookings.models import Booking
from .models import SiteSettings, ActivityLog


@login_required
@role_required(['host'])
def host_dashboard_view(request):
    """
    Dashboard for hosts to view their property and booking statistics.
    """
    # Get host's accommodations
    accommodations = Accommodation.objects.filter(host=request.user)

    # Get host's bookings
    bookings = Booking.objects.filter(accommodation__host=request.user)

    # Statistics
    total_properties = accommodations.count()
    active_properties = accommodations.filter(status='active').count()
    total_bookings = bookings.count()
    pending_bookings = bookings.filter(status='pending').count()
    confirmed_bookings = bookings.filter(status='confirmed').count()

    # Revenue calculation
    total_revenue = bookings.filter(
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('total_price'))['total'] or 0

    # Recent bookings
    recent_bookings = bookings.order_by('-created_at')[:5]

    context = {
        'total_properties': total_properties,
        'active_properties': active_properties,
        'total_bookings': total_bookings,
        'pending_bookings': pending_bookings,
        'confirmed_bookings': confirmed_bookings,
        'total_revenue': total_revenue,
        'recent_bookings': recent_bookings,
    }

    return render(request, 'dashboard/host_dashboard.html', context)


@login_required
@role_required(['admin'])
def admin_dashboard_view(request):
    """
    Main admin dashboard with key metrics and recent activity.
    """
    # Date ranges for analytics
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # User Statistics
    total_users = User.objects.count()
    new_users_week = User.objects.filter(date_joined__gte=week_ago).count()
    new_users_month = User.objects.filter(date_joined__gte=month_ago).count()

    # User role breakdown
    user_roles = UserProfile.objects.values('role').annotate(count=Count('role'))

    # Accommodation Statistics
    total_accommodations = Accommodation.objects.count()
    active_accommodations = Accommodation.objects.filter(status='active').count()
    pending_accommodations = Accommodation.objects.filter(status='pending').count()

    # Accommodation by city
    accommodations_by_city = Accommodation.objects.values('city').annotate(
        count=Count('city')
    ).order_by('-count')

    # Booking Statistics
    total_bookings = Booking.objects.count()
    pending_bookings = Booking.objects.filter(status='pending').count()
    confirmed_bookings = Booking.objects.filter(status='confirmed').count()
    completed_bookings = Booking.objects.filter(status='completed').count()

    # Revenue calculation (confirmed + completed bookings)
    total_revenue = Booking.objects.filter(
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('total_price'))['total'] or 0

    # Recent activity
    recent_users = User.objects.order_by('-date_joined')[:5]
    recent_accommodations = Accommodation.objects.order_by('-created_at')[:5]
    recent_bookings = Booking.objects.order_by('-created_at')[:5]

    # Weekly booking trends
    weekly_bookings = []
    for i in range(7):
        date = today - timedelta(days=i)
        count = Booking.objects.filter(created_at__date=date).count()
        weekly_bookings.append({
            'date': date,
            'count': count
        })
    weekly_bookings.reverse()

    context = {
        # User metrics
        'total_users': total_users,
        'new_users_week': new_users_week,
        'new_users_month': new_users_month,
        'user_roles': user_roles,

        # Accommodation metrics
        'total_accommodations': total_accommodations,
        'active_accommodations': active_accommodations,
        'pending_accommodations': pending_accommodations,
        'accommodations_by_city': accommodations_by_city,

        # Booking metrics
        'total_bookings': total_bookings,
        'pending_bookings': pending_bookings,
        'confirmed_bookings': confirmed_bookings,
        'completed_bookings': completed_bookings,
        'total_revenue': total_revenue,

        # Recent activity
        'recent_users': recent_users,
        'recent_accommodations': recent_accommodations,
        'recent_bookings': recent_bookings,
        'weekly_bookings': weekly_bookings,
    }

    return render(request, 'dashboard/admin_dashboard.html', context)


@login_required
@role_required(['admin'])
def user_management_view(request):
    """
    User management interface for admins.
    """
    # Get filter parameters
    role_filter = request.GET.get('role', '')
    search_query = request.GET.get('search', '')

    # Base queryset
    users = User.objects.select_related('profile').order_by('-date_joined')

    # Apply filters
    if role_filter:
        users = users.filter(profile__role=role_filter)

    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_count = users.count()
    role_counts = UserProfile.objects.values('role').annotate(count=Count('role'))

    context = {
        'page_obj': page_obj,
        'users': page_obj,
        'total_count': total_count,
        'role_counts': role_counts,
        'role_filter': role_filter,
        'search_query': search_query,
        'role_choices': UserProfile.ROLE_CHOICES,
    }

    return render(request, 'dashboard/user_management.html', context)


@login_required
@role_required(['admin'])
def accommodation_moderation_view(request):
    """
    Accommodation moderation interface for admins.
    """
    # Get filter parameters
    status_filter = request.GET.get('status', 'pending')
    city_filter = request.GET.get('city', '')

    # Base queryset
    accommodations = Accommodation.objects.select_related('host').order_by('-created_at')

    # Apply filters
    if status_filter:
        accommodations = accommodations.filter(status=status_filter)

    if city_filter:
        accommodations = accommodations.filter(city=city_filter)

    # Pagination
    paginator = Paginator(accommodations, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_count = accommodations.count()
    status_counts = Accommodation.objects.values('status').annotate(count=Count('status'))

    context = {
        'page_obj': page_obj,
        'accommodations': page_obj,
        'total_count': total_count,
        'status_counts': status_counts,
        'status_filter': status_filter,
        'city_filter': city_filter,
        'status_choices': Accommodation.STATUS_CHOICES,
        'city_choices': Accommodation.CITIES,
    }

    return render(request, 'dashboard/accommodation_moderation.html', context)


@login_required
@role_required(['admin'])
def accommodation_action_view(request, pk):
    """
    Handle accommodation moderation actions (approve, reject, activate, deactivate).
    """
    if request.method != 'POST':
        return redirect('dashboard:admin_accommodations')

    accommodation = get_object_or_404(Accommodation, pk=pk)
    action = request.POST.get('action')

    if action == 'approve':
        accommodation.status = 'active'
        accommodation.save()
        messages.success(request, f'Accommodation "{accommodation.name}" has been approved and activated.')

    elif action == 'reject':
        accommodation.status = 'rejected'
        accommodation.save()
        messages.warning(request, f'Accommodation "{accommodation.name}" has been rejected.')

    elif action == 'activate':
        accommodation.status = 'active'
        accommodation.save()
        messages.success(request, f'Accommodation "{accommodation.name}" has been activated.')

    elif action == 'deactivate':
        accommodation.status = 'inactive'
        accommodation.save()
        messages.info(request, f'Accommodation "{accommodation.name}" has been deactivated.')

    else:
        messages.error(request, 'Invalid action.')

    return redirect('dashboard:admin_accommodations')


@login_required
@role_required(['admin'])
def booking_analytics_view(request):
    """
    Booking analytics and management for admins.
    """
    # Date range filter
    date_range = request.GET.get('range', '30')
    try:
        days = int(date_range)
    except ValueError:
        days = 30

    start_date = timezone.now().date() - timedelta(days=days)

    # Booking statistics
    bookings = Booking.objects.filter(created_at__date__gte=start_date)

    total_bookings = bookings.count()
    revenue = bookings.filter(
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('total_price'))['total'] or 0

    # Status breakdown
    status_breakdown = bookings.values('status').annotate(count=Count('status'))

    # Individual status counts
    confirmed_bookings = bookings.filter(status='confirmed').count()
    pending_bookings = bookings.filter(status='pending').count()
    completed_bookings = bookings.filter(status='completed').count()
    cancelled_bookings = bookings.filter(status='cancelled').count()

    # Daily booking trends
    daily_bookings = []
    for i in range(days):
        date = start_date + timedelta(days=i)
        count = bookings.filter(created_at__date=date).count()
        daily_revenue = bookings.filter(
            created_at__date=date,
            status__in=['confirmed', 'completed']
        ).aggregate(total=Sum('total_price'))['total'] or 0

        daily_bookings.append({
            'date': date,
            'bookings': count,
            'revenue': daily_revenue
        })

    # Top accommodations by bookings
    top_accommodations = Accommodation.objects.annotate(
        booking_count=Count('bookings')
    ).order_by('-booking_count')[:10]

    # Recent bookings
    recent_bookings = Booking.objects.select_related('guest', 'accommodation').order_by('-created_at')[:10]

    # Additional analytics
    total_revenue = Booking.objects.filter(
        status__in=['confirmed', 'completed']
    ).aggregate(total=Sum('total_price'))['total'] or 0

    monthly_revenue = Booking.objects.filter(
        status__in=['confirmed', 'completed'],
        created_at__month=timezone.now().month,
        created_at__year=timezone.now().year
    ).aggregate(total=Sum('total_price'))['total'] or 0

    average_booking_value = total_revenue / total_bookings if total_bookings > 0 else 0
    occupancy_rate = 75.5  # Placeholder calculation

    context = {
        'total_bookings': total_bookings,
        'confirmed_bookings': confirmed_bookings,
        'pending_bookings': pending_bookings,
        'completed_bookings': completed_bookings,
        'cancelled_bookings': cancelled_bookings,
        'revenue': revenue,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'average_booking_value': average_booking_value,
        'occupancy_rate': occupancy_rate,
        'status_breakdown': status_breakdown,
        'daily_bookings': daily_bookings,
        'top_accommodations': top_accommodations,
        'recent_bookings': recent_bookings,
        'date_range': date_range,
        'start_date': start_date,
    }

    return render(request, 'dashboard/booking_analytics.html', context)


@login_required
@role_required(['admin'])
def site_settings_view(request):
    """
    Site settings management for admins.
    """
    from django.contrib.auth import get_user_model
    from accommodations.models import Accommodation
    from bookings.models import Booking
    from reviews.models import Review
    import django

    User = get_user_model()
    settings, created = SiteSettings.objects.get_or_create(pk=1)

    if request.method == 'POST':
        # Update settings
        settings.site_name = request.POST.get('site_name', settings.site_name)
        settings.site_description = request.POST.get('site_description', settings.site_description)
        settings.contact_email = request.POST.get('contact_email', settings.contact_email)

        # Note: maintenance_mode and allow_registration fields will be added in future update
        # For now, we'll just save the basic settings

        settings.save()

        messages.success(request, 'Site settings updated successfully.')
        return redirect('dashboard:site_settings')

    # Get platform statistics
    total_users = User.objects.count()
    total_accommodations = Accommodation.objects.count()
    total_bookings = Booking.objects.count()
    total_reviews = Review.objects.count()

    context = {
        'settings': settings,
        'total_users': total_users,
        'total_accommodations': total_accommodations,
        'total_bookings': total_bookings,
        'total_reviews': total_reviews,
        'django_version': django.get_version(),
    }

    return render(request, 'dashboard/site_settings.html', context)
