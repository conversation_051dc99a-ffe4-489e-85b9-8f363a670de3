"""
URL patterns for the dashboard app.

This module defines URL patterns for admin and host dashboards,
analytics, and management interfaces.
"""

from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # Host dashboard
    path('host/', views.host_dashboard_view, name='host_dashboard'),

    # Admin dashboard and management
    path('admin/', views.admin_dashboard_view, name='admin'),
    path('admin/users/', views.user_management_view, name='admin_users'),
    path('admin/accommodations/', views.accommodation_moderation_view, name='admin_accommodations'),
    path('admin/accommodations/<int:pk>/action/', views.accommodation_action_view, name='admin_accommodation_action'),
    path('admin/bookings/', views.booking_analytics_view, name='admin_bookings'),
    path('admin/settings/', views.site_settings_view, name='site_settings'),
]
