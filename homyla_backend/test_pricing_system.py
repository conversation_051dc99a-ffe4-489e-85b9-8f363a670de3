#!/usr/bin/env python
"""
Test script to verify holiday/seasonal pricing is working correctly
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation, AccommodationPricing
from bookings.models import Booking
from datetime import date, timedelta
from django.utils import timezone
from decimal import Decimal

User = get_user_model()

def test_pricing_system():
    print("=== TESTING HOLIDAY/SEASONAL PRICING SYSTEM ===")
    
    # Get or create test accommodation
    try:
        accommodation = Accommodation.objects.first()
        if not accommodation:
            print("❌ No accommodations found. Please run: python manage.py load_sample_data")
            return
        
        print(f"🏠 Testing with accommodation: {accommodation.name}")
        print(f"   Base price: {accommodation.price_per_night} IRR")
        
        # Create test pricing rules
        today = date.today()
        
        # Holiday pricing (higher rate)
        holiday_start = today + timedelta(days=10)
        holiday_end = today + timedelta(days=15)
        holiday_price = int(accommodation.price_per_night * 1.5)  # 50% increase
        
        holiday_pricing, created = AccommodationPricing.objects.get_or_create(
            accommodation=accommodation,
            pricing_type='holiday',
            start_date=holiday_start,
            end_date=holiday_end,
            defaults={
                'price_per_night': holiday_price,
                'description': 'New Year Holiday Premium',
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created holiday pricing: {holiday_start} to {holiday_end} at {holiday_price} IRR")
        else:
            print(f"📝 Using existing holiday pricing: {holiday_start} to {holiday_end} at {holiday_pricing.price_per_night} IRR")
        
        # Peak season pricing
        peak_start = today + timedelta(days=20)
        peak_end = today + timedelta(days=30)
        peak_price = int(accommodation.price_per_night * 1.3)  # 30% increase
        
        peak_pricing, created = AccommodationPricing.objects.get_or_create(
            accommodation=accommodation,
            pricing_type='peak_season',
            start_date=peak_start,
            end_date=peak_end,
            defaults={
                'price_per_night': peak_price,
                'description': 'Summer Peak Season',
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created peak season pricing: {peak_start} to {peak_end} at {peak_price} IRR")
        else:
            print(f"📝 Using existing peak season pricing: {peak_start} to {peak_end} at {peak_pricing.price_per_night} IRR")
        
        print("\n=== TESTING PRICE CALCULATION ===")
        
        # Test 1: Regular dates (should use base price)
        regular_start = today + timedelta(days=5)
        regular_end = today + timedelta(days=8)
        
        print(f"\n🧪 Test 1: Regular dates ({regular_start} to {regular_end})")
        for i in range((regular_end - regular_start).days):
            test_date = regular_start + timedelta(days=i)
            price = accommodation.get_price_for_date(test_date)
            print(f"   {test_date}: {price} IRR")
        
        total_regular = accommodation.calculate_total_price(regular_start, regular_end, 2)
        expected_regular = int(accommodation.price_per_night) * (regular_end - regular_start).days
        print(f"   Total calculated: {total_regular} IRR")
        print(f"   Expected (base): {expected_regular} IRR")
        print(f"   ✅ Match: {total_regular == expected_regular}")
        
        # Test 2: Holiday dates (should use holiday pricing)
        print(f"\n🧪 Test 2: Holiday dates ({holiday_start} to {holiday_end})")
        for i in range((holiday_end - holiday_start).days):
            test_date = holiday_start + timedelta(days=i)
            price = accommodation.get_price_for_date(test_date)
            print(f"   {test_date}: {price} IRR")
        
        total_holiday = accommodation.calculate_total_price(holiday_start, holiday_end, 2)
        expected_holiday = holiday_pricing.price_per_night * (holiday_end - holiday_start).days
        print(f"   Total calculated: {total_holiday} IRR")
        print(f"   Expected (holiday): {expected_holiday} IRR")
        print(f"   ✅ Match: {total_holiday == expected_holiday}")
        
        # Test 3: Mixed dates (spanning regular and holiday)
        mixed_start = today + timedelta(days=8)
        mixed_end = today + timedelta(days=13)
        
        print(f"\n🧪 Test 3: Mixed dates ({mixed_start} to {mixed_end})")
        expected_mixed = 0
        for i in range((mixed_end - mixed_start).days):
            test_date = mixed_start + timedelta(days=i)
            price = accommodation.get_price_for_date(test_date)
            expected_mixed += price
            print(f"   {test_date}: {price} IRR")
        
        total_mixed = accommodation.calculate_total_price(mixed_start, mixed_end, 2)
        print(f"   Total calculated: {total_mixed} IRR")
        print(f"   Expected (mixed): {expected_mixed} IRR")
        print(f"   ✅ Match: {total_mixed == expected_mixed}")
        
        print("\n=== TESTING BOOKING CREATION ===")
        
        # Get a guest user
        guest_user = User.objects.filter(profile__role='guest').first()
        if not guest_user:
            print("❌ No guest users found")
            return
        
        print(f"👤 Testing with guest: {guest_user.username}")
        
        # Test booking creation with holiday pricing
        test_booking = Booking(
            accommodation=accommodation,
            guest=guest_user,
            check_in_date=holiday_start,
            check_out_date=holiday_end,
            adults=2,
            teens=0,
            children=0,
            infants=0,
            total_guests=2
        )
        
        # Calculate price using accommodation method
        calculated_price = accommodation.calculate_total_price(
            holiday_start, holiday_end, 2,
            adults=2, teens=0, children=0, infants=0
        )
        
        test_booking.total_price = calculated_price
        
        print(f"🧪 Test booking for holiday period:")
        print(f"   Dates: {holiday_start} to {holiday_end}")
        print(f"   Calculated price: {calculated_price} IRR")
        print(f"   Expected holiday price: {expected_holiday} IRR")
        print(f"   ✅ Pricing correct: {calculated_price == expected_holiday}")
        
        # Don't save the test booking to avoid conflicts
        print(f"   (Test booking not saved to database)")
        
        print("\n=== PRICING SYSTEM TEST COMPLETE ===")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_pricing_system()
