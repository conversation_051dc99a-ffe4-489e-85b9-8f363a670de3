server {
    listen 443 ssl;
    server_name nomadpersia.hprojects.dev;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # Serve static files directly
    location /static/ {
        alias /var/www/homyla/homyla_backend/staticfiles/;
        expires 30d;
    }

    # Serve media files directly
    location /media/ {
        alias /var/www/homyla/homyla_backend/media/;
        expires 30d;
    }
    
    # Proxy everything else to Django
    location / {
        proxy_pass http://127.0.0.1:8004;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}