{% extends 'base.html' %}

{% block title %}
    {% if object %}Edit Property{% else %}Add New Property{% endif %} - NomadPersia
{% endblock %}

{% block extra_css %}
<!-- Cropper.js CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">

<style>
/* Photo Gallery Styles */
.photo-upload-container {
    position: relative;
    height: 200px;
}

.photo-upload-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-upload-box:hover {
    border-color: #3498db !important;
    background-color: #f8f9fa;
}

.photo-preview {
    position: relative;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-preview:hover .photo-overlay {
    opacity: 1;
}

.additional-photo-item {
    position: relative;
    margin-bottom: 15px;
}

.additional-photo-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.photo-counter {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
}

/* Photo Editor Modal Styles */
.photo-editor-modal .modal-dialog {
    max-width: 900px;
}

.photo-editor-container {
    max-height: 400px;
    overflow: hidden;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.photo-editor-container img {
    max-width: 100%;
    display: block;
}

.crop-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 15px 0;
}

.crop-controls .btn {
    min-width: 80px;
}

.tag-chips-container {
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 15px 0;
}

.tag-chip {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 6px 12px;
    margin: 3px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    font-size: 0.9rem;
}

.tag-chip:hover {
    background: #dee2e6;
    transform: translateY(-1px);
}

.tag-chip.selected {
    background: #3498db;
    color: white;
    border-color: #2980b9;
}

.tag-chip.selected:hover {
    background: #2980b9;
}

.custom-tag-input {
    margin-top: 10px;
}

.frame-alignment-guide {
    text-align: center;
    padding: 10px;
    background: #e3f2fd;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #1976d2;
}

.photo-title-input {
    margin-bottom: 20px;
}

.selected-tags-display {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    min-height: 40px;
}

.selected-tag {
    display: inline-block;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 12px;
    font-size: 0.8rem;
}
    font-size: 0.75rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h3 class="mb-0">
                        {% if object %}
                            ✏️ Edit Property
                        {% else %}
                            ➕ Add New Property
                        {% endif %}
                    </h3>
                    <small class="text-muted">
                        {% if object %}
                            Update your accommodation details
                        {% else %}
                            List your accommodation on NomadPersia
                        {% endif %}
                    </small>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <strong>Please correct the errors below:</strong>
                            <ul class="mb-0 mt-2">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">🏨 Basic Information</h5>
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">Property Name *</label>
                                    <input type="text" class="form-control" name="name" 
                                           id="{{ form.name.id_for_label }}" 
                                           value="{{ form.name.value|default:'' }}" required
                                           placeholder="e.g., Beautiful Tehran Hotel">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.accommodation_type.id_for_label }}" class="form-label">Property Type *</label>
                                    <select class="form-select" name="accommodation_type"
                                            id="{{ form.accommodation_type.id_for_label }}" required>
                                        <option value="">Select type</option>
                                        <option value="villa_suite" {% if form.accommodation_type.value == 'villa_suite' %}selected{% endif %}>Villa & Suite</option>
                                        <option value="apartment" {% if form.accommodation_type.value == 'apartment' %}selected{% endif %}>Apartment</option>
                                        <option value="cottage" {% if form.accommodation_type.value == 'cottage' %}selected{% endif %}>Cottage</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.city.id_for_label }}" class="form-label">City *</label>
                                    <select class="form-select" name="city"
                                            id="{{ form.city.id_for_label }}" required>
                                        <option value="">Select city</option>
                                        <option value="tehran" {% if form.city.value == 'tehran' %}selected{% endif %}>Tehran</option>
                                        <option value="shiraz" {% if form.city.value == 'shiraz' %}selected{% endif %}>Shiraz</option>
                                        <option value="mashhad" {% if form.city.value == 'mashhad' %}selected{% endif %}>Mashhad</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.town.id_for_label }}" class="form-label">Town/Suburb</label>
                                    <input type="text" class="form-control" name="town"
                                           id="{{ form.town.id_for_label }}"
                                           value="{{ form.town.value|default:'' }}"
                                           placeholder="e.g., Zafaraniyeh, Elahiyeh, Niavaran">
                                    <small class="form-text text-muted">Specific area, district, or suburb within the city</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.star_rating.id_for_label }}" class="form-label">Star Rating</label>
                                    <select class="form-select" name="star_rating" 
                                            id="{{ form.star_rating.id_for_label }}">
                                        <option value="">No rating</option>
                                        <option value="1" {% if form.star_rating.value == 1 or form.star_rating.value == '1' %}selected{% endif %}>1 Star</option>
                                        <option value="2" {% if form.star_rating.value == 2 or form.star_rating.value == '2' %}selected{% endif %}>2 Stars</option>
                                        <option value="3" {% if form.star_rating.value == 3 or form.star_rating.value == '3' %}selected{% endif %}>3 Stars</option>
                                        <option value="4" {% if form.star_rating.value == 4 or form.star_rating.value == '4' %}selected{% endif %}>4 Stars</option>
                                        <option value="5" {% if form.star_rating.value == 5 or form.star_rating.value == '5' %}selected{% endif %}>5 Stars</option>
                                    </select>
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="{{ form.address.id_for_label }}" class="form-label">Complete Address *</label>
                                    <textarea class="form-control" name="address"
                                              id="{{ form.address.id_for_label }}"
                                              rows="3" required
                                              placeholder="Enter complete address including street, neighborhood, and postal code">{{ form.address.value|default:'' }}</textarea>
                                    <small class="form-text text-muted">Include street address, neighborhood, and postal code for accurate location</small>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📞 Contact Information</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.reservation_phone.id_for_label }}" class="form-label">Reservation Phone *</label>
                                    <input type="tel" class="form-control" name="reservation_phone"
                                           id="{{ form.reservation_phone.id_for_label }}"
                                           value="{{ form.reservation_phone.value|default:'' }}" required
                                           placeholder="e.g., +98 21 1234 5678 or 09123456789">
                                    <small class="form-text text-muted">Phone number for guest reservations and contact</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.geolocation.id_for_label }}" class="form-label">GPS Coordinates</label>
                                    <input type="text" class="form-control" name="geolocation"
                                           id="{{ form.geolocation.id_for_label }}"
                                           value="{{ form.geolocation.value|default:'' }}"
                                           placeholder="e.g., 35.6892, 51.3890">
                                    <small class="form-text text-muted">Optional: Enter coordinates (lat,lng) or select on map</small>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📝 Description</h5>
                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">Property Description *</label>
                                <textarea class="form-control" name="description" 
                                          id="{{ form.description.id_for_label }}" 
                                          rows="5" required
                                          placeholder="Describe your property, its features, and what makes it special...">{{ form.description.value|default:'' }}</textarea>
                                <small class="form-text text-muted">Provide a detailed description to attract guests</small>
                            </div>
                        </div>

                        <!-- Capacity & Sleeping Arrangements -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">👥 Capacity & Sleeping</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.standard_capacity.id_for_label }}" class="form-label">Standard Capacity *</label>
                                    <select class="form-select" name="standard_capacity"
                                            id="{{ form.standard_capacity.id_for_label }}" required>
                                        <option value="">Select capacity</option>
                                        <option value="2" {% if form.standard_capacity.value == 2 or form.standard_capacity.value == '2' %}selected{% endif %}>Up to 2 guests</option>
                                        <option value="4" {% if form.standard_capacity.value == 4 or form.standard_capacity.value == '4' %}selected{% endif %}>Up to 4 guests</option>
                                        <option value="6" {% if form.standard_capacity.value == 6 or form.standard_capacity.value == '6' %}selected{% endif %}>Up to 6 guests</option>
                                        <option value="8" {% if form.standard_capacity.value == 8 or form.standard_capacity.value == '8' %}selected{% endif %}>Up to 8 guests</option>
                                        <option value="10" {% if form.standard_capacity.value == 10 or form.standard_capacity.value == '10' %}selected{% endif %}>Up to 10 guests</option>
                                        <option value="12" {% if form.standard_capacity.value == 12 or form.standard_capacity.value == '12' %}selected{% endif %}>Up to 12 guests</option>
                                    </select>
                                    <small class="form-text text-muted">Base capacity included in price</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.max_capacity.id_for_label }}" class="form-label">Maximum Capacity</label>
                                    <select class="form-select" name="max_capacity"
                                            id="{{ form.max_capacity.id_for_label }}">
                                        <option value="">Same as standard</option>
                                        <option value="4" {% if form.max_capacity.value == 4 or form.max_capacity.value == '4' %}selected{% endif %}>Up to 4 guests</option>
                                        <option value="6" {% if form.max_capacity.value == 6 or form.max_capacity.value == '6' %}selected{% endif %}>Up to 6 guests</option>
                                        <option value="8" {% if form.max_capacity.value == 8 or form.max_capacity.value == '8' %}selected{% endif %}>Up to 8 guests</option>
                                        <option value="10" {% if form.max_capacity.value == 10 or form.max_capacity.value == '10' %}selected{% endif %}>Up to 10 guests</option>
                                        <option value="12" {% if form.max_capacity.value == 12 or form.max_capacity.value == '12' %}selected{% endif %}>Up to 12 guests</option>
                                        <option value="15" {% if form.max_capacity.value == 15 or form.max_capacity.value == '15' %}selected{% endif %}>Up to 15 guests</option>
                                        <option value="20" {% if form.max_capacity.value == 20 or form.max_capacity.value == '20' %}selected{% endif %}>Up to 20 guests</option>
                                    </select>
                                    <small class="form-text text-muted">Maximum guests allowed</small>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.bedrooms.id_for_label }}" class="form-label">Number of Bedrooms *</label>
                                    <select class="form-select" name="bedrooms"
                                            id="{{ form.bedrooms.id_for_label }}" required>
                                        <option value="">Select bedrooms</option>
                                        <option value="1" {% if form.bedrooms.value == 1 or form.bedrooms.value == '1' %}selected{% endif %}>1 Bedroom</option>
                                        <option value="2" {% if form.bedrooms.value == 2 or form.bedrooms.value == '2' %}selected{% endif %}>2 Bedrooms</option>
                                        <option value="3" {% if form.bedrooms.value == 3 or form.bedrooms.value == '3' %}selected{% endif %}>3 Bedrooms</option>
                                        <option value="4" {% if form.bedrooms.value == 4 or form.bedrooms.value == '4' %}selected{% endif %}>4 Bedrooms</option>
                                        <option value="5" {% if form.bedrooms.value == 5 or form.bedrooms.value == '5' %}selected{% endif %}>5+ Bedrooms</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.bathrooms.id_for_label }}" class="form-label">Bathrooms *</label>
                                    <select class="form-select" name="bathrooms"
                                            id="{{ form.bathrooms.id_for_label }}" required>
                                        <option value="">Select bathrooms</option>
                                        <option value="1" {% if form.bathrooms.value == 1 or form.bathrooms.value == '1' %}selected{% endif %}>1 Bathroom</option>
                                        <option value="2" {% if form.bathrooms.value == 2 or form.bathrooms.value == '2' %}selected{% endif %}>2 Bathrooms</option>
                                        <option value="3" {% if form.bathrooms.value == 3 or form.bathrooms.value == '3' %}selected{% endif %}>3 Bathrooms</option>
                                        <option value="4" {% if form.bathrooms.value == 4 or form.bathrooms.value == '4' %}selected{% endif %}>4+ Bathrooms</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.property_size.id_for_label }}" class="form-label">Property Size (sq meters)</label>
                                    <input type="number" class="form-control" name="property_size"
                                           id="{{ form.property_size.id_for_label }}"
                                           value="{{ form.property_size.value|default:'' }}"
                                           min="50" step="10"
                                           placeholder="e.g., 350">
                                    <small class="form-text text-muted">Indoor space in square meters</small>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing & Guest Capacity Configuration -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">💰 Pricing & Guest Capacity Configuration</h5>

                            <!-- Base Pricing -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">🏷️ Base Pricing</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ form.price_per_night.id_for_label }}" class="form-label">Base Price per Night (IRR) *</label>
                                            <input type="number" class="form-control" name="price_per_night"
                                                   id="{{ form.price_per_night.id_for_label }}"
                                                   value="{{ form.price_per_night.value|default:'' }}" required
                                                   min="100000" step="50000"
                                                   placeholder="e.g., 1500000">
                                            <small class="form-text text-muted">Default nightly rate for standard capacity</small>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="alert alert-info">
                                                <small>
                                                    <strong>💡 Tip:</strong> This is your default nightly rate. You can set different prices for specific dates below.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dynamic Pricing Calendar -->
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">🗓️ Dynamic Pricing Calendar</h6>
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="addPricingRule">
                                        + Add Pricing Rule
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <div class="alert alert-light">
                                                <strong>📅 Set different prices for:</strong>
                                                <ul class="mb-0 mt-2">
                                                    <li>Weekends vs Weekdays</li>
                                                    <li>Public holidays (Nowruz, etc.)</li>
                                                    <li>Peak seasons</li>
                                                    <li>Custom date ranges</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Pricing Rules Container -->
                                    <div id="pricingRulesContainer">
                                        <!-- Dynamic pricing rules will be added here -->
                                    </div>

                                    <!-- Quick Actions -->
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addWeekendPricing()">
                                                    📅 Set Weekend Pricing
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addHolidayPricing()">
                                                    🎉 Add Holiday Pricing
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addPeakSeasonPricing()">
                                                    ⛰️ Peak Season Pricing
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Guest Capacity & Extra Fees -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">👥 Guest Capacity & Extra Fees</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.standard_capacity.id_for_label }}" class="form-label">Base Guest Capacity *</label>
                                            <input type="number" class="form-control" name="standard_capacity"
                                                   id="{{ form.standard_capacity.id_for_label }}"
                                                   value="{{ form.standard_capacity.value|default:'' }}" required
                                                   min="1" max="20"
                                                   placeholder="e.g., 4">
                                            <small class="form-text text-muted">Guests included in base price</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.max_capacity.id_for_label }}" class="form-label">Maximum Total Guests *</label>
                                            <input type="number" class="form-control" name="max_capacity"
                                                   id="{{ form.max_capacity.id_for_label }}"
                                                   value="{{ form.max_capacity.value|default:'' }}" required
                                                   min="1" max="30"
                                                   placeholder="e.g., 6">
                                            <small class="form-text text-muted">Maximum guests allowed</small>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="{{ form.extra_guest_fee.id_for_label }}" class="form-label">Extra Guest Fee (IRR)</label>
                                            <input type="number" class="form-control" name="extra_guest_fee"
                                                   id="{{ form.extra_guest_fee.id_for_label }}"
                                                   value="{{ form.extra_guest_fee.value|default:'' }}"
                                                   min="0" step="10000"
                                                   placeholder="e.g., 300000">
                                            <small class="form-text text-muted">Per extra guest per night</small>
                                        </div>
                                    </div>

                                    <!-- Capacity Calculator -->
                                    <div class="alert alert-info">
                                        <div class="row align-items-center">
                                            <div class="col-md-8">
                                                <strong>📊 Capacity Calculator:</strong>
                                                <div id="capacityCalculator" class="mt-2">
                                                    <span id="baseCapacityText">Base: 0 guests</span> |
                                                    <span id="extraCapacityText">Extra: 0 guests</span> |
                                                    <span id="totalCapacityText">Total: 0 guests</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4 text-end">
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleAgeSpecificPricing()">
                                                    👶 Age-Specific Pricing
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Age-Specific Pricing (Hidden by default) -->
                                    <div id="ageSpecificPricing" class="mt-3" style="display: none;">
                                        <h6>👶 Age-Specific Pricing Rules</h6>
                                        <div class="row">
                                            <div class="col-md-3 mb-3">
                                                <label class="form-label">Infants (0-2 years)</label>
                                                <input type="number" class="form-control" id="infantFee" placeholder="0" min="0" step="1000">
                                                <div class="form-check mt-1">
                                                    <input class="form-check-input" type="checkbox" id="infantsCountCapacity">
                                                    <label class="form-check-label small" for="infantsCountCapacity">
                                                        Count in capacity
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label class="form-label">Children (3-12 years)</label>
                                                <input type="number" class="form-control" id="childFee" placeholder="0" min="0" step="1000">
                                                <div class="form-check mt-1">
                                                    <input class="form-check-input" type="checkbox" id="childrenCountCapacity" checked>
                                                    <label class="form-check-label small" for="childrenCountCapacity">
                                                        Count in capacity
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label class="form-label">Teens (13-17 years)</label>
                                                <input type="number" class="form-control" id="teenFee" placeholder="0" min="0" step="1000">
                                                <div class="form-check mt-1">
                                                    <input class="form-check-input" type="checkbox" id="teensCountCapacity" checked>
                                                    <label class="form-check-label small" for="teensCountCapacity">
                                                        Count in capacity
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mb-3">
                                                <label class="form-label">Adults (18+ years)</label>
                                                <div class="form-control bg-light">Standard rate</div>
                                                <div class="form-check mt-1">
                                                    <input class="form-check-input" type="checkbox" checked disabled>
                                                    <label class="form-check-label small">
                                                        Always count in capacity
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Booking Policies -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📋 Booking Policies</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.instant_booking.id_for_label }}" class="form-label">Booking Confirmation *</label>
                                    <select class="form-select" name="instant_booking"
                                            id="{{ form.instant_booking.id_for_label }}" required>
                                        <option value="">Select confirmation type</option>
                                        <option value="true" {% if form.instant_booking.value == 'true' or form.instant_booking.value == True %}selected{% endif %}>Instant Booking (No host approval needed)</option>
                                        <option value="false" {% if form.instant_booking.value == 'false' or form.instant_booking.value == False %}selected{% endif %}>Host Approval Required</option>
                                    </select>
                                    <small class="form-text text-muted">Choose if bookings need your approval</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.cancellation_policy.id_for_label }}" class="form-label">Cancellation Policy</label>
                                    <select class="form-select" name="cancellation_policy"
                                            id="{{ form.cancellation_policy.id_for_label }}">
                                        <option value="">Select policy</option>
                                        <option value="flexible" {% if form.cancellation_policy.value == 'flexible' %}selected{% endif %}>Flexible (24 hours before)</option>
                                        <option value="moderate" {% if form.cancellation_policy.value == 'moderate' %}selected{% endif %}>Moderate (5 days before)</option>
                                        <option value="strict" {% if form.cancellation_policy.value == 'strict' %}selected{% endif %}>Strict (10 days before)</option>
                                    </select>
                                    <small class="form-text text-muted">Cancellation terms for guests</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.check_in_time.id_for_label }}" class="form-label">Check-in Until</label>
                                    <select class="form-select" name="check_in_time"
                                            id="{{ form.check_in_time.id_for_label }}">
                                        <option value="">Anytime</option>
                                        <option value="18:00" {% if form.check_in_time.value == '18:00' %}selected{% endif %}>6:00 PM</option>
                                        <option value="20:00" {% if form.check_in_time.value == '20:00' %}selected{% endif %}>8:00 PM</option>
                                        <option value="22:00" {% if form.check_in_time.value == '22:00' %}selected{% endif %}>10:00 PM</option>
                                        <option value="24:00" {% if form.check_in_time.value == '24:00' %}selected{% endif %}>12:00 Midnight</option>
                                    </select>
                                    <small class="form-text text-muted">Latest check-in time</small>
                                </div>
                            </div>
                        </div>

                        <!-- Amenities -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">🌟 Amenities & Features</h5>

                            <!-- Essential Amenities -->
                            <div class="mb-3">
                                <label class="form-label">Essential Amenities</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_essential" value="wifi" id="wifi">
                                            <label class="form-check-label" for="wifi">📶 WiFi</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_essential" value="parking" id="parking">
                                            <label class="form-check-label" for="parking">🚗 Parking</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_essential" value="air_conditioning" id="air_conditioning">
                                            <label class="form-check-label" for="air_conditioning">❄️ Air Conditioning</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_essential" value="heating" id="heating">
                                            <label class="form-check-label" for="heating">🔥 Heating</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_kitchen" value="kitchen" id="kitchen">
                                            <label class="form-check-label" for="kitchen">🍳 Full Kitchen</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_kitchen" value="refrigerator" id="refrigerator">
                                            <label class="form-check-label" for="refrigerator">🧊 Refrigerator</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_kitchen" value="stove" id="stove">
                                            <label class="form-check-label" for="stove">🔥 Stove</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_kitchen" value="tableware" id="tableware">
                                            <label class="form-check-label" for="tableware">🍽️ Tableware</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_luxury" value="swimming_pool" id="swimming_pool">
                                            <label class="form-check-label" for="swimming_pool">🏊 Swimming Pool</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_luxury" value="mountain_view" id="mountain_view">
                                            <label class="form-check-label" for="mountain_view">🏔️ Mountain View</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_luxury" value="forest_view" id="forest_view">
                                            <label class="form-check-label" for="forest_view">🌲 Forest View</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="amenities_luxury" value="garden" id="garden">
                                            <label class="form-check-label" for="garden">🌿 Garden</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Amenities -->
                            <div class="mb-3">
                                <label for="{{ form.amenities.id_for_label }}" class="form-label">Additional Amenities</label>
                                <textarea class="form-control" name="amenities"
                                          id="{{ form.amenities.id_for_label }}"
                                          rows="3"
                                          placeholder="List any additional amenities not covered above (e.g., Spa, Gym, BBQ area, Fireplace, etc.)">{{ form.amenities.value|default:'' }}</textarea>
                                <small class="form-text text-muted">Describe unique features that make your property special</small>
                            </div>
                        </div>

                        <!-- Photo Gallery -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📸 Photo Gallery</h5>
                            <div class="mb-3">
                                <label class="form-label">Property Photos</label>
                                <div class="border rounded p-3 bg-light">
                                    <div class="row" id="photoGallery">
                                        <!-- Main Photo -->
                                        <div class="col-md-4 mb-3" data-photo-type="main">
                                            <div class="photo-upload-container">
                                                <div class="photo-upload-box text-center p-4 border border-dashed rounded" id="mainPhotoBox">
                                                    <i class="fas fa-camera mb-2 text-muted"></i>
                                                    <p class="mb-2">📸 Main Photo</p>
                                                    <input type="file" class="form-control-file d-none" accept="image/*" id="mainPhoto" name="mainPhoto" data-type="main">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('mainPhoto').click()">
                                                        Choose & Edit Photo
                                                    </button>
                                                    <small class="d-block text-muted mt-1">Crop, rotate & tag your image</small>
                                                </div>
                                                <div class="photo-preview d-none" id="mainPhotoPreview">
                                                    <img src="" alt="Main Photo" class="img-fluid rounded">
                                                    <div class="photo-overlay">
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('main')">
                                                            🗑️ Remove
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('main')">
                                                            ✏️ Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Interior Photo -->
                                        <div class="col-md-4 mb-3" data-photo-type="interior">
                                            <div class="photo-upload-container">
                                                <div class="photo-upload-box text-center p-4 border border-dashed rounded" id="interiorPhotoBox">
                                                    <i class="fas fa-images mb-2 text-muted"></i>
                                                    <p class="mb-2">🏠 Interior View</p>
                                                    <input type="file" class="form-control-file d-none" accept="image/*" id="interiorPhoto" name="interiorPhoto" data-type="interior">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('interiorPhoto').click()">
                                                        Choose & Edit Photo
                                                    </button>
                                                    <small class="d-block text-muted mt-1">Living spaces & common areas</small>
                                                </div>
                                                <div class="photo-preview d-none" id="interiorPhotoPreview">
                                                    <img src="" alt="Interior Photo" class="img-fluid rounded">
                                                    <div class="photo-overlay">
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('interior')">
                                                            🗑️ Remove
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('interior')">
                                                            ✏️ Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Bedroom Photo -->
                                        <div class="col-md-4 mb-3" data-photo-type="bedroom">
                                            <div class="photo-upload-container">
                                                <div class="photo-upload-box text-center p-4 border border-dashed rounded" id="bedroomPhotoBox">
                                                    <i class="fas fa-bed mb-2 text-muted"></i>
                                                    <p class="mb-2">🛏️ Bedroom</p>
                                                    <input type="file" class="form-control-file d-none" accept="image/*" id="bedroomPhoto" name="bedroomPhoto" data-type="bedroom">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('bedroomPhoto').click()">
                                                        Choose & Edit Photo
                                                    </button>
                                                    <small class="d-block text-muted mt-1">Sleeping accommodations</small>
                                                </div>
                                                <div class="photo-preview d-none" id="bedroomPhotoPreview">
                                                    <img src="" alt="Bedroom Photo" class="img-fluid rounded">
                                                    <div class="photo-overlay">
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('bedroom')">
                                                            🗑️ Remove
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('bedroom')">
                                                            ✏️ Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Kitchen Photo -->
                                        <div class="col-md-4 mb-3" data-photo-type="kitchen">
                                            <div class="photo-upload-container">
                                                <div class="photo-upload-box text-center p-4 border border-dashed rounded" id="kitchenPhotoBox">
                                                    <i class="fas fa-utensils mb-2 text-muted"></i>
                                                    <p class="mb-2">🍳 Kitchen</p>
                                                    <input type="file" class="form-control-file d-none" accept="image/*" id="kitchenPhoto" name="kitchenPhoto" data-type="kitchen">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('kitchenPhoto').click()">
                                                        Choose & Edit Photo
                                                    </button>
                                                    <small class="d-block text-muted mt-1">Cooking & dining facilities</small>
                                                </div>
                                                <div class="photo-preview d-none" id="kitchenPhotoPreview">
                                                    <img src="" alt="Kitchen Photo" class="img-fluid rounded">
                                                    <div class="photo-overlay">
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('kitchen')">
                                                            🗑️ Remove
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('kitchen')">
                                                            ✏️ Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Bathroom Photo -->
                                        <div class="col-md-4 mb-3" data-photo-type="bathroom">
                                            <div class="photo-upload-container">
                                                <div class="photo-upload-box text-center p-4 border border-dashed rounded" id="bathroomPhotoBox">
                                                    <i class="fas fa-bath mb-2 text-muted"></i>
                                                    <p class="mb-2">🚿 Bathroom</p>
                                                    <input type="file" class="form-control-file d-none" accept="image/*" id="bathroomPhoto" name="bathroomPhoto" data-type="bathroom">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="document.getElementById('bathroomPhoto').click()">
                                                        Choose & Edit Photo
                                                    </button>
                                                    <small class="d-block text-muted mt-1">Sanitary facilities</small>
                                                </div>
                                                <div class="photo-preview d-none" id="bathroomPhotoPreview">
                                                    <img src="" alt="Bathroom Photo" class="img-fluid rounded">
                                                    <div class="photo-overlay">
                                                        <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('bathroom')">
                                                            🗑️ Remove
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('bathroom')">
                                                            ✏️ Edit
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Additional Photos -->
                                        <div class="col-md-4 mb-3">
                                            <div class="photo-upload-box text-center p-4 border border-dashed rounded">
                                                <i class="fas fa-plus mb-2 text-muted"></i>
                                                <p class="mb-2">📷 Add More Photos</p>
                                                <input type="file" class="form-control-file d-none" accept="image/*" multiple id="additionalPhotos" name="additionalPhotos" data-type="additional">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('additionalPhotos').click()">
                                                    Choose & Edit Photos
                                                </button>
                                                <small class="d-block text-muted mt-1">Multiple photos with editing</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Additional Photos Preview Area -->
                                    <div id="additionalPhotosPreview" class="row mt-3"></div>
                                </div>
                                <small class="form-text text-muted">
                                    Upload high-quality photos that showcase your property. Photos will be displayed in a professional gallery.
                                </small>
                            </div>

                            <!-- Photo Summary for Form Submission -->
                            <div class="mb-3">
                                <label for="{{ form.image_placeholder.id_for_label }}" class="form-label">Photo Summary</label>
                                <textarea class="form-control" name="image_placeholder"
                                          id="{{ form.image_placeholder.id_for_label }}"
                                          rows="2"
                                          readonly
                                          placeholder="Photo summary will be generated automatically">{{ form.image_placeholder.value|default:'' }}</textarea>
                                <small class="form-text text-muted">Summary of uploaded photos (automatically generated)</small>
                            </div>
                        </div>

                        <!-- Booking Policies & Rules -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2">📋 Booking Policies & Rules</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="smoking_allowed"
                                               id="{{ form.smoking_allowed.id_for_label }}"
                                               {% if form.smoking_allowed.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.smoking_allowed.id_for_label }}">
                                            🚬 Smoking Allowed
                                        </label>
                                        <small class="form-text text-muted d-block">Check if smoking is permitted in the property</small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="same_gender_groups_only"
                                               id="{{ form.same_gender_groups_only.id_for_label }}"
                                               {% if form.same_gender_groups_only.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.same_gender_groups_only.id_for_label }}">
                                            👥 Same Gender Groups Only
                                        </label>
                                        <small class="form-text text-muted d-block">Only accept all-male or all-female groups</small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="national_id_required"
                                               id="{{ form.national_id_required.id_for_label }}"
                                               {% if form.national_id_required.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.national_id_required.id_for_label }}">
                                            🆔 National ID Required
                                        </label>
                                        <small class="form-text text-muted d-block">Guests must present national ID upon check-in</small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="pets_not_allowed"
                                               id="{{ form.pets_not_allowed.id_for_label }}"
                                               {% if form.pets_not_allowed.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.pets_not_allowed.id_for_label }}">
                                            🐕 No Pets Allowed
                                        </label>
                                        <small class="form-text text-muted d-block">Pets are prohibited in the property</small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="events_not_allowed"
                                               id="{{ form.events_not_allowed.id_for_label }}"
                                               {% if form.events_not_allowed.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.events_not_allowed.id_for_label }}">
                                            🎉 No Events/Parties
                                        </label>
                                        <small class="form-text text-muted d-block">Events and parties are prohibited</small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="no_24hour_checkin"
                                               id="{{ form.no_24hour_checkin.id_for_label }}"
                                               {% if form.no_24hour_checkin.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ form.no_24hour_checkin.id_for_label }}">
                                            🕐 No 24-Hour Check-in
                                        </label>
                                        <small class="form-text text-muted d-block">24-hour check-in is not available</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Check-out Time -->
                            <div class="row mt-4">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.check_out_time.id_for_label }}" class="form-label">🕐 Check-out Time *</label>
                                    <select class="form-select" name="check_out_time"
                                            id="{{ form.check_out_time.id_for_label }}" required>
                                        <option value="12:00" {% if form.check_out_time.value == '12:00' %}selected{% endif %}>12:00 PM</option>
                                        <option value="14:00" {% if form.check_out_time.value == '14:00' %}selected{% endif %}>2:00 PM</option>
                                        <option value="16:00" {% if form.check_out_time.value == '16:00' %}selected{% endif %}>4:00 PM</option>
                                    </select>
                                    <small class="form-text text-muted">Required check-out time for all guests</small>
                                </div>
                            </div>

                            <!-- Additional Policies -->
                            <div class="row mt-3">
                                <div class="col-12 mb-3">
                                    <label for="{{ form.additional_policies.id_for_label }}" class="form-label">📝 Additional Policies</label>
                                    <textarea class="form-control" name="additional_policies"
                                              id="{{ form.additional_policies.id_for_label }}"
                                              rows="4"
                                              placeholder="Enter any additional house rules, policies, or special requirements for guests...">{{ form.additional_policies.value|default:'' }}</textarea>
                                    <small class="form-text text-muted">
                                        Examples: Quiet hours (10 PM - 8 AM), No shoes inside, Kitchen usage rules,
                                        Parking instructions, WiFi password policy, etc.
                                    </small>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <small>
                                    <strong>💡 Policy Guidelines:</strong><br>
                                    • Clear policies help set proper guest expectations<br>
                                    • These rules will be displayed to guests before booking<br>
                                    • Ensure your policies comply with local regulations
                                </small>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accommodations:host_accommodations' %}" class="btn btn-secondary">
                                ← Cancel
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                {% if object %}
                                    💾 Update Property
                                {% else %}
                                    🚀 List Property
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tips -->
            <div class="card mt-4 bg-light">
                <div class="card-body">
                    <h6>💡 Tips for a Great Listing</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <h6 class="small">📸 Quality Photos</h6>
                            <p class="small text-muted">High-quality photos are the most important factor for bookings</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">📝 Detailed Description</h6>
                            <p class="small text-muted">Include nearby attractions, transportation, and unique features</p>
                        </div>
                        <div class="col-md-4">
                            <h6 class="small">💰 Competitive Pricing</h6>
                            <p class="small text-muted">Research similar properties in your area for competitive rates</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

.photo-upload-box {
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-upload-box:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd !important;
}

.photo-upload-box i {
    font-size: 2rem;
}

.form-check {
    margin-bottom: 0.5rem;
}

.form-check-label {
    font-size: 0.9rem;
}

.border-dashed {
    border-style: dashed !important;
}

.photo-preview {
    max-width: 100%;
    max-height: 120px;
    object-fit: cover;
    border-radius: 5px;
}
</style>

<!-- Photo Editor Modal -->
<div class="modal fade photo-editor-modal" id="photoEditorModal" tabindex="-1" aria-labelledby="photoEditorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoEditorModalLabel">📸 Edit Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Frame Alignment Guide -->
                <div class="frame-alignment-guide">
                    <strong>📐 Frame Alignment:</strong> Crop and rotate your image to fit the frame perfectly.
                    Ensure the main subject is centered and properly aligned.
                </div>

                <!-- Photo Editor Container -->
                <div class="photo-editor-container">
                    <img id="photoEditorImage" src="" alt="Photo to edit">
                </div>

                <!-- Crop Controls -->
                <div class="crop-controls">
                    <small class="text-muted">Use mouse to drag and resize the crop area</small>
                </div>

                <!-- Photo Title Input -->
                <div class="photo-title-input">
                    <label for="photoTitle" class="form-label">
                        <strong>📝 Enter a title for the image:</strong>
                    </label>
                    <input type="text" class="form-control" id="photoTitle" placeholder="e.g., Spacious living room with mountain view">
                </div>

                <!-- Predefined Tags -->
                <div>
                    <label class="form-label">
                        <strong>🏷️ Select tags for this image:</strong>
                    </label>
                    <div class="tag-chips-container">
                        <!-- Room Types -->
                        <div class="mb-2">
                            <small class="text-muted fw-bold">Room Types:</small><br>
                            <span class="tag-chip" data-tag="Kitchen">🍳 Kitchen</span>
                            <span class="tag-chip" data-tag="Bathroom">🚿 Bathroom</span>
                            <span class="tag-chip" data-tag="Bedroom">🛏️ Bedroom</span>
                            <span class="tag-chip" data-tag="Living Room">🛋️ Living Room</span>
                            <span class="tag-chip" data-tag="Master Room">👑 Master Room</span>
                            <span class="tag-chip" data-tag="Guest Room">🚪 Guest Room</span>
                        </div>

                        <!-- Outdoor & Amenities -->
                        <div class="mb-2">
                            <small class="text-muted fw-bold">Outdoor & Amenities:</small><br>
                            <span class="tag-chip" data-tag="Pool">🏊 Pool</span>
                            <span class="tag-chip" data-tag="Terrace">🌿 Terrace</span>
                            <span class="tag-chip" data-tag="Rooftop">🏢 Rooftop</span>
                            <span class="tag-chip" data-tag="View">🌄 View</span>
                            <span class="tag-chip" data-tag="Parking">🚗 Parking</span>
                            <span class="tag-chip" data-tag="Garden">🌺 Garden</span>
                        </div>

                        <!-- Building Features -->
                        <div class="mb-2">
                            <small class="text-muted fw-bold">Building Features:</small><br>
                            <span class="tag-chip" data-tag="Building View">🏗️ Building View</span>
                            <span class="tag-chip" data-tag="Entrance">🚪 Entrance</span>
                            <span class="tag-chip" data-tag="Corridor">🚶 Corridor</span>
                            <span class="tag-chip" data-tag="Laundry">👕 Laundry</span>
                            <span class="tag-chip" data-tag="Jacuzzi">🛁 Jacuzzi</span>
                            <span class="tag-chip" data-tag="BBQ">🔥 BBQ</span>
                            <span class="tag-chip" data-tag="Reception Area">🏨 Reception Area</span>
                            <span class="tag-chip" data-tag="Billiard Table">🎱 Billiard Table</span>
                        </div>
                    </div>

                    <!-- Custom Tag Input -->
                    <div class="custom-tag-input">
                        <input type="text" class="form-control" id="customTag" placeholder="Add custom tag...">
                        <button type="button" class="btn btn-outline-success btn-sm mt-2" id="addCustomTag">
                            ➕ Add Custom Tag
                        </button>
                    </div>

                    <!-- Selected Tags Display -->
                    <div class="selected-tags-display">
                        <small class="text-muted">Selected tags:</small>
                        <div id="selectedTagsContainer">
                            <em class="text-muted">No tags selected</em>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitPhotoEdit">
                    ✅ Submit Photo
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Photo upload preview functionality
function setupPhotoPreview(inputId) {
    const input = document.getElementById(inputId);
    if (input) {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const uploadBox = input.closest('.photo-upload-box');
                    const existingPreview = uploadBox.querySelector('.photo-preview');
                    if (existingPreview) {
                        existingPreview.remove();
                    }

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'photo-preview mb-2';
                    uploadBox.insertBefore(img, uploadBox.firstChild);

                    // Update button text
                    const button = uploadBox.querySelector('button');
                    button.textContent = 'Change Photo';
                    button.className = 'btn btn-sm btn-success';
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Initialize photo previews when page loads
document.addEventListener('DOMContentLoaded', function() {
    setupPhotoPreview('mainPhoto');
    setupPhotoPreview('interiorPhoto');
    setupPhotoPreview('bedroomPhoto');
    setupPhotoPreview('kitchenPhoto');
    setupPhotoPreview('bathroomPhoto');

    // Handle multiple additional photos
    const additionalPhotos = document.getElementById('additionalPhotos');
    if (additionalPhotos) {
        additionalPhotos.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            const uploadBox = this.closest('.photo-upload-box');

            if (files.length > 0) {
                const button = uploadBox.querySelector('button');
                button.textContent = `${files.length} Photo${files.length > 1 ? 's' : ''} Selected`;
                button.className = 'btn btn-sm btn-success';
            }
        });
    }
});

// Update max capacity based on standard capacity
const standardCapacitySelect = document.getElementById('{{ form.standard_capacity.id_for_label }}');
if (standardCapacitySelect) {
    standardCapacitySelect.addEventListener('change', function() {
        const standardCapacity = parseInt(this.value);
        const maxCapacitySelect = document.getElementById('{{ form.max_capacity.id_for_label }}');

        if (maxCapacitySelect && standardCapacity) {
            // Clear and repopulate max capacity options
            maxCapacitySelect.innerHTML = '<option value="">Same as standard</option>';

            for (let i = standardCapacity; i <= 20; i += 2) {
                if (i > standardCapacity) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = `Up to ${i} guests`;
                    maxCapacitySelect.appendChild(option);
                }
            }
        }
    });
}

// Photo Gallery Functions
let uploadedPhotos = {
    main: null,
    interior: null,
    bedroom: null,
    kitchen: null,
    bathroom: null,
    additional: []
};

function handlePhotoUpload(input, type) {
    const file = input.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file.');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        if (type === 'additional') {
            addAdditionalPhoto(e.target.result, file);
        } else {
            showPhotoPreview(type, e.target.result);
            uploadedPhotos[type] = file;
        }
    };
    reader.readAsDataURL(file);
}

function showPhotoPreview(type, imageSrc) {
    const uploadBox = document.getElementById(`${type}PhotoBox`);
    const previewDiv = document.getElementById(`${type}PhotoPreview`);

    if (uploadBox && previewDiv) {
        uploadBox.classList.add('d-none');
        previewDiv.classList.remove('d-none');
        previewDiv.querySelector('img').src = imageSrc;
        updatePhotoSummary();
    }
}

function removePhoto(type) {
    const uploadBox = document.getElementById(`${type}PhotoBox`);
    const previewDiv = document.getElementById(`${type}PhotoPreview`);
    const input = document.getElementById(`${type}Photo`);

    if (uploadBox && previewDiv && input) {
        uploadBox.classList.remove('d-none');
        previewDiv.classList.add('d-none');
        input.value = '';
        uploadedPhotos[type] = null;
        updatePhotoSummary();
    }
}

function addAdditionalPhoto(imageSrc, file) {
    const container = document.getElementById('additionalPhotosPreview');
    const photoIndex = uploadedPhotos.additional.length;

    const photoDiv = document.createElement('div');
    photoDiv.className = 'col-md-3 mb-3';
    photoDiv.innerHTML = `
        <div class="additional-photo-item">
            <img src="${imageSrc}" alt="Additional Photo" class="img-fluid rounded">
            <div class="photo-overlay">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeAdditionalPhoto(${photoIndex})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="photo-counter">${photoIndex + 1}</div>
        </div>
    `;

    container.appendChild(photoDiv);
    uploadedPhotos.additional.push(file);
    updatePhotoSummary();
}

function removeAdditionalPhoto(index) {
    const container = document.getElementById('additionalPhotosPreview');
    const photoElements = container.children;

    if (photoElements[index]) {
        photoElements[index].remove();
        uploadedPhotos.additional.splice(index, 1);

        // Update photo counters
        Array.from(photoElements).forEach((element, i) => {
            const counter = element.querySelector('.photo-counter');
            if (counter) {
                counter.textContent = i + 1;
            }
        });

        updatePhotoSummary();
    }
}

function updatePhotoSummary() {
    const summaryTextarea = document.getElementById('{{ form.image_placeholder.id_for_label }}');
    if (!summaryTextarea) return;

    const photoTypes = [];

    // Check which photo types have been uploaded
    Object.keys(uploadedPhotos).forEach(type => {
        if (type === 'additional') {
            if (uploadedPhotos[type].length > 0) {
                photoTypes.push(`${uploadedPhotos[type].length} additional photos`);
            }
        } else if (uploadedPhotos[type]) {
            photoTypes.push(`${type} photo`);
        }
    });

    if (photoTypes.length > 0) {
        summaryTextarea.value = `Professional photo gallery with: ${photoTypes.join(', ')}. High-quality images showcasing the property's best features.`;
    } else {
        summaryTextarea.value = 'Photo gallery will be available once photos are uploaded.';
    }
}

// Collect amenities from checkboxes and update the main amenities field
function updateAmenitiesField() {
    const amenitiesTextarea = document.getElementById('{{ form.amenities.id_for_label }}');
    if (!amenitiesTextarea) return;

    const selectedAmenities = [];

    // Collect checked amenities from all categories
    const amenityCheckboxes = document.querySelectorAll('input[name^="amenities_"]:checked');
    amenityCheckboxes.forEach(checkbox => {
        const label = document.querySelector(`label[for="${checkbox.id}"]`);
        if (label) {
            // Extract text without emoji
            const amenityText = label.textContent.replace(/[^\w\s]/gi, '').trim();
            selectedAmenities.push(amenityText);
        }
    });

    // Get existing additional amenities
    const additionalAmenities = amenitiesTextarea.value.trim();
    if (additionalAmenities) {
        selectedAmenities.push(additionalAmenities);
    }

    // Update the hidden amenities field with comma-separated values
    const allAmenities = selectedAmenities.join(', ');

    // Create or update hidden field for form submission
    let hiddenAmenitiesField = document.getElementById('hidden_amenities_field');
    if (!hiddenAmenitiesField) {
        hiddenAmenitiesField = document.createElement('input');
        hiddenAmenitiesField.type = 'hidden';
        hiddenAmenitiesField.name = 'amenities';
        hiddenAmenitiesField.id = 'hidden_amenities_field';
        document.querySelector('form').appendChild(hiddenAmenitiesField);
    }
    hiddenAmenitiesField.value = allAmenities;
}

// Add event listeners to all amenity checkboxes and photo inputs
document.addEventListener('DOMContentLoaded', function() {
    // Amenity checkboxes
    const amenityCheckboxes = document.querySelectorAll('input[name^="amenities_"]');
    amenityCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateAmenitiesField);
    });

    // Also update when additional amenities text changes
    const amenitiesTextarea = document.getElementById('{{ form.amenities.id_for_label }}');
    if (amenitiesTextarea) {
        amenitiesTextarea.addEventListener('input', updateAmenitiesField);
    }

    // Photo upload inputs
    const photoInputs = ['mainPhoto', 'interiorPhoto', 'bedroomPhoto', 'kitchenPhoto', 'bathroomPhoto'];
    photoInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('change', function() {
                const type = this.getAttribute('data-type');
                handlePhotoUpload(this, type);
            });
        }
    });

    // Additional photos input
    const additionalPhotosInput = document.getElementById('additionalPhotos');
    if (additionalPhotosInput) {
        additionalPhotosInput.addEventListener('change', function() {
            Array.from(this.files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    addAdditionalPhoto(e.target.result, file);
                };
                reader.readAsDataURL(file);
            });
            this.value = ''; // Reset input to allow same files again
        });
    }

    // Initial update
    updateAmenitiesField();

    // Populate amenities checkboxes from existing data (for edit mode)
    populateAmenitiesFromField();
});

// Function to populate checkboxes from the amenities field (for edit mode)
function populateAmenitiesFromField() {
    const amenitiesTextarea = document.getElementById('{{ form.amenities.id_for_label }}');
    if (!amenitiesTextarea || !amenitiesTextarea.value) return;

    const amenitiesText = amenitiesTextarea.value.toLowerCase();

    // Check amenities based on text content
    const amenityMappings = {
        'wifi': 'wifi',
        'parking': 'parking',
        'air conditioning': 'air_conditioning',
        'heating': 'heating',
        'kitchen': 'kitchen',
        'refrigerator': 'refrigerator',
        'stove': 'stove',
        'microwave': 'microwave',
        'dishwasher': 'dishwasher',
        'washing machine': 'washing_machine',
        'dryer': 'dryer',
        'iron': 'iron',
        'tv': 'tv',
        'cable': 'cable_tv',
        'streaming': 'streaming',
        'sound system': 'sound_system',
        'pool': 'pool',
        'hot tub': 'hot_tub',
        'gym': 'gym',
        'garden': 'garden',
        'balcony': 'balcony',
        'terrace': 'terrace',
        'bbq': 'bbq_grill',
        'fireplace': 'fireplace'
    };

    // Check each amenity
    Object.entries(amenityMappings).forEach(([text, id]) => {
        if (amenitiesText.includes(text)) {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.checked = true;
            }
        }
    });
}

// Photo Editor Functionality
let cropper = null;
let currentPhotoType = null;
let currentPhotoFile = null;
let selectedTags = [];

// Initialize photo editor when modal is shown
document.getElementById('photoEditorModal').addEventListener('shown.bs.modal', function() {
    if (currentPhotoFile) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const image = document.getElementById('photoEditorImage');
            image.src = e.target.result;

            // Initialize cropper
            if (cropper) {
                cropper.destroy();
            }

            cropper = new Cropper(image, {
                aspectRatio: 16 / 9, // Default aspect ratio
                viewMode: 1,
                autoCropArea: 0.8,
                responsive: true,
                restore: false,
                guides: true,
                center: true,
                highlight: false,
                cropBoxMovable: true,
                cropBoxResizable: true,
                toggleDragModeOnDblclick: false,
            });
        };
        reader.readAsDataURL(currentPhotoFile);
    }
});

// Clean up cropper when modal is hidden
document.getElementById('photoEditorModal').addEventListener('hidden.bs.modal', function() {
    if (cropper) {
        cropper.destroy();
        cropper = null;
    }
    resetPhotoEditor();
});

// Simplified crop controls - just drag and resize

// Tag Selection
document.querySelectorAll('.tag-chip').forEach(chip => {
    chip.addEventListener('click', function() {
        const tag = this.dataset.tag;
        toggleTag(tag);
        this.classList.toggle('selected');
        updateSelectedTagsDisplay();
    });
});

// Custom Tag Addition
document.getElementById('addCustomTag').addEventListener('click', function() {
    const customTagInput = document.getElementById('customTag');
    const tag = customTagInput.value.trim();

    if (tag && !selectedTags.includes(tag)) {
        selectedTags.push(tag);
        customTagInput.value = '';
        updateSelectedTagsDisplay();
    }
});

// Allow Enter key for custom tags
document.getElementById('customTag').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('addCustomTag').click();
    }
});

// Submit Photo Edit
document.getElementById('submitPhotoEdit').addEventListener('click', function() {
    const title = document.getElementById('photoTitle').value.trim();

    if (cropper) {
        // Get cropped canvas
        const canvas = cropper.getCroppedCanvas({
            width: 800,
            height: 450,
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high',
        });

        // Convert to blob and process
        canvas.toBlob(function(blob) {
            // Create new file with cropped image
            const fileName = currentPhotoFile.name;
            const croppedFile = new File([blob], fileName, {
                type: currentPhotoFile.type,
                lastModified: Date.now()
            });

            // Process the photo with title and tags
            processEditedPhoto(croppedFile, title, selectedTags.slice(), currentPhotoType);

        }, currentPhotoFile.type, 0.9);
    } else {
        // No cropper, use original file
        processEditedPhoto(currentPhotoFile, title, selectedTags.slice(), currentPhotoType);
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('photoEditorModal'));
    modal.hide();
});

function openPhotoEditor(file, type) {
    currentPhotoFile = file;
    currentPhotoType = type;

    // Reset editor state
    resetPhotoEditor();

    // Set suggested title based on type
    const titleInput = document.getElementById('photoTitle');
    const suggestedTitles = {
        'main': 'Main property view',
        'interior': 'Interior living space',
        'bedroom': 'Comfortable bedroom',
        'kitchen': 'Fully equipped kitchen',
        'bathroom': 'Modern bathroom'
    };
    titleInput.value = suggestedTitles[type] || '';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('photoEditorModal'));
    modal.show();
}

function resetPhotoEditor() {
    selectedTags = [];
    document.getElementById('photoTitle').value = '';
    document.getElementById('customTag').value = '';

    // Reset tag selections
    document.querySelectorAll('.tag-chip').forEach(chip => {
        chip.classList.remove('selected');
    });

    updateSelectedTagsDisplay();
}

function toggleTag(tag) {
    const index = selectedTags.indexOf(tag);
    if (index > -1) {
        selectedTags.splice(index, 1);
    } else {
        selectedTags.push(tag);
    }
}

function updateSelectedTagsDisplay() {
    const container = document.getElementById('selectedTagsContainer');

    if (selectedTags.length === 0) {
        container.innerHTML = '<em class="text-muted">No tags selected</em>';
    } else {
        container.innerHTML = selectedTags.map(tag =>
            `<span class="selected-tag">${tag}</span>`
        ).join(' ');
    }
}

function processEditedPhoto(file, title, tags, type) {
    // Update the photo preview with the edited image
    const reader = new FileReader();
    reader.onload = function(e) {
        if (type === 'additional') {
            addAdditionalPhoto(e.target.result, file, title, tags);
        } else {
            // Update single photo preview
            const container = document.querySelector(`[data-photo-type="${type}"]`);
            if (container) {
                const uploadBox = container.querySelector('.photo-upload-box');
                const preview = container.querySelector('.photo-preview');

                if (preview) {
                    // Update existing preview
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="${title || type}" class="img-fluid rounded">
                        <div class="photo-overlay">
                            <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('${type}')">
                                🗑️ Remove
                            </button>
                            <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('${type}')">
                                ✏️ Edit
                            </button>
                        </div>
                        ${title ? `<div class="photo-title small mt-1">${title}</div>` : ''}
                        ${tags.length > 0 ? `<div class="photo-tags small text-muted">${tags.join(', ')}</div>` : ''}
                    `;
                    preview.classList.remove('d-none');
                    uploadBox.classList.add('d-none');
                } else {
                    // Create new preview
                    const newPreview = document.createElement('div');
                    newPreview.className = 'photo-preview';
                    newPreview.innerHTML = `
                        <img src="${e.target.result}" alt="${title || type}" class="img-fluid rounded">
                        <div class="photo-overlay">
                            <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('${type}')">
                                🗑️ Remove
                            </button>
                            <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('${type}')">
                                ✏️ Edit
                            </button>
                        </div>
                        ${title ? `<div class="photo-title small mt-1">${title}</div>` : ''}
                        ${tags.length > 0 ? `<div class="photo-tags small text-muted">${tags.join(', ')}</div>` : ''}
                    `;
                    container.querySelector('.photo-upload-container').appendChild(newPreview);
                    uploadBox.classList.add('d-none');
                }

                // Store photo data
                uploadedPhotos[type] = {
                    file: file,
                    title: title,
                    tags: tags,
                    preview: e.target.result
                };
            }
        }

        updatePhotoSummary();
    };
    reader.readAsDataURL(file);
}

// Update the existing handlePhotoUpload function to use the editor
function handlePhotoUpload(input, type) {
    const file = input.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file.');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB.');
        return;
    }

    // Open photo editor instead of direct preview
    openPhotoEditor(file, type);

    // Reset input
    input.value = '';
}

// Function to edit existing photos
function editPhoto(type) {
    const photoData = uploadedPhotos[type];
    if (photoData && photoData.file) {
        openPhotoEditor(photoData.file, type);
    }
}

// Enhanced additional photos handling
function addAdditionalPhoto(imageSrc, file, title = '', tags = []) {
    const previewArea = document.getElementById('additionalPhotosPreview');
    const photoIndex = uploadedPhotos.additional.length;

    const photoDiv = document.createElement('div');
    photoDiv.className = 'col-md-3 mb-3';
    photoDiv.innerHTML = `
        <div class="photo-preview">
            <img src="${imageSrc}" alt="${title || 'Additional Photo'}" class="img-fluid rounded">
            <div class="photo-overlay">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeAdditionalPhoto(${photoIndex})">
                    🗑️ Remove
                </button>
                <button type="button" class="btn btn-sm btn-info" onclick="editAdditionalPhoto(${photoIndex})">
                    ✏️ Edit
                </button>
            </div>
            <div class="photo-counter">${photoIndex + 1}</div>
            ${title ? `<div class="photo-title small mt-1">${title}</div>` : ''}
            ${tags.length > 0 ? `<div class="photo-tags small text-muted">${tags.join(', ')}</div>` : ''}
        </div>
    `;

    previewArea.appendChild(photoDiv);

    // Store photo data
    uploadedPhotos.additional.push({
        file: file,
        title: title,
        tags: tags,
        preview: imageSrc,
        element: photoDiv
    });
}

function removeAdditionalPhoto(index) {
    if (uploadedPhotos.additional[index]) {
        uploadedPhotos.additional[index].element.remove();
        uploadedPhotos.additional.splice(index, 1);

        // Update indices for remaining photos
        updateAdditionalPhotoIndices();
        updatePhotoSummary();
    }
}

function editAdditionalPhoto(index) {
    const photoData = uploadedPhotos.additional[index];
    if (photoData && photoData.file) {
        currentAdditionalPhotoIndex = index;
        openPhotoEditor(photoData.file, 'additional');
    }
}

function updateAdditionalPhotoIndices() {
    uploadedPhotos.additional.forEach((photo, index) => {
        const counter = photo.element.querySelector('.photo-counter');
        if (counter) {
            counter.textContent = index + 1;
        }

        // Update button onclick handlers
        const removeBtn = photo.element.querySelector('.btn-danger');
        const editBtn = photo.element.querySelector('.btn-info');
        if (removeBtn) removeBtn.setAttribute('onclick', `removeAdditionalPhoto(${index})`);
        if (editBtn) editBtn.setAttribute('onclick', `editAdditionalPhoto(${index})`);
    });
}

let currentAdditionalPhotoIndex = null;

// Update processEditedPhoto to handle additional photo editing
function processEditedPhotoOriginal(file, title, tags, type) {
    // Update the photo preview with the edited image
    const reader = new FileReader();
    reader.onload = function(e) {
        if (type === 'additional') {
            if (currentAdditionalPhotoIndex !== null) {
                // Update existing additional photo
                const photoData = uploadedPhotos.additional[currentAdditionalPhotoIndex];
                if (photoData) {
                    photoData.file = file;
                    photoData.title = title;
                    photoData.tags = tags;
                    photoData.preview = e.target.result;

                    // Update the display
                    const img = photoData.element.querySelector('img');
                    const titleDiv = photoData.element.querySelector('.photo-title');
                    const tagsDiv = photoData.element.querySelector('.photo-tags');

                    if (img) img.src = e.target.result;
                    if (img) img.alt = title || 'Additional Photo';

                    if (title) {
                        if (titleDiv) {
                            titleDiv.textContent = title;
                        } else {
                            const newTitleDiv = document.createElement('div');
                            newTitleDiv.className = 'photo-title small mt-1';
                            newTitleDiv.textContent = title;
                            photoData.element.querySelector('.photo-preview').appendChild(newTitleDiv);
                        }
                    } else if (titleDiv) {
                        titleDiv.remove();
                    }

                    if (tags.length > 0) {
                        if (tagsDiv) {
                            tagsDiv.textContent = tags.join(', ');
                        } else {
                            const newTagsDiv = document.createElement('div');
                            newTagsDiv.className = 'photo-tags small text-muted';
                            newTagsDiv.textContent = tags.join(', ');
                            photoData.element.querySelector('.photo-preview').appendChild(newTagsDiv);
                        }
                    } else if (tagsDiv) {
                        tagsDiv.remove();
                    }
                }
                currentAdditionalPhotoIndex = null;
            } else {
                // Add new additional photo
                addAdditionalPhoto(e.target.result, file, title, tags);
            }
        } else {
            // Handle single photo types (main, interior, bedroom, kitchen, bathroom)
            const container = document.querySelector(`[data-photo-type="${type}"]`);
            if (container) {
                const preview = container.querySelector('.photo-preview');
                if (preview) {
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="${title || type}">
                        <div class="photo-overlay">
                            <button type="button" class="btn btn-sm btn-danger" onclick="removePhoto('${type}')">
                                🗑️ Remove
                            </button>
                            <button type="button" class="btn btn-sm btn-info" onclick="editPhoto('${type}')">
                                ✏️ Edit
                            </button>
                        </div>
                        ${title ? `<div class="photo-title small mt-1">${title}</div>` : ''}
                        ${tags.length > 0 ? `<div class="photo-tags small text-muted">${tags.join(', ')}</div>` : ''}
                    `;
                    preview.classList.remove('d-none');
                    container.querySelector('.photo-upload-box').classList.add('d-none');
                }

                // Store photo data
                uploadedPhotos[type] = {
                    file: file,
                    title: title,
                    tags: tags,
                    preview: e.target.result
                };
            }
        }

        updatePhotoSummary();
    };
    reader.readAsDataURL(file);
}

// Remove photo function
function removePhoto(type) {
    const container = document.querySelector(`[data-photo-type="${type}"]`);
    if (container) {
        const preview = container.querySelector('.photo-preview');
        const uploadBox = container.querySelector('.photo-upload-box');

        if (preview) {
            preview.remove();
        }
        if (uploadBox) {
            uploadBox.classList.remove('d-none');
        }

        // Clear stored data
        uploadedPhotos[type] = null;

        // Reset file input
        const input = container.querySelector('input[type="file"]');
        if (input) {
            input.value = '';
        }

        updatePhotoSummary();
    }
}

// Pricing and Capacity Management JavaScript
let pricingRuleCounter = 0;
let availabilityRuleCounter = 0;

// Update capacity calculator
function updateCapacityCalculator() {
    const standardCapacity = parseInt(document.querySelector('input[name="standard_capacity"]').value) || 0;
    const maxCapacity = parseInt(document.querySelector('input[name="max_capacity"]').value) || 0;
    const extraCapacity = Math.max(0, maxCapacity - standardCapacity);

    document.getElementById('baseCapacityText').textContent = `Base: ${standardCapacity} guests`;
    document.getElementById('extraCapacityText').textContent = `Extra: ${extraCapacity} guests`;
    document.getElementById('totalCapacityText').textContent = `Total: ${maxCapacity} guests`;
}

// Add event listeners for capacity inputs
document.addEventListener('DOMContentLoaded', function() {
    const standardCapacityInput = document.querySelector('input[name="standard_capacity"]');
    const maxCapacityInput = document.querySelector('input[name="max_capacity"]');

    if (standardCapacityInput) {
        standardCapacityInput.addEventListener('input', updateCapacityCalculator);
    }
    if (maxCapacityInput) {
        maxCapacityInput.addEventListener('input', updateCapacityCalculator);
    }

    // Initialize calculator
    updateCapacityCalculator();
});

// Toggle age-specific pricing
function toggleAgeSpecificPricing() {
    const ageSpecificSection = document.getElementById('ageSpecificPricing');
    if (ageSpecificSection.style.display === 'none') {
        ageSpecificSection.style.display = 'block';
    } else {
        ageSpecificSection.style.display = 'none';
    }
}

// Add pricing rule
function addPricingRule(type = 'custom', startDate = '', endDate = '', price = '', description = '') {
    pricingRuleCounter++;
    const container = document.getElementById('pricingRulesContainer');

    const ruleHtml = `
        <div class="pricing-rule border rounded p-3 mb-3" id="pricingRule${pricingRuleCounter}">
            <div class="row">
                <div class="col-md-2">
                    <label class="form-label">Type</label>
                    <select class="form-select" name="pricing_type_${pricingRuleCounter}">
                        <option value="custom" ${type === 'custom' ? 'selected' : ''}>Custom</option>
                        <option value="weekday" ${type === 'weekday' ? 'selected' : ''}>Weekday</option>
                        <option value="weekend" ${type === 'weekend' ? 'selected' : ''}>Weekend</option>
                        <option value="holiday" ${type === 'holiday' ? 'selected' : ''}>Holiday</option>
                        <option value="peak_season" ${type === 'peak_season' ? 'selected' : ''}>Peak Season</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Start Date</label>
                    <input type="date" class="form-control" name="pricing_start_${pricingRuleCounter}" value="${startDate}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">End Date</label>
                    <input type="date" class="form-control" name="pricing_end_${pricingRuleCounter}" value="${endDate}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Price (IRR)</label>
                    <input type="number" class="form-control" name="pricing_price_${pricingRuleCounter}"
                           value="${price}" min="0" step="1000" placeholder="e.g., 2000000">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Description</label>
                    <input type="text" class="form-control" name="pricing_desc_${pricingRuleCounter}"
                           value="${description}" placeholder="e.g., Nowruz holidays">
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger btn-sm d-block"
                            onclick="removePricingRule(${pricingRuleCounter})">
                        🗑️
                    </button>
                </div>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', ruleHtml);
}

// Remove pricing rule
function removePricingRule(ruleId) {
    const rule = document.getElementById(`pricingRule${ruleId}`);
    if (rule) {
        rule.remove();
    }
}

// Quick pricing actions
function addWeekendPricing() {
    const basePrice = parseInt(document.querySelector('input[name="price_per_night"]').value) || 0;
    const weekendPrice = Math.round(basePrice * 1.3); // 30% increase for weekends
    addPricingRule('weekend', '', '', weekendPrice, 'Weekend pricing');
}

function addHolidayPricing() {
    const basePrice = parseInt(document.querySelector('input[name="price_per_night"]').value) || 0;
    const holidayPrice = Math.round(basePrice * 1.5); // 50% increase for holidays
    addPricingRule('holiday', '', '', holidayPrice, 'Public holiday pricing');
}

function addPeakSeasonPricing() {
    const basePrice = parseInt(document.querySelector('input[name="price_per_night"]').value) || 0;
    const peakPrice = Math.round(basePrice * 1.8); // 80% increase for peak season
    addPricingRule('peak_season', '', '', peakPrice, 'Peak season pricing');
}

// Add pricing rule button event
document.addEventListener('DOMContentLoaded', function() {
    const addPricingBtn = document.getElementById('addPricingRule');
    if (addPricingBtn) {
        addPricingBtn.addEventListener('click', function() {
            addPricingRule();
        });
    }

    // Load existing pricing rules
    loadExistingPricingRules();

    // Load existing capacity rules
    loadExistingCapacityRules();

    // Load existing photos
    loadExistingPhotos();
});

// Load existing pricing rules from backend
function loadExistingPricingRules() {
    {% if pricing_rules %}
        console.log('🔍 DEBUG: Loading existing pricing rules');
        {% for rule in pricing_rules %}
            addPricingRule(
                '{{ rule.pricing_type }}',
                '{{ rule.start_date|date:"Y-m-d" }}',
                '{{ rule.end_date|date:"Y-m-d" }}',
                '{{ rule.price_per_night }}',
                '{{ rule.description|escapejs }}'
            );
            console.log('🔍 DEBUG: Loaded pricing rule: {{ rule.pricing_type }} from {{ rule.start_date }} to {{ rule.end_date }}');
        {% endfor %}
    {% endif %}
}

// Load existing capacity rules from backend
function loadExistingCapacityRules() {
    {% if capacity_rules %}
        console.log('🔍 DEBUG: Loading existing capacity rules');
        {% for rule in capacity_rules %}
            const categoryInput = document.getElementById('{{ rule.age_category }}Fee');
            const categoryCheckbox = document.getElementById('{{ rule.age_category }}sCountCapacity');

            if (categoryInput) {
                categoryInput.value = {{ rule.price_per_night }};
            }
            if (categoryCheckbox) {
                categoryCheckbox.checked = {{ rule.counts_in_capacity|yesno:"true,false" }};
            }
            console.log('🔍 DEBUG: Loaded capacity rule: {{ rule.age_category }} at {{ rule.price_per_night }} IRR');
        {% endfor %}
    {% endif %}
}

// Load existing photos from backend
function loadExistingPhotos() {
    {% if photos %}
        console.log('🔍 DEBUG: Loading existing photos');

        {% if photos.main %}
            loadExistingPhoto('main', '{{ photos.main.image.url }}', '{{ photos.main.caption|default:"Main Photo" }}');
        {% endif %}

        {% if photos.interior %}
            loadExistingPhoto('interior', '{{ photos.interior.image.url }}', '{{ photos.interior.caption|default:"Interior View" }}');
        {% endif %}

        {% if photos.bedroom %}
            loadExistingPhoto('bedroom', '{{ photos.bedroom.image.url }}', '{{ photos.bedroom.caption|default:"Bedroom" }}');
        {% endif %}

        {% if photos.kitchen %}
            loadExistingPhoto('kitchen', '{{ photos.kitchen.image.url }}', '{{ photos.kitchen.caption|default:"Kitchen" }}');
        {% endif %}

        {% if photos.bathroom %}
            loadExistingPhoto('bathroom', '{{ photos.bathroom.image.url }}', '{{ photos.bathroom.caption|default:"Bathroom" }}');
        {% endif %}

        {% if photos.additional %}
            {% for photo in photos.additional %}
                loadExistingAdditionalPhoto('{{ photo.image.url }}', '{{ photo.caption|default:"Additional Photo" }}');
            {% endfor %}
        {% endif %}

        console.log('🔍 DEBUG: Finished loading existing photos');
    {% endif %}
}

// Helper function to load an existing photo
function loadExistingPhoto(type, imageUrl, caption) {
    const photoBox = document.getElementById(`${type}PhotoBox`);
    const photoPreview = document.getElementById(`${type}PhotoPreview`);

    if (photoBox && photoPreview) {
        // Hide upload box and show preview
        photoBox.style.display = 'none';
        photoPreview.style.display = 'block';

        // Set image source
        const img = photoPreview.querySelector('img');
        if (img) {
            img.src = imageUrl;
            img.alt = caption;
        }

        // Mark as uploaded in our tracking object
        uploadedPhotos[type] = {
            url: imageUrl,
            caption: caption,
            existing: true
        };

        console.log(`🔍 DEBUG: Loaded existing ${type} photo: ${imageUrl}`);
    }
}

// Helper function to load existing additional photos
function loadExistingAdditionalPhoto(imageUrl, caption) {
    const additionalPhotosPreview = document.getElementById('additionalPhotosPreview');
    if (additionalPhotosPreview) {
        const photoIndex = uploadedPhotos.additional.length;

        const photoDiv = document.createElement('div');
        photoDiv.className = 'col-md-3 mb-3';
        photoDiv.innerHTML = `
            <div class="photo-upload-container">
                <div class="photo-preview">
                    <img src="${imageUrl}" alt="${caption}" class="img-fluid rounded">
                    <div class="photo-overlay">
                        <button type="button" class="btn btn-sm btn-danger" onclick="removeAdditionalPhoto(${photoIndex})">
                            🗑️ Remove
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="editAdditionalPhoto(${photoIndex})">
                            ✏️ Edit
                        </button>
                    </div>
                </div>
            </div>
        `;

        additionalPhotosPreview.appendChild(photoDiv);

        // Add to tracking object
        uploadedPhotos.additional.push({
            url: imageUrl,
            caption: caption,
            existing: true
        });

        console.log(`🔍 DEBUG: Loaded existing additional photo: ${imageUrl}`);
    }
}
</script>

<!-- Cropper.js JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>

{% endblock %}
