<!-- Dynamic Booking Widget with Pricing and Capacity Management -->
<div class="booking-widget card shadow-sm">
    <div class="card-header">
        <h5 class="mb-0">📅 Book Your Stay</h5>
    </div>
    <div class="card-body">
        <!-- Base Price Display -->
        <div class="price-display mb-3">
            <div class="d-flex align-items-baseline">
                <span class="h4 mb-0" id="displayPrice">{{ base_price|floatformat:0 }}</span>
                <span class="text-muted ms-1">IRR / night</span>
            </div>
            <small class="text-muted">Base price for {{ standard_capacity }} guests</small>
        </div>

        <!-- Booking Form -->
        <form id="bookingForm" method="post" action="{% url 'bookings:create' accommodation.id %}">
            {% csrf_token %}
            
            <!-- Date Selection -->
            <div class="row mb-3">
                <div class="col-6">
                    <label for="checkIn" class="form-label">Check-in</label>
                    <input type="date" class="form-control" id="checkIn" name="check_in" required>
                </div>
                <div class="col-6">
                    <label for="checkOut" class="form-label">Check-out</label>
                    <input type="date" class="form-control" id="checkOut" name="check_out" required>
                </div>
            </div>

            <!-- Check-in Time Selection -->
            <div class="mb-3">
                <label for="checkInTime" class="form-label">Check-in Time</label>
                <select class="form-select" id="checkInTime" name="check_in_time" required>
                    <option value="12:00">12:00 PM</option>
                    <option value="14:00" selected>2:00 PM</option>
                    <option value="16:00">4:00 PM</option>
                </select>
                <small class="text-muted">Available check-in times</small>
            </div>

            <!-- Guest Capacity Selection -->
            <div class="mb-3">
                <label class="form-label">Guests</label>
                
                <!-- Quick Guest Selector -->
                <div class="guest-quick-selector mb-2">
                    <button type="button" class="btn btn-outline-secondary w-100" id="guestSelectorBtn" data-bs-toggle="collapse" data-bs-target="#guestDetails">
                        <span id="guestSummary">1 guest</span>
                        <i class="fas fa-chevron-down float-end mt-1"></i>
                    </button>
                </div>

                <!-- Detailed Guest Selection -->
                <div class="collapse" id="guestDetails">
                    <div class="card card-body">
                        <!-- Adults -->
                        <div class="guest-category d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-bold">Adults</div>
                                <small class="text-muted">18+ years</small>
                            </div>
                            <div class="guest-counter">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('adults', -1)">-</button>
                                <span class="mx-2" id="adultsCount">1</span>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('adults', 1)">+</button>
                            </div>
                        </div>

                        <!-- Teens -->
                        <div class="guest-category d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-bold">Teens</div>
                                <small class="text-muted">13-17 years</small>
                            </div>
                            <div class="guest-counter">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('teens', -1)">-</button>
                                <span class="mx-2" id="teensCount">0</span>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('teens', 1)">+</button>
                            </div>
                        </div>

                        <!-- Children -->
                        <div class="guest-category d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-bold">Children</div>
                                <small class="text-muted">3-12 years</small>
                            </div>
                            <div class="guest-counter">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('children', -1)">-</button>
                                <span class="mx-2" id="childrenCount">0</span>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('children', 1)">+</button>
                            </div>
                        </div>

                        <!-- Infants -->
                        <div class="guest-category d-flex justify-content-between align-items-center">
                            <div>
                                <div class="fw-bold">Infants</div>
                                <small class="text-muted">0-2 years</small>
                            </div>
                            <div class="guest-counter">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('infants', -1)">-</button>
                                <span class="mx-2" id="infantsCount">0</span>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeGuestCount('infants', 1)">+</button>
                            </div>
                        </div>

                        <!-- Capacity Warning -->
                        <div class="alert alert-warning mt-3 d-none" id="capacityWarning">
                            <small>Maximum capacity is {{ max_capacity }} guests</small>
                        </div>
                    </div>
                </div>

                <!-- Hidden inputs for guest counts -->
                <input type="hidden" name="adults" id="adultsInput" value="1">
                <input type="hidden" name="teens" id="teensInput" value="0">
                <input type="hidden" name="children" id="childrenInput" value="0">
                <input type="hidden" name="infants" id="infantsInput" value="0">
            </div>

            <!-- Price Breakdown -->
            <div class="price-breakdown mb-3" id="priceBreakdown" style="display: none;">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Price Breakdown</h6>
                        <div id="priceDetails">
                            <!-- Dynamic price details will be inserted here -->
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Total</span>
                            <span id="totalPrice">0 IRR</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Button -->
            <div class="d-grid">
                {% if accommodation.instant_booking %}
                    <button type="submit" class="btn btn-primary btn-lg" id="bookingBtn" disabled>
                        ⚡ Book Instantly
                    </button>
                {% else %}
                    <button type="submit" class="btn btn-outline-primary btn-lg" id="bookingBtn" disabled>
                        📝 Request to Book
                    </button>
                {% endif %}
            </div>

            <!-- Booking Notes -->
            <div class="booking-notes mt-3">
                <small class="text-muted">
                    {% if accommodation.instant_booking %}
                        ⚡ Instant booking - You'll be charged immediately
                    {% else %}
                        📝 Host approval required - You won't be charged until confirmed
                    {% endif %}
                </small>
            </div>
        </form>
    </div>
</div>

<!-- Pricing Calendar Modal -->
<div class="modal fade" id="pricingCalendarModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">📅 Pricing Calendar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="pricingCalendar">
                    <!-- Calendar will be generated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Booking widget JavaScript
const accommodationData = {
    id: {{ accommodation.id }},
    basePrice: {{ base_price }},
    extraGuestFee: {{ extra_guest_fee }},
    standardCapacity: {{ standard_capacity }},
    maxCapacity: {{ max_capacity }},
    pricingCalendar: {{ pricing_calendar|safe }},
    capacityRules: {{ capacity_rules|safe }},
    instantBooking: {{ accommodation.instant_booking|yesno:"true,false" }}
};

// Guest counts
let guestCounts = {
    adults: 1,
    teens: 0,
    children: 0,
    infants: 0
};

// Initialize booking widget
document.addEventListener('DOMContentLoaded', function() {
    initializeDatePickers();
    updateGuestSummary();
    updateBookingButton();
});

// Initialize date pickers with restrictions
function initializeDatePickers() {
    const today = new Date().toISOString().split('T')[0];
    const checkInInput = document.getElementById('checkIn');
    const checkOutInput = document.getElementById('checkOut');
    
    checkInInput.min = today;
    checkOutInput.min = today;
    
    checkInInput.addEventListener('change', function() {
        const checkInDate = new Date(this.value);
        const nextDay = new Date(checkInDate);
        nextDay.setDate(nextDay.getDate() + 1);
        
        checkOutInput.min = nextDay.toISOString().split('T')[0];
        if (checkOutInput.value && new Date(checkOutInput.value) <= checkInDate) {
            checkOutInput.value = nextDay.toISOString().split('T')[0];
        }
        
        calculatePricing();
    });
    
    checkOutInput.addEventListener('change', calculatePricing);
}

// Change guest count
function changeGuestCount(category, change) {
    const newCount = Math.max(0, guestCounts[category] + change);
    
    // Validate capacity
    if (category === 'adults' && newCount === 0) {
        return; // At least 1 adult required
    }
    
    const totalGuests = getTotalGuests();
    if (change > 0 && totalGuests >= accommodationData.maxCapacity) {
        showCapacityWarning();
        return;
    }
    
    guestCounts[category] = newCount;
    document.getElementById(category + 'Count').textContent = newCount;
    document.getElementById(category + 'Input').value = newCount;
    
    updateGuestSummary();
    calculatePricing();
    hideCapacityWarning();
}

// Get total guest count
function getTotalGuests() {
    return guestCounts.adults + guestCounts.teens + guestCounts.children + guestCounts.infants;
}

// Update guest summary
function updateGuestSummary() {
    const total = getTotalGuests();
    const summary = total === 1 ? '1 guest' : `${total} guests`;
    document.getElementById('guestSummary').textContent = summary;
}

// Show/hide capacity warning
function showCapacityWarning() {
    document.getElementById('capacityWarning').classList.remove('d-none');
}

function hideCapacityWarning() {
    document.getElementById('capacityWarning').classList.add('d-none');
}

// Calculate pricing
function calculatePricing() {
    const checkIn = document.getElementById('checkIn').value;
    const checkOut = document.getElementById('checkOut').value;

    if (!checkIn || !checkOut) {
        document.getElementById('priceBreakdown').style.display = 'none';
        updateBookingButton();
        return;
    }

    const startDate = new Date(checkIn);
    const endDate = new Date(checkOut);
    const nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

    if (nights <= 0) {
        document.getElementById('priceBreakdown').style.display = 'none';
        updateBookingButton();
        return;
    }

    let totalCost = 0;
    let priceDetails = '';

    console.log('🔍 DEBUG: Calculating pricing for', nights, 'nights');
    console.log('🔍 DEBUG: Guest counts:', guestCounts);
    console.log('🔍 DEBUG: Pricing calendar:', accommodationData.pricingCalendar);

    // Calculate base accommodation cost with dynamic pricing
    let accommodationCost = 0;
    let priceBreakdownByDate = [];

    for (let i = 0; i < nights; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(currentDate.getDate() + i);
        const dateStr = currentDate.toISOString().split('T')[0];

        let dailyPrice = accommodationData.basePrice; // Default fallback

        // Check if we have pricing data for this date
        if (accommodationData.pricingCalendar && accommodationData.pricingCalendar[dateStr]) {
            dailyPrice = accommodationData.pricingCalendar[dateStr].price;
            console.log(`🔍 DEBUG: Date ${dateStr} price: ${dailyPrice} IRR`);
        } else {
            console.log(`🔍 DEBUG: Date ${dateStr} using base price: ${dailyPrice} IRR`);
        }

        accommodationCost += dailyPrice;
        priceBreakdownByDate.push({
            date: dateStr,
            price: dailyPrice
        });
    }

    totalCost += accommodationCost;
    priceDetails += `<div class="d-flex justify-content-between">
        <span>Accommodation (${nights} nights)</span>
        <span>${accommodationCost.toLocaleString()} IRR</span>
    </div>`;

    console.log('🔍 DEBUG: Base accommodation cost:', accommodationCost, 'IRR');

    // Calculate extra guest fees (guests beyond standard capacity)
    const totalGuests = getTotalGuests();
    const extraGuests = Math.max(0, totalGuests - accommodationData.standardCapacity);

    if (extraGuests > 0 && accommodationData.extraGuestFee > 0) {
        const extraGuestCost = extraGuests * accommodationData.extraGuestFee;  // One-time fee, not per night
        totalCost += extraGuestCost;
        priceDetails += `<div class="d-flex justify-content-between">
            <span>Extra guests (${extraGuests} guests)</span>
            <span>${extraGuestCost.toLocaleString()} IRR</span>
        </div>`;

        console.log('🔍 DEBUG: Extra guest fee:', extraGuestCost, 'IRR (one-time fee)');
    }

    // Calculate age-specific fees if capacity rules exist
    if (accommodationData.capacityRules && accommodationData.capacityRules.length > 0) {
        let ageSpecificCost = 0;

        accommodationData.capacityRules.forEach(rule => {
            let guestCountForCategory = 0;

            switch(rule.age_category) {
                case 'infant':
                    guestCountForCategory = guestCounts.infants;
                    break;
                case 'child':
                    guestCountForCategory = guestCounts.children;
                    break;
                case 'teen':
                    guestCountForCategory = guestCounts.teens;
                    break;
                case 'adult':
                    guestCountForCategory = guestCounts.adults;
                    break;
            }

            if (guestCountForCategory > 0 && rule.price_per_night > 0) {
                const categoryCost = guestCountForCategory * rule.price_per_night * nights;
                ageSpecificCost += categoryCost;

                priceDetails += `<div class="d-flex justify-content-between">
                    <span>${rule.age_category_display} (${guestCountForCategory} × ${nights} nights)</span>
                    <span>${categoryCost.toLocaleString()} IRR</span>
                </div>`;

                console.log(`🔍 DEBUG: ${rule.age_category} fee:`, categoryCost, 'IRR');
            }
        });

        totalCost += ageSpecificCost;
    }

    // Update display
    document.getElementById('priceDetails').innerHTML = priceDetails;
    document.getElementById('totalPrice').textContent = `${totalCost.toLocaleString()} IRR`;
    document.getElementById('priceBreakdown').style.display = 'block';

    console.log('🔍 DEBUG: Final total cost:', totalCost, 'IRR');

    updateBookingButton();
}

// Update booking button state
function updateBookingButton() {
    const checkIn = document.getElementById('checkIn').value;
    const checkOut = document.getElementById('checkOut').value;
    const bookingBtn = document.getElementById('bookingBtn');
    
    const isValid = checkIn && checkOut && getTotalGuests() > 0 && getTotalGuests() <= accommodationData.maxCapacity;
    bookingBtn.disabled = !isValid;
}
</script>
