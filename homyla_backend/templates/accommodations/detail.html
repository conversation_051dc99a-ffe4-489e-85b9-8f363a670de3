{% extends 'base.html' %}

{% block title %}{{ accommodation.name }} - NomadPersia{% endblock %}

{% block extra_css %}
<style>
.photo-gallery-preview .photo-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.photo-gallery-preview .photo-placeholder:hover {
    border-color: #3498db;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.photo-gallery-preview .photo-placeholder.main-photo {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
}

.photo-gallery-preview .photo-placeholder.main-photo:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
}

.photo-content {
    text-align: center;
    padding: 20px;
}

.photo-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.photo-placeholder p {
    margin: 0;
    font-weight: 500;
    color: #6c757d;
}

.photo-placeholder.main-photo p {
    color: rgba(255, 255, 255, 0.9);
}

.photo-placeholder.main-photo h4 {
    color: white;
    margin-bottom: 5px;
}

.photo-placeholder::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.photo-placeholder.main-photo::before {
    background: rgba(255, 255, 255, 0.2);
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-body">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h1 class="h2">{{ accommodation.name }}</h1>
                            <div class="mb-2">
                                <span class="badge bg-primary me-1">{{ accommodation.get_accommodation_type_display }}</span>
                                <span class="badge bg-info me-1">
                                    {{ accommodation.get_city_display }}{% if accommodation.town %}, {{ accommodation.town }}{% endif %}
                                </span>
                                {% if accommodation.star_rating %}
                                    <span class="badge bg-warning text-dark">
                                        {% for i in "12345"|slice:":"|slice:accommodation.star_rating %}⭐{% endfor %}
                                        {{ accommodation.star_rating }} Star{{ accommodation.star_rating|pluralize }}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        {% if is_host %}
                            <div>
                                <a href="{% url 'accommodations:edit' accommodation.pk %}" class="btn btn-outline-primary btn-sm">
                                    ✏️ Edit
                                </a>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Photo Gallery -->
                    {% if photos.main or photos.interior or photos.bedroom or photos.kitchen or photos.bathroom or photos.additional %}
                        <div class="mb-4">
                            <!-- Main Photo -->
                            {% if photos.main %}
                                <div class="mb-3">
                                    <img src="{{ MEDIA_URL }}{{ photos.main }}" alt="{{ accommodation.name }}" class="img-fluid rounded shadow" style="width: 100%; height: 400px; object-fit: cover;">
                                </div>
                            {% endif %}

                            <!-- Photo Gallery Grid -->
                            <div class="row">
                                {% if photos.interior %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-item">
                                            <img src="{{ MEDIA_URL }}{{ photos.interior }}" alt="Interior View" class="img-fluid rounded shadow" style="width: 100%; height: 200px; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Interior View</small>
                                        </div>
                                    </div>
                                {% endif %}

                                {% if photos.bedroom %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-item">
                                            <img src="{{ MEDIA_URL }}{{ photos.bedroom }}" alt="Bedroom" class="img-fluid rounded shadow" style="width: 100%; height: 200px; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Bedroom</small>
                                        </div>
                                    </div>
                                {% endif %}

                                {% if photos.kitchen %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-item">
                                            <img src="{{ MEDIA_URL }}{{ photos.kitchen }}" alt="Kitchen" class="img-fluid rounded shadow" style="width: 100%; height: 200px; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Kitchen</small>
                                        </div>
                                    </div>
                                {% endif %}

                                {% if photos.bathroom %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-item">
                                            <img src="{{ MEDIA_URL }}{{ photos.bathroom }}" alt="Bathroom" class="img-fluid rounded shadow" style="width: 100%; height: 200px; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Bathroom</small>
                                        </div>
                                    </div>
                                {% endif %}

                                {% for additional_photo in photos.additional %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-item">
                                            <img src="{{ MEDIA_URL }}{{ additional_photo }}" alt="Additional View" class="img-fluid rounded shadow" style="width: 100%; height: 200px; object-fit: cover;">
                                            <small class="text-muted d-block mt-1">Additional View</small>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% elif "Professional photo gallery" in accommodation.image_placeholder %}
                        <!-- Enhanced Photo Gallery Preview -->
                        <div class="mb-4">
                            <div class="photo-gallery-preview">
                                <!-- Main Photo Placeholder -->
                                <div class="mb-3">
                                    <div class="photo-placeholder main-photo" style="height: 400px;">
                                        <div class="photo-content">
                                            <div class="photo-icon">📸</div>
                                            <h4>{{ accommodation.name }}</h4>
                                            <p class="text-muted">Main Property Photo</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Photo Gallery Grid -->
                                <div class="row">
                                    {% if "interior photo" in accommodation.image_placeholder %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-placeholder" style="height: 200px;">
                                            <div class="photo-content">
                                                <div class="photo-icon">🏠</div>
                                                <p>Interior View</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if "bedroom photo" in accommodation.image_placeholder %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-placeholder" style="height: 200px;">
                                            <div class="photo-content">
                                                <div class="photo-icon">🛏️</div>
                                                <p>Bedroom</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if "kitchen photo" in accommodation.image_placeholder %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-placeholder" style="height: 200px;">
                                            <div class="photo-content">
                                                <div class="photo-icon">🍳</div>
                                                <p>Kitchen</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if "bathroom photo" in accommodation.image_placeholder %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-placeholder" style="height: 200px;">
                                            <div class="photo-content">
                                                <div class="photo-icon">🚿</div>
                                                <p>Bathroom</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if "additional photos" in accommodation.image_placeholder %}
                                    <div class="col-md-4 mb-3">
                                        <div class="photo-placeholder" style="height: 200px;">
                                            <div class="photo-content">
                                                <div class="photo-icon">📷</div>
                                                <p>Additional Views</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="alert alert-info mt-3">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">📸</div>
                                        <div>
                                            <strong>Photo Gallery Available</strong><br>
                                            <small>{{ accommodation.image_placeholder }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- Default Image Placeholder -->
                        <div class="bg-light rounded p-4 text-center mb-4" style="min-height: 300px;">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div>
                                    <div style="font-size: 4rem; margin-bottom: 1rem;">🏨</div>
                                    <p class="text-muted mb-0">{{ accommodation.image_placeholder }}</p>
                                    <small class="d-block text-muted mt-2">Photo gallery will be available soon</small>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Description -->
                    <div class="mb-4">
                        <h4>About This Property</h4>
                        <p class="text-muted">{{ accommodation.description|linebreaks }}</p>
                    </div>

                    <!-- Property Details -->
                    <div class="mb-4">
                        <h4>Property Details</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">👥</span>
                                    <div>
                                        <strong>Capacity:</strong>
                                        <span>{{ accommodation.standard_capacity }} guest{{ accommodation.standard_capacity|pluralize }}</span>
                                        {% if accommodation.max_capacity and accommodation.max_capacity != accommodation.standard_capacity %}
                                            <span class="text-muted">(up to {{ accommodation.max_capacity }})</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">🛏️</span>
                                    <div>
                                        <strong>Bedrooms:</strong>
                                        <span>{{ accommodation.bedrooms }} bedroom{{ accommodation.bedrooms|pluralize }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">🚿</span>
                                    <div>
                                        <strong>Bathrooms:</strong>
                                        <span>{{ accommodation.bathrooms }} bathroom{{ accommodation.bathrooms|pluralize }}</span>
                                    </div>
                                </div>
                            </div>
                            {% if accommodation.property_size %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">📐</span>
                                    <div>
                                        <strong>Size:</strong>
                                        <span>{{ accommodation.property_size }} m²</span>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Location & Contact Information -->
                    <div class="mb-4">
                        <h4>📍 Location & Contact</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <span class="text-primary me-2">🏠</span>
                                    <div>
                                        <strong>Address:</strong>
                                        <div class="text-muted">{{ accommodation.address|linebreaks }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">📞</span>
                                    <div>
                                        <strong>Reservation Phone:</strong>
                                        <div class="text-muted">
                                            <a href="tel:{{ accommodation.reservation_phone }}" class="text-decoration-none">
                                                {{ accommodation.reservation_phone }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% if accommodation.geolocation %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">🗺️</span>
                                    <div>
                                        <strong>GPS Coordinates:</strong>
                                        <div class="text-muted">{{ accommodation.geolocation }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Booking Policies -->
                    <div class="mb-4">
                        <h4>📋 Booking Information</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">⚡</span>
                                    <div>
                                        <strong>Booking:</strong>
                                        <span>
                                            {% if accommodation.instant_booking %}
                                                <span class="badge bg-success">Instant Booking</span>
                                            {% else %}
                                                <span class="badge bg-warning text-dark">Host Approval Required</span>
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">📋</span>
                                    <div>
                                        <strong>Cancellation:</strong>
                                        <span>{{ accommodation.get_cancellation_policy_display }}</span>
                                    </div>
                                </div>
                            </div>
                            {% if accommodation.check_in_time %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">🕐</span>
                                    <div>
                                        <strong>Check-in until:</strong>
                                        <span>{{ accommodation.check_in_time }}</span>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">🕐</span>
                                    <div>
                                        <strong>Check-out time:</strong>
                                        <span>{{ accommodation.get_check_out_time_display }}</span>
                                    </div>
                                </div>
                            </div>
                            {% if accommodation.extra_guest_fee > 0 %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="text-primary me-2">💰</span>
                                    <div>
                                        <strong>Extra Guest Fee:</strong>
                                        <span>{{ accommodation.extra_guest_fee|floatformat:0 }} IRR/night</span>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Amenities -->
                    {% if amenities_list %}
                    <div class="mb-4">
                        <h4>Amenities</h4>
                        <div class="row">
                            {% for amenity in amenities_list %}
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="d-flex align-items-center">
                                    <span class="text-success me-2">✓</span>
                                    <span>{{ amenity }}</span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Property Policies & Rules -->
                    <div class="mb-4">
                        <h4>🏠 Property Policies & Rules</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">🚬</span>
                                    <div>
                                        <strong>Smoking:</strong>
                                        <span class="{% if accommodation.smoking_allowed %}text-success{% else %}text-danger{% endif %}">
                                            {% if accommodation.smoking_allowed %}Allowed{% else %}Not Allowed{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">🐕</span>
                                    <div>
                                        <strong>Pets:</strong>
                                        <span class="{% if accommodation.pets_not_allowed %}text-danger{% else %}text-success{% endif %}">
                                            {% if accommodation.pets_not_allowed %}Not Allowed{% else %}Allowed{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">🎉</span>
                                    <div>
                                        <strong>Events/Parties:</strong>
                                        <span class="{% if accommodation.events_not_allowed %}text-danger{% else %}text-success{% endif %}">
                                            {% if accommodation.events_not_allowed %}Not Allowed{% else %}Allowed{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">🕐</span>
                                    <div>
                                        <strong>24-Hour Check-in:</strong>
                                        <span class="{% if accommodation.no_24hour_checkin %}text-danger{% else %}text-success{% endif %}">
                                            {% if accommodation.no_24hour_checkin %}Not Available{% else %}Available{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% if accommodation.same_gender_groups_only %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">👥</span>
                                    <div>
                                        <strong>Group Policy:</strong>
                                        <span class="text-info">Same Gender Groups Only</span>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            {% if accommodation.national_id_required %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">🆔</span>
                                    <div>
                                        <strong>ID Requirement:</strong>
                                        <span class="text-warning">National ID Required</span>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Additional Policies -->
                    {% if accommodation.additional_policies %}
                    <div class="mb-4">
                        <h4>House Rules & Additional Policies</h4>
                        <div class="bg-light p-3 rounded">
                            <div class="d-flex align-items-start">
                                <span class="text-primary me-2">📝</span>
                                <div>
                                    {{ accommodation.additional_policies|linebreaks }}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Host Information -->
                    <div class="border-top pt-4">
                        <h5>Hosted by {{ accommodation.host.get_full_name|default:accommodation.host.username }}</h5>
                        <p class="text-muted">
                            Member since {{ accommodation.host.date_joined|date:"F Y" }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Booking Sidebar -->
        <div class="col-lg-4">
            <div class="card sticky-top shadow" style="top: 2rem;">
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h3 class="text-primary">{{ accommodation.price_per_night|floatformat:0 }} IRR</h3>
                        <small class="text-muted">per night</small>
                    </div>

                    {% if can_book %}
                        <div class="d-grid mb-3">
                            <a href="{% url 'bookings:create' accommodation.pk %}" class="btn btn-primary btn-lg">
                                📅 Book Now
                            </a>
                        </div>
                        <p class="text-center text-muted small">
                            Submit a booking request - host will review
                        </p>
                    {% elif user.is_authenticated %}
                        {% if is_host %}
                            <div class="alert alert-info text-center">
                                <strong>This is your property</strong><br>
                                <small>You cannot book your own accommodation</small>
                            </div>
                        {% elif user.profile.is_host %}
                            <div class="alert alert-warning text-center">
                                <strong>Host Account</strong><br>
                                <small>Switch to a guest account to make bookings</small>
                            </div>
                        {% elif user.profile.is_admin_user %}
                            <div class="alert alert-info text-center">
                                <strong>Admin Account</strong><br>
                                <small>Admin accounts cannot make bookings</small>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="d-grid mb-3">
                            <a href="{% url 'accounts:login' %}?next={{ request.path }}" class="btn btn-outline-primary btn-lg">
                                Sign In to Book
                            </a>
                        </div>
                        <div class="text-center">
                            <small class="text-muted">
                                Don't have an account? 
                                <a href="{% url 'accounts:register' %}" class="text-decoration-none">Sign up</a>
                            </small>
                        </div>
                    {% endif %}

                    <!-- Property Details -->
                    <hr>
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Property Type:</span>
                            <span>{{ accommodation.get_accommodation_type_display }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Location:</span>
                            <span>{{ accommodation.get_city_display }}{% if accommodation.town %}, {{ accommodation.town }}{% endif %}</span>
                        </div>
                        {% if accommodation.star_rating %}
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Star Rating:</span>
                            <span>{{ accommodation.star_rating }} Star{{ accommodation.star_rating|pluralize }}</span>
                        </div>
                        {% endif %}
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Listed:</span>
                            <span>{{ accommodation.created_at|date:"M d, Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions for Host -->
            {% if is_host %}
            <div class="card mt-3 shadow">
                <div class="card-header">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'accommodations:edit' accommodation.pk %}" class="btn btn-outline-primary btn-sm">
                            ✏️ Edit Property
                        </a>
                        <a href="{% url 'accommodations:host_accommodations' %}" class="btn btn-outline-secondary btn-sm">
                            📋 My Properties
                        </a>
                        {% if accommodation.status == 'active' %}
                            <a href="{% url 'accommodations:toggle_status' accommodation.pk %}" 
                               class="btn btn-outline-warning btn-sm"
                               onclick="return confirm('Are you sure you want to deactivate this property?')">
                                ⏸️ Deactivate
                            </a>
                        {% elif accommodation.status == 'inactive' %}
                            <a href="{% url 'accommodations:toggle_status' accommodation.pk %}" 
                               class="btn btn-outline-success btn-sm">
                                ▶️ Activate
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Action Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 20px;">
                {% if can_book %}
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">📅 Ready to Book?</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="price-display mb-3">
                                <div class="h4 mb-0">{{ base_price|floatformat:0 }} IRR</div>
                                <small class="text-muted">per night</small>
                            </div>
                            <p class="text-muted mb-3">Complete your booking with detailed pricing and guest selection</p>
                            <a href="{% url 'bookings:create' accommodation.pk %}" class="btn btn-primary btn-lg w-100">
                                📝 Book Now
                            </a>
                            <small class="text-muted mt-2 d-block">
                                {% if accommodation.instant_booking %}
                                    ⚡ Instant booking available
                                {% else %}
                                    📝 Host approval required
                                {% endif %}
                            </small>
                        </div>
                    </div>
                {% elif not user.is_authenticated %}
                    <div class="card shadow-sm">
                        <div class="card-body text-center">
                            <h5>Ready to book?</h5>
                            <p class="text-muted">Sign in to start your booking</p>
                            <a href="{% url 'accounts:login' %}?next={{ request.path }}" class="btn btn-primary">
                                Sign In to Book
                            </a>
                        </div>
                    </div>
                {% elif is_host %}
                    <div class="card shadow-sm">
                        <div class="card-body text-center">
                            <h5>Your Property</h5>
                            <p class="text-muted">This is your accommodation listing</p>
                            <a href="{% url 'accommodations:edit' accommodation.pk %}" class="btn btn-outline-primary">
                                Edit Property
                            </a>
                        </div>
                    </div>
                {% else %}
                    <div class="card shadow-sm">
                        <div class="card-body text-center">
                            <h5>Contact Host</h5>
                            <p class="text-muted">Get in touch with the property owner</p>
                            <button class="btn btn-outline-primary" disabled>
                                Send Message
                            </button>
                        </div>
                    </div>
                {% endif %}

                <!-- Pricing Information Card -->
                <div class="card shadow-sm mt-3">
                    <div class="card-body">
                        <h6 class="card-title">💰 Pricing Information</h6>
                        <div class="pricing-info">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Base price:</span>
                                <span class="fw-bold">{{ base_price|floatformat:0 }} IRR/night</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Standard capacity:</span>
                                <span>{{ standard_capacity }} guests</span>
                            </div>
                            {% if extra_guest_fee > 0 %}
                            <div class="d-flex justify-content-between mb-2">
                                <span>Extra guest fee:</span>
                                <span>{{ extra_guest_fee|floatformat:0 }} IRR/night</span>
                            </div>
                            {% endif %}
                            <div class="d-flex justify-content-between">
                                <span>Maximum capacity:</span>
                                <span>{{ max_capacity }} guests</span>
                            </div>
                        </div>

                        {% if pricing_rules %}
                        <hr>
                        <h6>📅 Special Pricing</h6>
                        <div class="special-pricing">
                            {% for rule in pricing_rules|slice:":3" %}
                            <div class="d-flex justify-content-between small mb-1">
                                <span>{{ rule.get_pricing_type_display }}:</span>
                                <span class="fw-bold">{{ rule.price_per_night|floatformat:0 }} IRR</span>
                            </div>
                            {% endfor %}
                            {% if pricing_rules|length > 3 %}
                            <small class="text-muted">+ {{ pricing_rules|length|add:"-3" }} more pricing rules</small>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Host Information Card -->
                <div class="card shadow-sm mt-3">
                    <div class="card-body">
                        <h6 class="card-title">🏠 Host Information</h6>
                        <div class="d-flex align-items-center">
                            <div class="host-avatar me-3">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <span class="text-white fw-bold">{{ accommodation.host.first_name.0|default:accommodation.host.username.0|upper }}</span>
                                </div>
                            </div>
                            <div>
                                <div class="fw-bold">{{ accommodation.host.get_full_name|default:accommodation.host.username }}</div>
                                <small class="text-muted">Host since {{ accommodation.host.date_joined|date:"Y" }}</small>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="d-flex justify-content-between small mb-1">
                                <span>Response rate:</span>
                                <span>95%</span>
                            </div>
                            <div class="d-flex justify-content-between small">
                                <span>Response time:</span>
                                <span>Within 1 hour</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'accommodations:search' %}" class="text-decoration-none">
                            ← Back to Search
                        </a>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-star text-warning me-2"></i>Guest Reviews
                    </h4>
                    {% if accommodation.average_rating %}
                        <div class="d-flex align-items-center">
                            <span class="text-warning me-2 fs-5">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= accommodation.average_rating|floatformat:0 %}
                                        ★
                                    {% else %}
                                        ☆
                                    {% endif %}
                                {% endfor %}
                            </span>
                            <span class="fw-bold">{{ accommodation.average_rating|floatformat:1 }}</span>
                            <span class="text-muted ms-2">({{ accommodation.total_reviews }} نظر)</span>
                        </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if accommodation.total_reviews > 0 %}
                        <!-- Recent Reviews Preview -->
                        {% for review in accommodation.reviews.all|slice:":3" %}
                            <div class="border-bottom pb-3 mb-3">
                                <div class="row">
                                    <div class="col-md-2 text-center">
                                        <i class="fas fa-user-circle fa-2x text-muted"></i>
                                        <div class="small fw-bold mt-1">{{ review.guest.get_full_name|default:review.guest.username }}</div>
                                        <div class="small text-muted">{{ review.created_at|date:"F Y" }}</div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="text-warning me-2">
                                                {% for i in "12345" %}
                                                    {% if forloop.counter <= review.overall_rating %}
                                                        ★
                                                    {% else %}
                                                        ☆
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                            <small class="text-muted">{{ review.created_at|date:"j F Y" }}</small>
                                        </div>
                                        {% if review.review_text %}
                                            <p class="mb-0">{{ review.review_text|truncatewords:30 }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}

                        <div class="text-center">
                            <a href="{% url 'reviews:accommodation_reviews' accommodation.id %}" class="btn btn-outline-primary">
                                View All Reviews ({{ accommodation.total_reviews }})
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-star fa-3x text-muted mb-3"></i>
                            <h5>No Reviews Yet</h5>
                            <p class="text-muted">Be the first to share your experience about this accommodation.</p>
                        </div>
                    {% endif %}

                    <!-- Write Review Button -->
                    {% if user.is_authenticated and user.profile and user.profile.is_guest and can_review %}
                        <div class="text-center mt-3">
                            <a href="{% url 'reviews:create_review' accommodation.id %}" class="btn btn-primary">
                                <i class="fas fa-star me-2"></i>Write a Review
                            </a>
                        </div>
                    {% elif user.is_authenticated and user.profile and user.profile.is_guest %}
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                You can write a review after completing a booking at this accommodation.
                            </small>
                        </div>
                    {% elif user.is_authenticated %}
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                {% if user.profile %}
                                    Only guests can write reviews after completing bookings.
                                {% else %}
                                    Please complete your profile setup to write reviews.
                                {% endif %}
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center mt-3">
                            <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In to Write a Review
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 10px;
}

.sticky-top {
    z-index: 1020;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.badge {
    font-size: 0.75em;
}

.alert {
    border-radius: 8px;
}

/* Booking Widget Styles */
.booking-widget {
    border: 1px solid #e9ecef;
    border-radius: 12px;
}

.booking-widget .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.price-display {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.guest-counter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.guest-counter .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.price-breakdown {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.pricing-info .fw-bold {
    color: #2c3e50;
}

.host-avatar {
    flex-shrink: 0;
}

.special-pricing {
    max-height: 120px;
    overflow-y: auto;
}

@media (max-width: 991.98px) {
    .sticky-top {
        position: relative !important;
        top: auto !important;
    }
}
</style>
{% endblock %}
