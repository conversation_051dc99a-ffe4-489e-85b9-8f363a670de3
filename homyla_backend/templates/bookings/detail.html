{% extends 'base.html' %}

{% block title %}Booking Details - NomadPersia{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">Booking Details</h3>
                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'completed' %}primary{% else %}danger{% endif %}">
                        {{ booking.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <!-- Accommodation Info -->
                    <h4>{{ booking.accommodation.name }}</h4>
                    <p class="text-muted">{{ booking.accommodation.get_accommodation_type_display }} in {{ booking.accommodation.get_city_display }}{% if booking.accommodation.town %}, {{ booking.accommodation.town }}{% endif %}</p>
                    
                    <!-- Booking Details -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Check-in</h5>
                            <p>{{ booking.check_in_date|date:"M d, Y" }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Check-out</h5>
                            <p>{{ booking.check_out_date|date:"M d, Y" }}</p>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Guests</h5>
                            <p>{{ booking.number_of_guests }} person{{ booking.number_of_guests|pluralize }}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Total Price</h5>
                            <p>{{ booking.total_price }} IRR</p>
                        </div>
                    </div>
                    
                    {% if booking.special_requests %}
                    <div class="mb-3">
                        <h5>Special Requests</h5>
                        <p>{{ booking.special_requests }}</p>
                    </div>
                    {% endif %}
                    
                    <!-- Status Management -->
                    {% if form %}
                    <div class="mt-4">
                        <h5>Update Booking Status</h5>
                        <form method="post">
                            {% csrf_token %}
                            {{ form.as_p }}
                            <button type="submit" class="btn btn-primary">Update Status</button>
                        </form>
                    </div>
                    {% endif %}
                    
                    <!-- Cancellation -->
                    {% if can_cancel %}
                    <div class="mt-4">
                        <a href="{% url 'bookings:cancel' booking.pk %}" class="btn btn-danger">
                            Cancel Booking
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Booking Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Booking ID:</strong> {{ booking.id }}</p>
                    <p><strong>Created:</strong> {{ booking.created_at|date:"M d, Y g:i A" }}</p>
                    <p><strong>Duration:</strong> {{ booking.duration_nights }} night{{ booking.duration_nights|pluralize }}</p>
                    
                    {% if is_guest %}
                    <p><strong>Host:</strong> {{ booking.accommodation.host.get_full_name }}</p>
                    {% elif is_host or is_admin %}
                    <p><strong>Guest:</strong> {{ booking.guest.get_full_name }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <a href="{% url 'bookings:my_bookings' %}" class="btn btn-outline-primary">
                    Back to My Bookings
                </a>
                {% if is_host %}
                <a href="{% url 'bookings:host_bookings' %}" class="btn btn-outline-secondary">
                    Back to Host Bookings
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
