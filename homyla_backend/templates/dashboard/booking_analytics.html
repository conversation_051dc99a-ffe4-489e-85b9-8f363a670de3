{% extends 'base.html' %}
{% load static %}

{% block title %}Booking Analytics - NomadPersia Admin{% endblock %}

{% block extra_css %}
<style>
.analytics-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}
.analytics-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}
.stat-card.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.stat-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.stat-card.info {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-chart-line text-primary me-2"></i>Booking Analytics</h2>
            <p class="text-muted">Comprehensive booking statistics and insights</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'dashboard:admin' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Key Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ total_bookings|default:0 }}</h3>
                        <p class="mb-0">Total Bookings</p>
                    </div>
                    <div>
                        <i class="fas fa-calendar-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ confirmed_bookings|default:0 }}</h3>
                        <p class="mb-0">Confirmed</p>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ pending_bookings|default:0 }}</h3>
                        <p class="mb-0">Pending</p>
                    </div>
                    <div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ completed_bookings|default:0 }}</h3>
                        <p class="mb-0">Completed</p>
                    </div>
                    <div>
                        <i class="fas fa-flag-checkered fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Status Breakdown -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-pie-chart text-info me-2"></i>Booking Status Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt text-success me-2"></i>Monthly Bookings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings Table -->
    <div class="analytics-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list text-warning me-2"></i>Recent Bookings
            </h5>
        </div>
        <div class="card-body">
            {% if recent_bookings %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Booking ID</th>
                                <th>Guest</th>
                                <th>Accommodation</th>
                                <th>Check-in</th>
                                <th>Check-out</th>
                                <th>Status</th>
                                <th>Total Price</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for booking in recent_bookings %}
                            <tr>
                                <td><strong>#{{ booking.id }}</strong></td>
                                <td>{{ booking.guest.get_full_name|default:booking.guest.username }}</td>
                                <td>{{ booking.accommodation.name|truncatechars:30 }}</td>
                                <td>{{ booking.check_in_date|date:"M j, Y" }}</td>
                                <td>{{ booking.check_out_date|date:"M j, Y" }}</td>
                                <td>
                                    {% if booking.status == 'confirmed' %}
                                        <span class="badge bg-success">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'pending' %}
                                        <span class="badge bg-warning">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'cancelled' %}
                                        <span class="badge bg-danger">{{ booking.get_status_display }}</span>
                                    {% elif booking.status == 'completed' %}
                                        <span class="badge bg-info">{{ booking.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ booking.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>${{ booking.total_price|floatformat:0 }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5>No Bookings Found</h5>
                    <p class="text-muted">No booking data available to display.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Revenue Analytics -->
    <div class="row">
        <div class="col-md-12">
            <div class="analytics-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign text-success me-2"></i>Revenue Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="h4 text-success">${{ total_revenue|default:0|floatformat:0 }}</div>
                            <div class="text-muted">Total Revenue</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h4 text-info">${{ monthly_revenue|default:0|floatformat:0 }}</div>
                            <div class="text-muted">This Month</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h4 text-warning">${{ average_booking_value|default:0|floatformat:0 }}</div>
                            <div class="text-muted">Avg. Booking Value</div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="h4 text-primary">{{ occupancy_rate|default:0|floatformat:1 }}%</div>
                            <div class="text-muted">Occupancy Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Confirmed', 'Pending', 'Completed', 'Cancelled'],
            datasets: [{
                data: [
                    {{ confirmed_bookings|default:0 }},
                    {{ pending_bookings|default:0 }},
                    {{ completed_bookings|default:0 }},
                    {{ cancelled_bookings|default:0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#17a2b8',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Bookings Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Bookings',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>
{% endblock %}
