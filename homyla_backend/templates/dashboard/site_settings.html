{% extends 'base.html' %}
{% load static %}

{% block title %}Site Settings - NomadPersia Admin{% endblock %}

{% block extra_css %}
<style>
.settings-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}
.settings-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.settings-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
.form-switch {
    padding-left: 2.5em;
}
.form-switch .form-check-input {
    width: 2em;
    margin-left: -2.5em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280,0,0,0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    background-size: contain;
    border-radius: 2em;
    transition: background-position .15s ease-in-out;
}
.form-switch .form-check-input:checked {
    background-position: right center;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255,255,255,1.0%29'/%3e%3c/svg%3e");
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="fas fa-cogs text-primary me-2"></i>Site Settings</h2>
            <p class="text-muted">Manage site-wide configuration and settings</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'dashboard:admin' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Settings Form -->
    <form method="post">
        {% csrf_token %}
        
        <!-- Site Information Section -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>Site Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="site_name" class="form-label">Site Name</label>
                            <input type="text" class="form-control" id="site_name" name="site_name" 
                                   value="{{ settings.site_name|default:'NomadPersia' }}" required>
                            <div class="form-text">The name of your accommodation platform</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="contact_email" class="form-label">Contact Email</label>
                            <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                   value="{{ settings.contact_email|default:'<EMAIL>' }}" required>
                            <div class="form-text">Primary contact email for the site</div>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="site_description" class="form-label">Site Description</label>
                    <textarea class="form-control" id="site_description" name="site_description" rows="3">{{ settings.site_description|default:'Premium accommodation booking platform in Iran' }}</textarea>
                    <div class="form-text">Brief description of your platform (used in meta tags)</div>
                </div>
            </div>
        </div>

        <!-- Site Features Section -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-toggle-on text-success me-2"></i>Site Features
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="allow_registration"
                                   name="allow_registration" checked disabled>
                            <label class="form-check-label" for="allow_registration">
                                <strong>Allow User Registration</strong>
                                <div class="text-muted small">Enable new users to register on the platform</div>
                                <div class="text-warning small"><i class="fas fa-info-circle"></i> Feature coming soon</div>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="maintenance_mode"
                                   name="maintenance_mode" disabled>
                            <label class="form-check-label" for="maintenance_mode">
                                <strong>Maintenance Mode</strong>
                                <div class="text-muted small">Put the site in maintenance mode</div>
                                <div class="text-warning small"><i class="fas fa-info-circle"></i> Feature coming soon</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Platform Statistics Section -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar text-warning me-2"></i>Platform Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-primary">{{ total_users|default:0 }}</div>
                            <div class="text-muted">Total Users</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-success">{{ total_accommodations|default:0 }}</div>
                            <div class="text-muted">Accommodations</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-info">{{ total_bookings|default:0 }}</div>
                            <div class="text-muted">Total Bookings</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h3 text-warning">{{ total_reviews|default:0 }}</div>
                            <div class="text-muted">Reviews</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information Section -->
        <div class="settings-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server text-secondary me-2"></i>System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Platform Version:</strong></td>
                                <td>NomadPersia v1.0</td>
                            </tr>
                            <tr>
                                <td><strong>Django Version:</strong></td>
                                <td>{{ django_version }}</td>
                            </tr>
                            <tr>
                                <td><strong>Database:</strong></td>
                                <td>SQLite</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Last Updated:</strong></td>
                                <td>{{ settings.updated_at|date:"F j, Y g:i A"|default:"Never" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Created:</strong></td>
                                <td>{{ settings.created_at|date:"F j, Y"|default:"Unknown" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-success">Active</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
                <button type="reset" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
            <div>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Changes will take effect immediately
                </small>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation for maintenance mode
    const maintenanceToggle = document.getElementById('maintenance_mode');
    if (maintenanceToggle) {
        maintenanceToggle.addEventListener('change', function() {
            if (this.checked) {
                if (!confirm('Are you sure you want to enable maintenance mode? This will make the site unavailable to regular users.')) {
                    this.checked = false;
                }
            }
        });
    }
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const siteName = document.getElementById('site_name').value.trim();
        const contactEmail = document.getElementById('contact_email').value.trim();
        
        if (!siteName) {
            alert('Site name is required.');
            e.preventDefault();
            return;
        }
        
        if (!contactEmail) {
            alert('Contact email is required.');
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}
