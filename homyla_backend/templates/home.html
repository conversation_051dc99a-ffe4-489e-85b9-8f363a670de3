{% extends 'base.html' %}

{% block title %}Welcome to NomadPersia - Discover Iran's Hidden Gems{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <h1 class="display-3 fw-bold text-white mb-4">
                    Discover Iran's Hidden Gems
                </h1>
                <p class="lead text-white mb-4">
                    Experience authentic Persian hospitality in Tehran, Shiraz, and Mashhad. 
                    Find unique accommodations that tell the story of Iran's rich culture.
                </p>
                <div class="d-flex gap-3">
                    <a href="{% url 'accommodations:search' %}" class="btn btn-light btn-lg">
                        🔍 Explore Properties
                    </a>
                    {% if not user.is_authenticated %}
                    <a href="{% url 'accounts:register' %}" class="btn btn-outline-light btn-lg">
                        Join <PERSON>P<PERSON>
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <div class="bg-white bg-opacity-10 rounded-3 p-5 text-center">
                        <div style="font-size: 8rem; margin-bottom: 1rem;">🕌</div>
                        <h3 class="text-white">Persian Architecture</h3>
                        <p class="text-white-50">Experience the beauty of traditional Iranian design</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Search -->
<div class="container mt-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-lg">
                <div class="card-body p-4">
                    <h4 class="text-center mb-4">Find Your Perfect Stay</h4>
                    <form action="{% url 'accommodations:search' %}" method="get">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <select name="city" class="form-select">
                                    <option value="">Select City</option>
                                    <option value="tehran">Tehran</option>
                                    <option value="shiraz">Shiraz</option>
                                    <option value="mashhad">Mashhad</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <select name="accommodation_type" class="form-select">
                                    <option value="">Property Type</option>
                                    <option value="hotel">Hotel</option>
                                    <option value="villa">Villa</option>
                                    <option value="suite">Suite</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    🔍 Search
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Cities -->
<div class="container mt-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="fw-bold">Explore Iran's Cultural Capitals</h2>
            <p class="text-muted">Discover the unique charm of each destination</p>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="city-card">
                <div class="card h-100 shadow">
                    <div class="card-body text-center p-4">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🏛️</div>
                        <h4>Tehran</h4>
                        <p class="text-muted">
                            Iran's vibrant capital city, where modern life meets ancient traditions. 
                            Explore museums, bazaars, and contemporary culture.
                        </p>
                        <a href="{% url 'accommodations:search' %}?city=tehran" class="btn btn-outline-primary">
                            Explore Tehran
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="city-card">
                <div class="card h-100 shadow">
                    <div class="card-body text-center p-4">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🌹</div>
                        <h4>Shiraz</h4>
                        <p class="text-muted">
                            The city of poetry and gardens, home to Hafez and Saadi. 
                            Experience Persian literature and beautiful gardens.
                        </p>
                        <a href="{% url 'accommodations:search' %}?city=shiraz" class="btn btn-outline-primary">
                            Explore Shiraz
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 mb-4">
            <div class="city-card">
                <div class="card h-100 shadow">
                    <div class="card-body text-center p-4">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🕌</div>
                        <h4>Mashhad</h4>
                        <p class="text-muted">
                            Iran's spiritual heart and second-largest city. 
                            Visit the holy shrine and experience deep cultural traditions.
                        </p>
                        <a href="{% url 'accommodations:search' %}?city=mashhad" class="btn btn-outline-primary">
                            Explore Mashhad
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="bg-light py-5 mt-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">Why Choose NomadPersia?</h2>
                <p class="text-muted">Experience authentic Iranian hospitality</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">🏠</div>
                    <h5>Authentic Properties</h5>
                    <p class="text-muted">Stay in carefully selected accommodations that showcase Iranian culture and hospitality.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">🤝</div>
                    <h5>Local Hosts</h5>
                    <p class="text-muted">Connect with passionate local hosts who share their knowledge and love for Iran.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">🛡️</div>
                    <h5>Secure Booking</h5>
                    <p class="text-muted">Book with confidence through our secure platform with verified properties and hosts.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">💎</div>
                    <h5>Unique Experiences</h5>
                    <p class="text-muted">Discover hidden gems and experience Iran like a local, not a tourist.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action -->
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto text-center">
            <h2 class="fw-bold mb-4">Ready to Start Your Iranian Adventure?</h2>
            <p class="lead text-muted mb-4">
                Join thousands of travelers who have discovered the beauty of Iran through NomadPersia.
            </p>
            <div class="d-flex justify-content-center gap-3">
                {% if user.is_authenticated %}
                    {% if user.profile.is_guest %}
                        <a href="{% url 'accommodations:search' %}" class="btn btn-primary btn-lg">
                            🔍 Find Your Stay
                        </a>
                    {% elif user.profile.is_host %}
                        <a href="{% url 'accommodations:create' %}" class="btn btn-success btn-lg">
                            ➕ List Your Property
                        </a>
                        <a href="{% url 'dashboard:host_dashboard' %}" class="btn btn-outline-primary btn-lg">
                            📊 Host Dashboard
                        </a>
                    {% elif user.profile.is_admin_user %}
                        <a href="{% url 'dashboard:admin' %}" class="btn btn-primary btn-lg">
                            🛠️ Admin Dashboard
                        </a>
                    {% endif %}
                {% else %}
                    <a href="{% url 'accounts:register' %}" class="btn btn-primary btn-lg">
                        Join as Guest
                    </a>
                    <a href="{% url 'accounts:register' %}" class="btn btn-success btn-lg">
                        Become a Host
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.min-vh-75 {
    min-height: 75vh;
}

.city-card .card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.city-card .card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    line-height: 1;
}

.card {
    border-radius: 10px;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}
</style>
{% endblock %}
