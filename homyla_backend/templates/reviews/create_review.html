{% extends 'base.html' %}
{% load static %}

{% block title %}Write a Review - {{ accommodation.name }}{% endblock %}

{% block extra_css %}
<style>
.rating-input {
    display: flex;
    align-items: center;
    gap: 10px;
}
.star-rating-input {
    display: flex;
    gap: 5px;
}
.star-rating-input .star {
    font-size: 1.5em;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s ease;
}
.star-rating-input .star:hover,
.star-rating-input .star.active {
    color: #ffc107;
}
.review-form-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}
.booking-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}
.rating-category {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accommodations:search' %}">Accommodations</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accommodations:detail' accommodation.id %}">{{ accommodation.name }}</a></li>
            <li class="breadcrumb-item active">Write a Review</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <div class="review-form-card">
                <h2 class="mb-4">Write Your Review for This Accommodation</h2>

                <!-- Booking Information -->
                <div class="booking-info">
                    <h5><i class="fas fa-calendar-check text-primary me-2"></i>Your Booking Information</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Check-in Date:</strong> {{ booking.check_in_date|date:"F j, Y" }}</p>
                            <p><strong>Check-out Date:</strong> {{ booking.check_out_date|date:"F j, Y" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Number of Guests:</strong> {{ booking.total_guests }} guests</p>
                            <p><strong>Duration:</strong> {{ booking.duration_nights }} nights</p>
                        </div>
                    </div>
                </div>

                <form method="post" id="reviewForm">
                    {% csrf_token %}
                    
                    <!-- Overall Rating -->
                    <div class="rating-category">
                        <h5>Overall Rating</h5>
                        <p class="text-muted">How was your overall experience at this accommodation?</p>
                        <div class="rating-input">
                            <div class="star-rating-input" data-rating="overall_rating">
                                {% for i in "12345" %}
                                    <span class="star" data-value="{{ i }}">★</span>
                                {% endfor %}
                            </div>
                            <span class="rating-text ms-3"></span>
                        </div>
                        {{ form.overall_rating.as_hidden }}
                    </div>

                    <!-- Category Ratings -->
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Cleanliness -->
                            <div class="rating-category">
                                <h6>Cleanliness</h6>
                                <p class="text-muted small">How clean was the accommodation?</p>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="cleanliness_rating">
                                        {% for i in "12345" %}
                                            <span class="star" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.cleanliness_rating.as_hidden }}
                            </div>

                            <!-- Location -->
                            <div class="rating-category">
                                <h6>Location</h6>
                                <p class="text-muted small">How was the location of the accommodation?</p>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="location_rating">
                                        {% for i in "12345" %}
                                            <span class="star" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.location_rating.as_hidden }}
                            </div>

                            <!-- Value -->
                            <div class="rating-category">
                                <h6>Value for Money</h6>
                                <p class="text-muted small">Was it worth the money you paid?</p>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="value_rating">
                                        {% for i in "12345" %}
                                            <span class="star" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.value_rating.as_hidden }}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Communication -->
                            <div class="rating-category">
                                <h6>Host Communication</h6>
                                <p class="text-muted small">How was the communication with the host?</p>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="communication_rating">
                                        {% for i in "12345" %}
                                            <span class="star" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.communication_rating.as_hidden }}
                            </div>

                            <!-- Check-in -->
                            <div class="rating-category">
                                <h6>Check-in Experience</h6>
                                <p class="text-muted small">How was the check-in process?</p>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="checkin_rating">
                                        {% for i in "12345" %}
                                            <span class="star" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.checkin_rating.as_hidden }}
                            </div>

                            <!-- Accuracy -->
                            <div class="rating-category">
                                <h6>Accuracy of Description</h6>
                                <p class="text-muted small">Did the accommodation match the description?</p>
                                <div class="rating-input">
                                    <div class="star-rating-input" data-rating="accuracy_rating">
                                        {% for i in "12345" %}
                                            <span class="star" data-value="{{ i }}">★</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {{ form.accuracy_rating.as_hidden }}
                            </div>
                        </div>
                    </div>

                    <!-- Written Review -->
                    <div class="mb-4">
                        <label for="{{ form.review_text.id_for_label }}" class="form-label">
                            <h5>Written Review (Optional)</h5>
                        </label>
                        <p class="text-muted">Share your experience with other guests</p>
                        {{ form.review_text }}
                        {% if form.review_text.errors %}
                            <div class="text-danger">
                                {% for error in form.review_text.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <i class="fas fa-star me-2"></i>Submit Review
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Accommodation Summary -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ accommodation.name }}</h5>
                    <p class="text-muted">{{ accommodation.get_accommodation_type_display }} in {{ accommodation.get_city_display }}</p>

                    {% if accommodation.average_rating %}
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <span class="text-warning me-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= accommodation.average_rating|floatformat:0 %}
                                        ★
                                    {% else %}
                                        ☆
                                    {% endif %}
                                {% endfor %}
                            </span>
                            <span>{{ accommodation.average_rating|floatformat:1 }} ({{ accommodation.total_reviews }} reviews)</span>
                        </div>
                    </div>
                    {% endif %}
                    
                    <p class="text-muted small">
                        نظر شما به سایر مهمانان کمک می‌کند تا تصمیم بهتری بگیرند.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ratingInputs = document.querySelectorAll('.star-rating-input');
    const submitBtn = document.getElementById('submitBtn');
    const requiredRatings = ['overall_rating', 'cleanliness_rating', 'location_rating', 'value_rating', 'communication_rating', 'checkin_rating', 'accuracy_rating'];
    let ratings = {};

    // Rating texts
    const ratingTexts = {
        1: 'Poor',
        2: 'Fair',
        3: 'Good',
        4: 'Very Good',
        5: 'Excellent'
    };

    ratingInputs.forEach(function(ratingInput) {
        const ratingName = ratingInput.getAttribute('data-rating');
        const stars = ratingInput.querySelectorAll('.star');
        const hiddenInput = document.getElementById(`id_${ratingName}`);
        const ratingText = ratingInput.parentElement.querySelector('.rating-text');

        stars.forEach(function(star, index) {
            star.addEventListener('click', function() {
                const value = parseInt(star.getAttribute('data-value'));
                ratings[ratingName] = value;
                hiddenInput.value = value;

                // Update star display
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });

                // Update rating text
                if (ratingText) {
                    ratingText.textContent = ratingTexts[value];
                }

                // Check if all required ratings are filled
                checkFormCompletion();
            });

            star.addEventListener('mouseenter', function() {
                const value = parseInt(star.getAttribute('data-value'));
                stars.forEach(function(s, i) {
                    if (i < value) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });

        ratingInput.addEventListener('mouseleave', function() {
            const currentValue = ratings[ratingName] || 0;
            stars.forEach(function(s, i) {
                if (i < currentValue) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });

    function checkFormCompletion() {
        const allRated = requiredRatings.every(rating => ratings[rating] && ratings[rating] > 0);
        submitBtn.disabled = !allRated;
    }
});
</script>
{% endblock %}
