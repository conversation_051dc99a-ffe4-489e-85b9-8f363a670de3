{% extends 'base.html' %}
{% load static %}

{% block title %}حذف نظر - {{ accommodation.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accommodations:search' %}">Accommodations</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accommodations:detail' accommodation.id %}">{{ accommodation.name }}</a></li>
            <li class="breadcrumb-item active">Delete Review</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>حذف نظر
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>هشدار:</strong> آیا مطمئن هستید که می‌خواهید این نظر را حذف کنید؟ این عمل قابل بازگشت نیست.
                    </div>

                    <!-- Review to be deleted -->
                    <div class="border rounded p-3 mb-4" style="background-color: #f8f9fa;">
                        <h6>نظری که حذف خواهد شد:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-3">
                                <i class="fas fa-user-circle fa-2x text-muted"></i>
                            </div>
                            <div>
                                <div class="fw-bold">{{ review.guest.get_full_name|default:review.guest.username }}</div>
                                <div class="text-warning">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.overall_rating %}
                                            ★
                                        {% else %}
                                            ☆
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted">{{ review.created_at|date:"j F Y" }}</small>
                            </div>
                        </div>
                        
                        {% if review.review_text %}
                            <p class="mb-0">{{ review.review_text|linebreaks }}</p>
                        {% else %}
                            <p class="text-muted mb-0">نظر کتبی ندارد.</p>
                        {% endif %}

                        {% if review.host_response %}
                            <div class="mt-3 p-2 border-start border-primary" style="border-width: 3px !important; background-color: #f0f8ff;">
                                <small class="text-muted">پاسخ میزبان:</small>
                                <p class="mb-0">{{ review.host_response|linebreaks }}</p>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Accommodation Info -->
                    <div class="mb-4">
                        <h6>اقامتگاه:</h6>
                        <p class="text-muted">{{ accommodation.name }} - {{ accommodation.get_accommodation_type_display }} در {{ accommodation.get_city_display }}</p>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>انصراف
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>بله، حذف کن
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
