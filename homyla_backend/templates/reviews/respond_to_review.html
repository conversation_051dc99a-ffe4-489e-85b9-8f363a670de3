{% extends 'base.html' %}
{% load static %}

{% block title %}پاسخ به نظر - {{ accommodation.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'accommodations:search' %}">Accommodations</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accommodations:detail' accommodation.id %}">{{ accommodation.name }}</a></li>
            <li class="breadcrumb-item active">Respond to Review</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">پاسخ به نظر مهمان</h4>
                </div>
                <div class="card-body">
                    <!-- Original Review -->
                    <div class="border rounded p-3 mb-4" style="background-color: #f8f9fa;">
                        <h6>نظر مهمان:</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-3">
                                <i class="fas fa-user-circle fa-2x text-muted"></i>
                            </div>
                            <div>
                                <div class="fw-bold">{{ review.guest.get_full_name|default:review.guest.username }}</div>
                                <div class="text-warning">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.overall_rating %}
                                            ★
                                        {% else %}
                                            ☆
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted">{{ review.created_at|date:"j F Y" }}</small>
                            </div>
                        </div>
                        
                        {% if review.review_text %}
                            <p class="mb-0">{{ review.review_text|linebreaks }}</p>
                        {% else %}
                            <p class="text-muted mb-0">مهمان نظر کتبی ننوشته است.</p>
                        {% endif %}
                    </div>

                    <!-- Response Form -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.host_response.id_for_label }}" class="form-label">
                                <h5>پاسخ شما:</h5>
                            </label>
                            {{ form.host_response }}
                            {% if form.host_response.errors %}
                                <div class="text-danger">
                                    {% for error in form.host_response.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                پاسخ شما برای سایر مهمانان قابل مشاهده خواهد بود.
                            </div>
                        </div>

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'accommodations:detail' accommodation.id %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>انصراف
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-reply me-2"></i>ارسال پاسخ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Accommodation Summary -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ accommodation.name }}</h5>
                    <p class="text-muted">{{ accommodation.get_accommodation_type_display }} در {{ accommodation.get_city_display }}</p>
                    
                    {% if accommodation.average_rating %}
                    <div class="mb-3">
                        <div class="d-flex align-items-center">
                            <span class="text-warning me-2">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= accommodation.average_rating|floatformat:0 %}
                                        ★
                                    {% else %}
                                        ☆
                                    {% endif %}
                                {% endfor %}
                            </span>
                            <span>{{ accommodation.average_rating|floatformat:1 }} ({{ accommodation.total_reviews }} نظر)</span>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-2"></i>
                            پاسخ شما به نظر مهمان نشان می‌دهد که شما به بازخورد مهمانان اهمیت می‌دهید.
                        </small>
                    </div>
                </div>
            </div>

            <!-- Tips for Host Response -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">نکات مفید</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>از مهمان برای انتخاب اقامتگاه شما تشکر کنید</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>به نکات مثبت اشاره کنید</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>در صورت وجود مشکل، راه حل ارائه دهید</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>مهمان را برای بازگشت دعوت کنید</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
