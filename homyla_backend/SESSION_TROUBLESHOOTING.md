# Session Troubleshooting Guide

## Issue: "Your session has been terminated for security reasons"

### Root Cause
The `SessionSecurityMiddleware` was detecting IP address changes and treating them as potential session hijacking attempts.

### Solution Applied ✅
1. **Modified Session Security**: Updated middleware to be less strict in development mode
2. **Extended Session Duration**: Increased from 24 hours to 7 days for better UX
3. **IP Check Control**: Added setting to disable IP checking in development

### Current Settings
```python
# In nomadpersia/settings.py
SESSION_COOKIE_AGE = 604800  # 7 days
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # Allow sessions to persist
ENFORCE_SESSION_IP_CHECK = not DEBUG  # Only enforce in production
```

## Quick Fixes

### 1. Clear All Sessions (Nuclear Option)
```bash
cd /var/www/nomadpersia
python manage.py clear_sessions --confirm
```

### 2. Clear Only Expired Sessions
```bash
cd /var/www/nomadpersia
python manage.py clear_sessions --expired-only --confirm
```

### 3. Restart Django Server
```bash
# Kill current server and restart
python manage.py runserver 0.0.0.0:8082
```

## Common Causes & Solutions

### 1. IP Address Changes
**Cause**: Using VPN, mobile hotspot, or dynamic IP
**Solution**: ✅ Fixed - IP checking disabled in development

### 2. Browser Issues
**Cause**: Corrupted cookies or browser cache
**Solution**: 
- Clear browser cookies for localhost:8082
- Use incognito/private browsing mode
- Try different browser

### 3. Session Timeout
**Cause**: Long periods of inactivity
**Solution**: ✅ Fixed - Extended session duration to 7 days

### 4. CSRF Token Issues
**Cause**: Mismatched CSRF tokens
**Solution**: 
- Refresh the page
- Clear browser cache
- Check for JavaScript errors

## Prevention Tips

### For Development
1. **Use Consistent Network**: Avoid switching between WiFi/mobile
2. **Disable VPN**: Turn off VPN while testing
3. **Use Incognito Mode**: Prevents cookie conflicts
4. **Regular Logout**: Properly logout instead of closing browser

### For Production
1. **Enable IP Checking**: Set `ENFORCE_SESSION_IP_CHECK = True`
2. **Shorter Sessions**: Reduce `SESSION_COOKIE_AGE` for security
3. **Monitor Logs**: Check for suspicious session activity

## Debugging Session Issues

### 1. Check Current Session
```python
# In Django shell
python manage.py shell

from django.contrib.sessions.models import Session
from django.contrib.auth.models import User

# List all active sessions
for session in Session.objects.all():
    print(f"Session: {session.session_key}")
    print(f"Expires: {session.expire_date}")
    print(f"Data: {session.get_decoded()}")
    print("---")
```

### 2. Check User Sessions
```python
# Find sessions for specific user
user = User.objects.get(username='your_username')
sessions = Session.objects.filter(
    session_data__contains=f'"_auth_user_id":"{user.id}"'
)
print(f"User {user.username} has {sessions.count()} active sessions")
```

### 3. Monitor Session Activity
```bash
# Check Django logs for session-related messages
tail -f /var/log/django/debug.log | grep -i session
```

## Advanced Configuration

### Custom Session Settings
```python
# In settings.py - for production
SESSION_COOKIE_AGE = 3600  # 1 hour
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_SAVE_EVERY_REQUEST = True
ENFORCE_SESSION_IP_CHECK = True

# For development
SESSION_COOKIE_AGE = 604800  # 7 days
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
ENFORCE_SESSION_IP_CHECK = False
```

### Disable Session Security Temporarily
```python
# In settings.py - emergency fix
MIDDLEWARE = [
    # Comment out this line temporarily
    # 'accounts.middleware.SessionSecurityMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # ... other middleware
]
```

## Testing Session Fixes

### 1. Login Test
1. Login with test credentials
2. Navigate between pages for 5+ minutes
3. Verify no session termination messages

### 2. IP Change Simulation
1. Login normally
2. Change network (WiFi to mobile)
3. Continue using the site
4. Should work without termination in development

### 3. Long Session Test
1. Login and stay active
2. Leave browser open for several hours
3. Return and continue using
4. Session should persist

## Emergency Recovery

### If Completely Locked Out
1. **Clear all sessions**: `python manage.py clear_sessions --confirm`
2. **Restart server**: Kill and restart Django
3. **Clear browser data**: Delete all localhost cookies
4. **Use different browser**: Try Chrome/Firefox/Safari
5. **Check network**: Ensure stable internet connection

### If Issues Persist
1. **Disable middleware**: Comment out `SessionSecurityMiddleware`
2. **Check database**: Verify session table is accessible
3. **Review logs**: Look for error patterns
4. **Reset user password**: May clear session conflicts

## Status After Fix ✅

- ✅ **IP Checking**: Disabled in development mode
- ✅ **Session Duration**: Extended to 7 days
- ✅ **Browser Persistence**: Sessions survive browser close
- ✅ **Management Command**: Available to clear sessions
- ✅ **Flexible Configuration**: Easy to adjust for production

The session termination issue should now be resolved for development use while maintaining security for production deployment.
