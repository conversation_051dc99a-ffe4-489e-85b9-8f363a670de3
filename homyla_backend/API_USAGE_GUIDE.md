# NomadPersia API Usage Guide

## Overview
The NomadPersia API provides RESTful endpoints for managing accommodations and bookings. It supports JWT authentication, role-based permissions, and comprehensive filtering.

## Base URL
```
http://localhost:8082/api/
```

## Authentication

### 1. User Registration
```bash
curl -X POST http://localhost:8082/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "securepassword",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "phone_number": "09123456789",
    "is_host": true
  }'
```

### 2. User Login
```bash
curl -X POST http://localhost:8082/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "securepassword"
  }'
```

Response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 3. Using JWT Token
Include the access token in the Authorization header:
```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  http://localhost:8082/api/v1/accommodations/
```

## Accommodations API

### 1. List All Accommodations (Public)
```bash
curl http://localhost:8082/api/v1/accommodations/
```

### 2. Search Accommodations with Filters
```bash
curl "http://localhost:8082/api/v1/accommodations/search/?city=tehran&min_price=100000&max_capacity_min=4&instant_booking=true"
```

Available filters:
- `q` - Search query
- `city` - Filter by city (tehran, isfahan, shiraz, etc.)
- `accommodation_type` - villa_suite, apartment, cottage
- `min_price`, `max_price` - Price range
- `min_capacity` - Minimum guest capacity
- `instant_booking` - true/false
- `smoking_allowed` - true/false
- `pets_not_allowed` - true/false
- `bedrooms_min`, `bathrooms_min` - Room requirements

### 3. Get Accommodation Details
```bash
curl http://localhost:8082/api/v1/accommodations/12/
```

### 4. Check Availability
```bash
curl "http://localhost:8082/api/v1/accommodations/12/availability/?check_in=2025-08-01&check_out=2025-08-05"
```

### 5. Create Accommodation (Host/Admin only)
```bash
curl -X POST http://localhost:8082/api/v1/accommodations/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Beautiful Villa in Tehran",
    "description": "A stunning villa with modern amenities",
    "accommodation_type": "villa_suite",
    "city": "tehran",
    "town": "Zafaraniyeh",
    "address": "123 Main Street, Zafaraniyeh, Tehran",
    "reservation_phone": "+98 21 1234 5678",
    "geolocation": "35.6892,51.3890",
    "price_per_night": 250000,
    "star_rating": 5,
    "standard_capacity": 4,
    "max_capacity": 6,
    "bedrooms": 3,
    "bathrooms": 2,
    "property_size": 200,
    "extra_guest_fee": 50000,
    "instant_booking": true,
    "cancellation_policy": "flexible",
    "check_in_time": "15:00",
    "check_out_time": "12:00",
    "additional_policies": "Quiet hours: 10 PM - 8 AM. No smoking inside.",
    "smoking_allowed": false,
    "pets_not_allowed": true,
    "events_not_allowed": true,
    "national_id_required": true,
    "amenities": "WiFi, Air Conditioning, Kitchen, Parking"
  }'
```

## Bookings API

### 1. List User's Bookings
```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  http://localhost:8082/api/v1/bookings/
```

### 2. Create a Booking
```bash
curl -X POST http://localhost:8082/api/v1/bookings/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "accommodation_id": 12,
    "check_in_date": "2025-08-01",
    "check_out_date": "2025-08-05",
    "adults": 2,
    "teens": 1,
    "children": 0,
    "infants": 0,
    "special_requests": "Late check-in requested"
  }'
```

### 3. Get Booking Details
```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  http://localhost:8082/api/v1/bookings/1/
```

### 4. Cancel a Booking
```bash
curl -X POST -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  http://localhost:8082/api/v1/bookings/1/cancel/
```

### 5. Update Booking Status (Host/Admin only)
```bash
curl -X PUT http://localhost:8082/api/v1/bookings/1/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "confirmed"
  }'
```

## User Profile API

### 1. Get User Profile
```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  http://localhost:8082/api/auth/profile/
```

### 2. Update User Profile
```bash
curl -X PUT http://localhost:8082/api/auth/profile/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "09123456789",
    "role": "host"
  }'
```

## Error Handling

The API returns standard HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

Error response format:
```json
{
  "error": "Error message",
  "details": {
    "field_name": ["Field-specific error message"]
  }
}
```

## Rate Limiting
- Anonymous users: 100 requests/hour
- Authenticated users: 1000 requests/hour

## Pagination
List endpoints return paginated results:
```json
{
  "count": 25,
  "next": "http://localhost:8082/api/v1/accommodations/?page=2",
  "previous": null,
  "results": [...]
}
```

## Interactive Documentation
Visit the interactive API documentation at:
- Swagger UI: http://localhost:8082/api/
- ReDoc: http://localhost:8082/api/redoc/

## Support
For API support and questions, please refer to the interactive documentation or contact the development team.
