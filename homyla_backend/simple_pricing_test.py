#!/usr/bin/env python
"""
Simple test to check pricing system without git issues
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from accommodations.models import Accommodation, AccommodationPricing
from datetime import date, timedelta

def simple_test():
    print("=== SIMPLE PRICING TEST ===")
    
    # Get first accommodation
    accommodation = Accommodation.objects.first()
    if not accommodation:
        print("❌ No accommodations found")
        return
    
    print(f"🏠 Accommodation: {accommodation.name}")
    print(f"   Base price: {accommodation.price_per_night} IRR")
    
    # Check existing pricing rules
    pricing_rules = AccommodationPricing.objects.filter(accommodation=accommodation)
    print(f"   Existing pricing rules: {pricing_rules.count()}")
    
    for rule in pricing_rules:
        print(f"     - {rule.pricing_type}: {rule.start_date} to {rule.end_date} = {rule.price_per_night} IRR (Active: {rule.is_active})")
    
    # Test price for today
    today = date.today()
    price_today = accommodation.get_price_for_date(today)
    print(f"   Price for today ({today}): {price_today} IRR")
    
    # Test price calculation for a 3-night stay
    start_date = today + timedelta(days=1)
    end_date = start_date + timedelta(days=3)
    total_price = accommodation.calculate_total_price(start_date, end_date, 2)
    print(f"   Total for {start_date} to {end_date} (2 guests): {total_price} IRR")
    
    # Create a test pricing rule
    test_start = today + timedelta(days=5)
    test_end = today + timedelta(days=8)
    test_price = int(accommodation.price_per_night * 1.5)
    
    test_rule, created = AccommodationPricing.objects.get_or_create(
        accommodation=accommodation,
        pricing_type='holiday',
        start_date=test_start,
        end_date=test_end,
        defaults={
            'price_per_night': test_price,
            'description': 'Test Holiday Pricing',
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ Created test pricing rule: {test_start} to {test_end} = {test_price} IRR")
    else:
        print(f"📝 Test pricing rule already exists")
    
    # Test the new pricing
    test_date = test_start + timedelta(days=1)
    price_with_rule = accommodation.get_price_for_date(test_date)
    print(f"   Price for {test_date} (with rule): {price_with_rule} IRR")
    
    # Test total calculation with the rule
    total_with_rule = accommodation.calculate_total_price(test_start, test_end, 2)
    print(f"   Total for holiday period: {total_with_rule} IRR")
    
    expected_total = test_price * (test_end - test_start).days
    print(f"   Expected total: {expected_total} IRR")
    print(f"   ✅ Pricing working: {total_with_rule == expected_total}")

if __name__ == '__main__':
    simple_test()
