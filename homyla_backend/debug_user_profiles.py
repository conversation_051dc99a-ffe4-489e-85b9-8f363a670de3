#!/usr/bin/env python
"""
Debug script to check user profiles and review eligibility
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.auth import get_user_model
from accommodations.models import Accommodation
from bookings.models import Booking
from reviews.models import Review

User = get_user_model()

def debug_user_profiles():
    print("=== USER PROFILE DEBUG ===")
    
    # Get all users
    users = User.objects.all()
    
    for user in users:
        print(f"\n👤 User: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   First Name: {user.first_name}")
        print(f"   Last Name: {user.last_name}")
        
        # Check if user has profile
        if hasattr(user, 'profile'):
            profile = user.profile
            print(f"   ✅ Profile exists")
            print(f"   Role: {profile.role}")
            print(f"   Is Guest: {profile.is_guest()}")
            print(f"   Is Host: {profile.is_host()}")
            print(f"   Is Admin: {profile.is_admin_user()}")
        else:
            print(f"   ❌ No profile found")
        
        # Check bookings
        bookings = Booking.objects.filter(guest=user)
        print(f"   Bookings: {bookings.count()}")
        
        completed_bookings = bookings.filter(status='completed')
        print(f"   Completed Bookings: {completed_bookings.count()}")
        
        # Check reviews
        reviews = Review.objects.filter(guest=user)
        print(f"   Reviews Written: {reviews.count()}")
        
        # Check specific accommodation (ID 12)
        accommodation = Accommodation.objects.get(id=12)
        completed_booking_for_12 = bookings.filter(
            accommodation=accommodation,
            status='completed'
        ).exists()
        
        existing_review_for_12 = reviews.filter(
            accommodation=accommodation
        ).exists()
        
        can_review_12 = (
            hasattr(user, 'profile') and 
            user.profile.is_guest() and 
            completed_booking_for_12 and 
            not existing_review_for_12
        )
        
        print(f"   Can review Accommodation 12: {can_review_12}")
        print(f"     - Has completed booking for 12: {completed_booking_for_12}")
        print(f"     - Has existing review for 12: {existing_review_for_12}")

def create_guest_user_with_booking():
    print("\n=== CREATING TEST GUEST USER ===")
    
    # Create or get test guest user
    user, created = User.objects.get_or_create(
        username='test_guest_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Guest'
        }
    )
    
    if created:
        print(f"✅ Created new user: {user.username}")
    else:
        print(f"📝 Using existing user: {user.username}")
    
    # Ensure user has guest profile
    if hasattr(user, 'profile'):
        profile = user.profile
        if profile.role != 'guest':
            profile.role = 'guest'
            profile.save()
            print(f"✅ Updated user role to guest")
        else:
            print(f"✅ User already has guest role")
    else:
        from accounts.models import UserProfile
        profile = UserProfile.objects.create(
            user=user,
            role='guest'
        )
        print(f"✅ Created guest profile for user")
    
    # Create a completed booking for accommodation 12
    accommodation = Accommodation.objects.get(id=12)
    
    # Check if booking already exists
    existing_booking = Booking.objects.filter(
        guest=user,
        accommodation=accommodation,
        status='completed'
    ).first()
    
    if not existing_booking:
        from datetime import datetime, timedelta
        from django.utils import timezone
        
        # Create future booking first
        booking = Booking.objects.create(
            accommodation=accommodation,
            guest=user,
            check_in_date=timezone.now().date() + timedelta(days=60),
            check_out_date=timezone.now().date() + timedelta(days=63),
            adults=2,
            teens=0,
            children=0,
            infants=0,
            total_price=accommodation.price_per_night * 3,
            status='completed'  # Set as completed
        )
        print(f"✅ Created completed booking for {user.username}")
    else:
        print(f"📝 User already has completed booking")
    
    # Check if user already has review
    existing_review = Review.objects.filter(
        guest=user,
        accommodation=accommodation
    ).exists()
    
    print(f"📊 Final status for {user.username}:")
    print(f"   - Has guest profile: {hasattr(user, 'profile') and user.profile.is_guest()}")
    print(f"   - Has completed booking: {Booking.objects.filter(guest=user, accommodation=accommodation, status='completed').exists()}")
    print(f"   - Has existing review: {existing_review}")
    print(f"   - Can write review: {not existing_review}")

if __name__ == '__main__':
    debug_user_profiles()
    create_guest_user_with_booking()
    print("\n=== FINAL USER PROFILES ===")
    debug_user_profiles()
