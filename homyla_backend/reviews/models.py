"""
Review models for NomadPersia platform

This module contains models for the review and rating system,
allowing guests to review accommodations after completed bookings.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.db.models import Avg
from accommodations.models import Accommodation
from bookings.models import Booking

User = get_user_model()


class Review(models.Model):
    """
    Review model for accommodation reviews by guests.
    
    Only guests who have completed bookings can leave reviews.
    Includes overall rating and individual category ratings.
    """
    
    # Core relationships
    guest = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='reviews_written',
        verbose_name=_('Guest Reviewer'),
        help_text=_('The guest who wrote this review')
    )
    
    accommodation = models.ForeignKey(
        Accommodation,
        on_delete=models.CASCADE,
        related_name='reviews',
        verbose_name=_('Accommodation'),
        help_text=_('The accommodation being reviewed')
    )
    
    booking = models.OneToOneField(
        Booking,
        on_delete=models.CASCADE,
        related_name='review',
        verbose_name=_('Related Booking'),
        help_text=_('The completed booking this review is based on')
    )
    
    # Rating fields (1-5 stars)
    overall_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Overall Rating'),
        help_text=_('Overall rating from 1 to 5 stars')
    )
    
    cleanliness_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Cleanliness Rating'),
        help_text=_('Cleanliness rating from 1 to 5 stars')
    )
    
    location_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Location Rating'),
        help_text=_('Location rating from 1 to 5 stars')
    )
    
    value_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Value Rating'),
        help_text=_('Value for money rating from 1 to 5 stars')
    )
    
    communication_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Communication Rating'),
        help_text=_('Host communication rating from 1 to 5 stars')
    )
    
    checkin_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Check-in Rating'),
        help_text=_('Check-in experience rating from 1 to 5 stars')
    )
    
    accuracy_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name=_('Accuracy Rating'),
        help_text=_('Listing accuracy rating from 1 to 5 stars')
    )
    
    # Review content
    review_text = models.TextField(
        blank=True,
        verbose_name=_('Review Text'),
        help_text=_('Written review comments (optional)')
    )
    
    # Host response
    host_response = models.TextField(
        blank=True,
        verbose_name=_('Host Response'),
        help_text=_('Host response to the review (optional)')
    )
    
    host_response_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Host Response Date'),
        help_text=_('Date when host responded to the review')
    )
    
    # Verification and moderation
    is_verified = models.BooleanField(
        default=True,
        verbose_name=_('Verified Review'),
        help_text=_('Whether this review is from a verified booking')
    )
    
    is_published = models.BooleanField(
        default=True,
        verbose_name=_('Published'),
        help_text=_('Whether this review is visible to the public')
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )
    
    class Meta:
        verbose_name = _('Review')
        verbose_name_plural = _('Reviews')
        ordering = ['-created_at']
        unique_together = ['guest', 'accommodation']  # One review per guest per accommodation
        indexes = [
            models.Index(fields=['accommodation', '-created_at']),
            models.Index(fields=['guest', '-created_at']),
            models.Index(fields=['overall_rating']),
            models.Index(fields=['is_published', '-created_at']),
        ]
    
    def __str__(self):
        return f'Review by {self.guest.get_full_name() or self.guest.username} for {self.accommodation.name}'
    
    @property
    def average_category_rating(self):
        """Calculate average of all category ratings."""
        ratings = [
            self.cleanliness_rating,
            self.location_rating,
            self.value_rating,
            self.communication_rating,
            self.checkin_rating,
            self.accuracy_rating
        ]
        return sum(ratings) / len(ratings)
    
    @property
    def star_display(self):
        """Return star display for overall rating."""
        return '★' * self.overall_rating + '☆' * (5 - self.overall_rating)
    
    def save(self, *args, **kwargs):
        """Override save to ensure review is from completed booking."""
        if self.booking.status != 'completed':
            raise ValueError('Reviews can only be created for completed bookings')
        
        if self.booking.guest != self.guest:
            raise ValueError('Review guest must match booking guest')
        
        if self.booking.accommodation != self.accommodation:
            raise ValueError('Review accommodation must match booking accommodation')
        
        super().save(*args, **kwargs)
        
        # Update accommodation average rating
        self.accommodation.update_average_rating()


class ReviewHelpfulness(models.Model):
    """
    Model to track review helpfulness votes.
    
    Allows users to mark reviews as helpful or not helpful.
    """
    
    review = models.ForeignKey(
        Review,
        on_delete=models.CASCADE,
        related_name='helpfulness_votes',
        verbose_name=_('Review')
    )
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='review_votes',
        verbose_name=_('User')
    )
    
    is_helpful = models.BooleanField(
        verbose_name=_('Is Helpful'),
        help_text=_('Whether the user found this review helpful')
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    
    class Meta:
        verbose_name = _('Review Helpfulness')
        verbose_name_plural = _('Review Helpfulness Votes')
        unique_together = ['review', 'user']  # One vote per user per review
    
    def __str__(self):
        helpful_text = 'helpful' if self.is_helpful else 'not helpful'
        return f'{self.user.username} found review {helpful_text}'
