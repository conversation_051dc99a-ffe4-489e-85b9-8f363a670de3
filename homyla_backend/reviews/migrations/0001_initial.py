# Generated by Django 5.0.14 on 2025-07-21 13:30

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accommodations", "0008_accommodation_average_rating_and_more"),
        ("bookings", "0002_booking_adults_booking_children_booking_infants_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Review",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "overall_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Overall rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Overall Rating",
                    ),
                ),
                (
                    "cleanliness_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Cleanliness rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Cleanliness Rating",
                    ),
                ),
                (
                    "location_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Location rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Location Rating",
                    ),
                ),
                (
                    "value_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Value for money rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Value Rating",
                    ),
                ),
                (
                    "communication_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Host communication rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Communication Rating",
                    ),
                ),
                (
                    "checkin_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Check-in experience rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Check-in Rating",
                    ),
                ),
                (
                    "accuracy_rating",
                    models.PositiveSmallIntegerField(
                        help_text="Listing accuracy rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                        verbose_name="Accuracy Rating",
                    ),
                ),
                (
                    "review_text",
                    models.TextField(
                        blank=True,
                        help_text="Written review comments (optional)",
                        verbose_name="Review Text",
                    ),
                ),
                (
                    "host_response",
                    models.TextField(
                        blank=True,
                        help_text="Host response to the review (optional)",
                        verbose_name="Host Response",
                    ),
                ),
                (
                    "host_response_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date when host responded to the review",
                        null=True,
                        verbose_name="Host Response Date",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this review is from a verified booking",
                        verbose_name="Verified Review",
                    ),
                ),
                (
                    "is_published",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this review is visible to the public",
                        verbose_name="Published",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Updated At"),
                ),
                (
                    "accommodation",
                    models.ForeignKey(
                        help_text="The accommodation being reviewed",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to="accommodations.accommodation",
                        verbose_name="Accommodation",
                    ),
                ),
                (
                    "booking",
                    models.OneToOneField(
                        help_text="The completed booking this review is based on",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review",
                        to="bookings.booking",
                        verbose_name="Related Booking",
                    ),
                ),
                (
                    "guest",
                    models.ForeignKey(
                        help_text="The guest who wrote this review",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews_written",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Guest Reviewer",
                    ),
                ),
            ],
            options={
                "verbose_name": "Review",
                "verbose_name_plural": "Reviews",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ReviewHelpfulness",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "is_helpful",
                    models.BooleanField(
                        help_text="Whether the user found this review helpful",
                        verbose_name="Is Helpful",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "review",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="helpfulness_votes",
                        to="reviews.review",
                        verbose_name="Review",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review_votes",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="User",
                    ),
                ),
            ],
            options={
                "verbose_name": "Review Helpfulness",
                "verbose_name_plural": "Review Helpfulness Votes",
            },
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["accommodation", "-created_at"],
                name="reviews_rev_accommo_3d24cf_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["guest", "-created_at"], name="reviews_rev_guest_i_29d2b3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["overall_rating"], name="reviews_rev_overall_96630a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="review",
            index=models.Index(
                fields=["is_published", "-created_at"],
                name="reviews_rev_is_publ_8a1cd6_idx",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="review",
            unique_together={("guest", "accommodation")},
        ),
        migrations.AlterUniqueTogether(
            name="reviewhelpfulness",
            unique_together={("review", "user")},
        ),
    ]
