"""
Views for the review system

This module contains views for creating, displaying, and managing reviews.
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from accommodations.models import Accommodation
from bookings.models import Booking
from .models import Review, ReviewHelpfulness
from .forms import ReviewForm, HostResponseForm
from accounts.views import role_required


def accommodation_reviews(request, accommodation_id):
    """
    Display all reviews for a specific accommodation.
    """
    accommodation = get_object_or_404(Accommodation, id=accommodation_id)
    
    # Get published reviews
    reviews = Review.objects.filter(
        accommodation=accommodation,
        is_published=True
    ).select_related('guest', 'booking').order_by('-created_at')
    
    # Pagination
    paginator = Paginator(reviews, 10)  # 10 reviews per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Check if current user can write a review
    can_review = False
    if request.user.is_authenticated:
        # Check if user has completed booking and hasn't reviewed yet
        completed_booking = Booking.objects.filter(
            guest=request.user,
            accommodation=accommodation,
            status='completed'
        ).exists()
        
        existing_review = Review.objects.filter(
            guest=request.user,
            accommodation=accommodation
        ).exists()
        
        can_review = completed_booking and not existing_review
    
    context = {
        'accommodation': accommodation,
        'reviews': page_obj,
        'can_review': can_review,
        'total_reviews': accommodation.total_reviews,
        'average_rating': accommodation.average_rating,
    }
    
    return render(request, 'reviews/accommodation_reviews.html', context)


@login_required
@role_required(['guest'])
def create_review(request, accommodation_id):
    """
    Create a new review for an accommodation.
    """
    accommodation = get_object_or_404(Accommodation, id=accommodation_id)
    
    # Check if user is a guest
    if not (hasattr(request.user, 'profile') and request.user.profile.is_guest()):
        messages.error(request, _('Only guests can write reviews.'))
        return redirect('accommodations:detail', pk=accommodation_id)
    
    # Check if user has completed booking
    completed_booking = Booking.objects.filter(
        guest=request.user,
        accommodation=accommodation,
        status='completed'
    ).first()
    
    if not completed_booking:
        messages.error(request, _('You can only review accommodations where you have completed a booking.'))
        return redirect('accommodations:detail', pk=accommodation_id)
    
    # Check if user has already reviewed
    existing_review = Review.objects.filter(
        guest=request.user,
        accommodation=accommodation
    ).first()
    
    if existing_review:
        messages.info(request, _('You have already reviewed this accommodation.'))
        return redirect('accommodations:detail', pk=accommodation_id)
    
    if request.method == 'POST':
        form = ReviewForm(request.POST, user=request.user, accommodation=accommodation)
        if form.is_valid():
            review = form.save()
            messages.success(request, _('Your review has been submitted successfully!'))
            return redirect('accommodations:detail', pk=accommodation_id)
    else:
        form = ReviewForm(user=request.user, accommodation=accommodation)
    
    context = {
        'form': form,
        'accommodation': accommodation,
        'booking': completed_booking,
    }
    
    return render(request, 'reviews/create_review.html', context)


@login_required
@role_required(['guest'])
def edit_review(request, review_id):
    """
    Edit an existing review.
    """
    review = get_object_or_404(Review, id=review_id, guest=request.user)
    
    if request.method == 'POST':
        form = ReviewForm(
            request.POST, 
            instance=review,
            user=request.user, 
            accommodation=review.accommodation
        )
        if form.is_valid():
            form.save()
            messages.success(request, _('Your review has been updated successfully!'))
            return redirect('accommodations:detail', pk=review.accommodation.id)
    else:
        form = ReviewForm(
            instance=review,
            user=request.user, 
            accommodation=review.accommodation
        )
    
    context = {
        'form': form,
        'review': review,
        'accommodation': review.accommodation,
    }
    
    return render(request, 'reviews/edit_review.html', context)


@login_required
def respond_to_review(request, review_id):
    """
    Host response to a review.
    """
    review = get_object_or_404(Review, id=review_id)
    
    # Check if user is the host
    if review.accommodation.host != request.user:
        messages.error(request, _('Only the accommodation host can respond to reviews.'))
        return redirect('accommodations:detail', pk=review.accommodation.id)
    
    if request.method == 'POST':
        form = HostResponseForm(request.POST, instance=review, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, _('Your response has been added successfully!'))
            return redirect('accommodations:detail', pk=review.accommodation.id)
    else:
        form = HostResponseForm(instance=review, user=request.user)
    
    context = {
        'form': form,
        'review': review,
        'accommodation': review.accommodation,
    }
    
    return render(request, 'reviews/respond_to_review.html', context)


@login_required
@require_http_methods(["POST"])
def mark_helpful(request, review_id):
    """
    Mark a review as helpful or not helpful (AJAX endpoint).
    """
    review = get_object_or_404(Review, id=review_id)
    is_helpful = request.POST.get('is_helpful') == 'true'
    
    # Get or create helpfulness vote
    vote, created = ReviewHelpfulness.objects.get_or_create(
        review=review,
        user=request.user,
        defaults={'is_helpful': is_helpful}
    )
    
    if not created:
        # Update existing vote
        vote.is_helpful = is_helpful
        vote.save()
    
    # Get updated counts
    helpful_count = review.helpfulness_votes.filter(is_helpful=True).count()
    not_helpful_count = review.helpfulness_votes.filter(is_helpful=False).count()
    
    return JsonResponse({
        'success': True,
        'helpful_count': helpful_count,
        'not_helpful_count': not_helpful_count,
        'user_vote': is_helpful
    })


@login_required
def delete_review(request, review_id):
    """
    Delete a review (only by the review author or admin).
    """
    review = get_object_or_404(Review, id=review_id)
    
    # Check permissions
    if review.guest != request.user and not request.user.profile.is_admin_user():
        messages.error(request, _('You can only delete your own reviews.'))
        return redirect('accommodations:detail', pk=review.accommodation.id)
    
    if request.method == 'POST':
        accommodation_id = review.accommodation.id
        review.delete()
        messages.success(request, _('Review has been deleted successfully.'))
        return redirect('accommodations:detail', pk=accommodation_id)
    
    context = {
        'review': review,
        'accommodation': review.accommodation,
    }
    
    return render(request, 'reviews/delete_review.html', context)
