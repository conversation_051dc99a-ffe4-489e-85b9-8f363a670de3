"""
Forms for the review system

This module contains forms for creating and managing reviews.
"""

from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import Review
from bookings.models import Booking


class ReviewForm(forms.ModelForm):
    """
    Form for creating and editing accommodation reviews.
    """
    
    class Meta:
        model = Review
        fields = [
            'overall_rating',
            'cleanliness_rating',
            'location_rating',
            'value_rating',
            'communication_rating',
            'checkin_rating',
            'accuracy_rating',
            'review_text'
        ]
        
        widgets = {
            'overall_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'overall'
                }
            ),
            'cleanliness_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'cleanliness'
                }
            ),
            'location_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'location'
                }
            ),
            'value_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'value'
                }
            ),
            'communication_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'communication'
                }
            ),
            'checkin_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'checkin'
                }
            ),
            'accuracy_rating': forms.Select(
                choices=[(i, f'{i} ستاره') for i in range(1, 6)],
                attrs={
                    'class': 'form-select rating-select',
                    'data-rating': 'accuracy'
                }
            ),
            'review_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': _('Share your experience with this accommodation (optional)...'),
                'maxlength': 2000
            })
        }
        
        labels = {
            'overall_rating': _('Overall Rating'),
            'cleanliness_rating': _('Cleanliness'),
            'location_rating': _('Location'),
            'value_rating': _('Value for Money'),
            'communication_rating': _('Host Communication'),
            'checkin_rating': _('Check-in Experience'),
            'accuracy_rating': _('Listing Accuracy'),
            'review_text': _('Your Review')
        }
        
        help_texts = {
            'overall_rating': _('Rate your overall experience'),
            'cleanliness_rating': _('How clean was the accommodation?'),
            'location_rating': _('How was the location?'),
            'value_rating': _('Was it worth the price?'),
            'communication_rating': _('How was the host communication?'),
            'checkin_rating': _('How was the check-in process?'),
            'accuracy_rating': _('Did the listing match reality?'),
            'review_text': _('Optional: Share details about your stay')
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.accommodation = kwargs.pop('accommodation', None)
        super().__init__(*args, **kwargs)
        
        # Add CSS classes for styling
        for field_name, field in self.fields.items():
            if 'class' not in field.widget.attrs:
                field.widget.attrs['class'] = 'form-control'
    
    def clean(self):
        """Custom validation for the review form."""
        cleaned_data = super().clean()
        
        if not self.user or not self.accommodation:
            raise ValidationError(_('User and accommodation are required.'))
        
        # Check if user has a completed booking for this accommodation
        completed_booking = Booking.objects.filter(
            guest=self.user,
            accommodation=self.accommodation,
            status='completed'
        ).first()
        
        if not completed_booking:
            raise ValidationError(
                _('You can only review accommodations where you have completed a booking.')
            )
        
        # Check if user has already reviewed this accommodation
        existing_review = Review.objects.filter(
            guest=self.user,
            accommodation=self.accommodation
        ).first()
        
        if existing_review and not self.instance.pk:
            raise ValidationError(
                _('You have already reviewed this accommodation.')
            )
        
        return cleaned_data
    
    def save(self, commit=True):
        """Save the review with user and accommodation."""
        review = super().save(commit=False)
        
        if self.user and self.accommodation:
            review.guest = self.user
            review.accommodation = self.accommodation
            
            # Get the completed booking
            booking = Booking.objects.filter(
                guest=self.user,
                accommodation=self.accommodation,
                status='completed'
            ).first()
            
            if booking:
                review.booking = booking
        
        if commit:
            review.save()
        
        return review


class HostResponseForm(forms.ModelForm):
    """
    Form for hosts to respond to reviews.
    """
    
    class Meta:
        model = Review
        fields = ['host_response']
        
        widgets = {
            'host_response': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('Respond to this review (optional)...'),
                'maxlength': 1000
            })
        }
        
        labels = {
            'host_response': _('Your Response')
        }
        
        help_texts = {
            'host_response': _('Optional: Respond to the guest review')
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
    
    def clean(self):
        """Custom validation for host response."""
        cleaned_data = super().clean()
        
        if not self.user:
            raise ValidationError(_('User is required.'))
        
        # Check if user is the host of the accommodation
        if self.instance and self.instance.accommodation.host != self.user:
            raise ValidationError(_('Only the accommodation host can respond to reviews.'))
        
        return cleaned_data
    
    def save(self, commit=True):
        """Save the host response with timestamp."""
        review = super().save(commit=False)
        
        if review.host_response and not review.host_response_date:
            from django.utils import timezone
            review.host_response_date = timezone.now()
        
        if commit:
            review.save()
        
        return review
