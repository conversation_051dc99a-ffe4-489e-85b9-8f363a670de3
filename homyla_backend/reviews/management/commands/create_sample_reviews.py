"""
Management command to create sample reviews for testing

This command creates sample reviews for accommodations with completed bookings.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from accommodations.models import Accommodation
from bookings.models import Booking
from reviews.models import Review

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample reviews for testing the review system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of sample reviews to create'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        self.stdout.write('Creating sample reviews...')
        
        # Get accommodations and users (filter for valid accommodation types)
        accommodations = list(Accommodation.objects.filter(
            status='active',
            accommodation_type__in=['villa_suite', 'apartment', 'cottage']
        ))
        users = list(User.objects.filter(profile__role='guest'))
        
        if not accommodations:
            self.stdout.write(
                self.style.ERROR('No active accommodations found. Please create some accommodations first.')
            )
            return
        
        if not users:
            self.stdout.write(
                self.style.ERROR('No guest users found. Please create some guest users first.')
            )
            return
        
        created_count = 0
        
        # Sample review texts
        review_texts = [
            "اقامتگاه بسیار تمیز و مرتب بود. میزبان بسیار مهربان و پاسخگو بود. قطعا دوباره رزرو خواهم کرد.",
            "موقعیت عالی و دسترسی آسان به مراکز خرید. امکانات کامل و مطابق توضیحات.",
            "تجربه فوق‌العاده‌ای داشتیم. اقامتگاه دقیقا مطابق عکس‌ها بود و حتی بهتر.",
            "میزبان بسیار مهربان بود و راهنمایی‌های مفیدی ارائه داد. اقامتگاه تمیز و مجهز.",
            "قیمت مناسب و کیفیت عالی. پیشنهاد می‌کنم به دوستان.",
            "اقامتگاه در محیطی آرام و دنج قرار دارد. برای استراحت عالی است.",
            "امکانات کامل و مدرن. آشپزخانه مجهز و اتاق‌های راحت.",
            "تجربه خوبی بود اما انتظار بیشتری داشتم. در کل راضی هستم.",
            "میزبان سریع پاسخ می‌داد و مشکلات را حل می‌کرد. خدمات عالی.",
            "موقعیت مکانی فوق‌العاده و نزدیک به جاهای دیدنی. پیشنهاد می‌کنم."
        ]
        
        host_responses = [
            "از اقامت شما خوشحالیم. امیدواریم دوباره میهمان ما باشید.",
            "متشکریم از نظر مثبت شما. سعی می‌کنیم همیشه بهترین خدمات را ارائه دهیم.",
            "خوشحالیم که تجربه خوبی داشتید. منتظر بازگشت شما هستیم.",
            "سپاس از انتخاب اقامتگاه ما. نظرات شما برای ما ارزشمند است.",
            "ممنون از وقتی که گذاشتید و نظر خود را نوشتید.",
        ]
        
        for i in range(count):
            try:
                # Select random accommodation and user
                accommodation = random.choice(accommodations)
                user = random.choice(users)
                
                # Check if user already has a review for this accommodation
                if Review.objects.filter(guest=user, accommodation=accommodation).exists():
                    continue
                
                # Create a completed booking first (bypass validation by using bulk_create or direct SQL)
                check_in = timezone.now().date() - timedelta(days=random.randint(7, 60))
                check_out = check_in + timedelta(days=random.randint(1, 7))

                # Create booking without validation
                booking = Booking(
                    accommodation=accommodation,
                    guest=user,
                    check_in_date=check_in,
                    check_out_date=check_out,
                    adults=random.randint(1, 4),
                    teens=random.randint(0, 2),
                    children=random.randint(0, 2),
                    infants=random.randint(0, 1),
                    total_price=accommodation.price_per_night * (check_out - check_in).days,
                    status='completed'
                )
                booking.save()
                
                # Create review
                overall_rating = random.randint(3, 5)  # Mostly positive reviews
                
                review = Review.objects.create(
                    guest=user,
                    accommodation=accommodation,
                    booking=booking,
                    overall_rating=overall_rating,
                    cleanliness_rating=random.randint(max(1, overall_rating-1), 5),
                    location_rating=random.randint(max(1, overall_rating-1), 5),
                    value_rating=random.randint(max(1, overall_rating-1), 5),
                    communication_rating=random.randint(max(1, overall_rating-1), 5),
                    checkin_rating=random.randint(max(1, overall_rating-1), 5),
                    accuracy_rating=random.randint(max(1, overall_rating-1), 5),
                    review_text=random.choice(review_texts),
                    is_verified=True,
                    is_published=True
                )
                
                # Sometimes add host response
                if random.choice([True, False]):
                    review.host_response = random.choice(host_responses)
                    review.host_response_date = timezone.now()
                    review.save()
                
                created_count += 1
                self.stdout.write(f'Created review {created_count}: {user.username} -> {accommodation.name}')
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'Failed to create review {i+1}: {str(e)}')
                )
                continue
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} sample reviews!')
        )
        
        # Update accommodation ratings
        self.stdout.write('Updating accommodation average ratings...')
        for accommodation in Accommodation.objects.filter(
            status='active',
            accommodation_type__in=['villa_suite', 'apartment', 'cottage']
        ):
            try:
                accommodation.update_average_rating()
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'Failed to update rating for {accommodation.name}: {str(e)}')
                )
        
        self.stdout.write(
            self.style.SUCCESS('Sample review creation completed!')
        )
