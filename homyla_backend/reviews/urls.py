"""
URL configuration for reviews app

This module defines URL patterns for review-related views.
"""

from django.urls import path
from . import views

app_name = 'reviews'

urlpatterns = [
    # Review listing and display
    path('accommodation/<int:accommodation_id>/', views.accommodation_reviews, name='accommodation_reviews'),
    
    # Review creation and editing
    path('create/<int:accommodation_id>/', views.create_review, name='create_review'),
    path('edit/<int:review_id>/', views.edit_review, name='edit_review'),
    path('delete/<int:review_id>/', views.delete_review, name='delete_review'),
    
    # Host responses
    path('respond/<int:review_id>/', views.respond_to_review, name='respond_to_review'),
    
    # AJAX endpoints
    path('helpful/<int:review_id>/', views.mark_helpful, name='mark_helpful'),
]
