#!/usr/bin/env python3
"""
Quick session fix script for NomadPersia.
Run this if you're experiencing session termination issues.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nomadpersia.settings')
django.setup()

from django.contrib.sessions.models import Session
from django.utils import timezone
from django.contrib.auth.models import User

def clear_all_sessions():
    """Clear all user sessions."""
    count = Session.objects.count()
    Session.objects.all().delete()
    print(f"✅ Cleared {count} sessions. All users have been logged out.")

def clear_expired_sessions():
    """Clear only expired sessions."""
    expired = Session.objects.filter(expire_date__lt=timezone.now())
    count = expired.count()
    expired.delete()
    print(f"✅ Cleared {count} expired sessions.")

def show_session_info():
    """Show current session information."""
    total_sessions = Session.objects.count()
    expired_sessions = Session.objects.filter(expire_date__lt=timezone.now()).count()
    active_sessions = total_sessions - expired_sessions
    
    print(f"📊 Session Statistics:")
    print(f"   Total sessions: {total_sessions}")
    print(f"   Active sessions: {active_sessions}")
    print(f"   Expired sessions: {expired_sessions}")
    
    if total_sessions > 0:
        print(f"\n👥 Recent sessions:")
        for session in Session.objects.order_by('-expire_date')[:5]:
            data = session.get_decoded()
            user_id = data.get('_auth_user_id')
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    username = user.username
                except User.DoesNotExist:
                    username = f"Unknown (ID: {user_id})"
            else:
                username = "Anonymous"
            
            status = "Active" if session.expire_date > timezone.now() else "Expired"
            print(f"   - {username}: {status} (expires: {session.expire_date})")

def main():
    print("🔧 NomadPersia Session Fix Tool")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Show session information")
        print("2. Clear expired sessions only")
        print("3. Clear ALL sessions (logs out all users)")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == '1':
            show_session_info()
        elif choice == '2':
            clear_expired_sessions()
        elif choice == '3':
            confirm = input("⚠️  This will log out ALL users. Continue? (y/N): ")
            if confirm.lower() in ['y', 'yes']:
                clear_all_sessions()
            else:
                print("❌ Operation cancelled.")
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid option. Please try again.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("💡 Make sure you're running this from the project directory.")
        sys.exit(1)
