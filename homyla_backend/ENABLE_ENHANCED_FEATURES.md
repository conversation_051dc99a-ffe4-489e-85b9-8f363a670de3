# Enable Enhanced Features for NomadPersia

## Current Status
✅ **Basic Platform**: Fully functional with updated property categories  
⏳ **Enhanced Features**: Ready to enable (requires database migration)

## What's Working Now
- ✅ Updated property categories (Villa & Suite, Apartment, Cottage)
- ✅ Photo gallery interface (UI ready)
- ✅ Basic accommodation creation and management
- ✅ All existing booking functionality
- ✅ Host and admin dashboards

## Enhanced Features Ready to Enable

### 🏠 Capacity Management
- **Standard Capacity**: Base number of guests included in price
- **Maximum Capacity**: Optional higher limit with extra fees
- **Bedrooms & Bathrooms**: Detailed room information
- **Property Size**: Square meters specification

### 💰 Enhanced Pricing
- **Extra Guest Fees**: Per-person charges above standard capacity
- **Transparent Pricing**: Clear breakdown for guests

### 📋 Booking Policies
- **Instant Booking**: Optional host approval bypass
- **Cancellation Policies**: Flexible, Moderate, or Strict
- **Check-in Times**: Latest arrival time specification

## How to Enable Enhanced Features

### Option 1: Automatic Migration (Recommended)
```bash
# Navigate to project directory
cd /var/www/nomadpersia

# Run the enhanced features migration
python migrate_enhanced_features.py

# Follow the prompts to complete migration
```

### Option 2: Manual Django Migration
```bash
# Navigate to project directory
cd /var/www/nomadpersia

# Activate virtual environment
source .venv/bin/activate

# Create and run migrations
python manage.py makemigrations accommodations
python manage.py migrate accommodations
```

### Option 3: Manual Database Updates
If automatic migration fails, you can manually add the columns:

```sql
-- Add capacity management columns
ALTER TABLE accommodations_accommodation ADD COLUMN standard_capacity INTEGER DEFAULT 2 NOT NULL;
ALTER TABLE accommodations_accommodation ADD COLUMN max_capacity INTEGER NULL;
ALTER TABLE accommodations_accommodation ADD COLUMN bedrooms INTEGER DEFAULT 1 NOT NULL;
ALTER TABLE accommodations_accommodation ADD COLUMN bathrooms INTEGER DEFAULT 1 NOT NULL;
ALTER TABLE accommodations_accommodation ADD COLUMN property_size INTEGER NULL;

-- Add enhanced pricing
ALTER TABLE accommodations_accommodation ADD COLUMN extra_guest_fee DECIMAL(10,2) DEFAULT 0 NOT NULL;

-- Add booking policies
ALTER TABLE accommodations_accommodation ADD COLUMN instant_booking BOOLEAN DEFAULT 0 NOT NULL;
ALTER TABLE accommodations_accommodation ADD COLUMN cancellation_policy VARCHAR(10) DEFAULT 'moderate' NOT NULL;
ALTER TABLE accommodations_accommodation ADD COLUMN check_in_time VARCHAR(5) DEFAULT '' NOT NULL;

-- Update accommodation types
UPDATE accommodations_accommodation SET accommodation_type = 'villa_suite' WHERE accommodation_type IN ('hotel', 'villa', 'suite');
```

## After Migration: Enable Model Fields

### Step 1: Uncomment Enhanced Fields
Edit `accommodations/models.py` and uncomment all the enhanced field definitions:

```python
# Uncomment these sections:
# - Capacity and Layout fields
# - Enhanced Pricing fields  
# - Booking Policies fields
# - Related methods and validation
```

### Step 2: Update Templates
The templates are already prepared with enhanced features. After migration, they will automatically show the new fields.

### Step 3: Restart Server
```bash
# Restart the Django development server
python manage.py runserver 0.0.0.0:8082
```

## Testing Enhanced Features

### Create Enhanced Villa Listing
1. Go to `/accommodations/host/create/`
2. Select "Villa & Suite" category
3. Upload photos in gallery
4. Set capacity (e.g., 5 standard, 10 maximum)
5. Configure instant booking
6. Select comprehensive amenities
7. Set extra guest fees

### Test Guest Experience
1. Browse enhanced property listings
2. View detailed capacity and pricing
3. Experience booking flow with transparent pricing
4. See instant booking confirmation

### Verify Host Management
1. Manage enhanced property details
2. Control booking confirmation settings
3. Monitor capacity and pricing analytics

## Troubleshooting

### Migration Fails
- Check database permissions
- Ensure Django environment is properly set up
- Try manual SQL approach

### Fields Not Showing
- Verify migration completed successfully
- Check that model fields are uncommented
- Restart Django server

### Template Errors
- Ensure all enhanced fields exist in database
- Check for typos in field names
- Verify form field references

## Support

### Documentation
- `ENHANCED_FEATURES.md`: Comprehensive feature documentation
- `migrate_enhanced_features.py`: Automatic migration script
- Django admin: Database inspection and management

### Test Data
After migration, you can create test properties with:
- **Villa Example**: 5 standard + 5 extra capacity
- **Apartment Example**: 2 standard + 2 extra capacity  
- **Cottage Example**: 4 standard capacity only

## Benefits After Enhancement

### For Hosts
- Professional property listing interface
- Flexible capacity and pricing control
- Instant booking options
- Comprehensive amenity management

### For Guests
- Detailed property information
- Transparent pricing breakdown
- Rich photo galleries
- Clear booking policies

### For Platform
- Competitive feature set
- Enhanced user experience
- Better property categorization
- Professional marketplace appearance

---

**Ready to transform NomadPersia into a world-class accommodation platform? Run the migration and unlock the enhanced features! 🚀**
